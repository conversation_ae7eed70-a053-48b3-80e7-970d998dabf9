package cn.ijiami.detection.rest.controller;

import cn.ijiami.base.common.log.OperateLog;
import cn.ijiami.detection.VO.ChannelMonitoring;
import cn.ijiami.detection.VO.ChannelMonitoringVO;
import cn.ijiami.detection.VO.ChannelNotificationVO;
import cn.ijiami.detection.enums.ChannelStatusEnum;
import cn.ijiami.detection.query.ChannelMonitoringQuery;
import cn.ijiami.detection.query.ChannelNotificationQuery;
import cn.ijiami.detection.service.api.IChannelMonitoringService;
import cn.ijiami.detection.service.api.IChannelNotificationService;
import cn.ijiami.framework.common.response.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@Slf4j
@RequestMapping(value = "/api/channel/notification")
@Api(value = "/api/channel/notification",tags = "渠道通报模块")
public class ChannelNotificationController {
    @Autowired
    private IChannelNotificationService channelNotificationService;

    @Autowired
    private IChannelMonitoringService channelMonitoringService;

    @ApiOperation(value = "获取渠道通报列表",notes = "获取渠道通报列表")
    @PostMapping(value = "/findChannelList")
    @OperateLog(
            moduleName ="渠道通报模块",
            operateName = "查询列表"
    )
    public BaseResponse<ChannelNotificationVO> findChannelList(@RequestBody ChannelNotificationQuery query){
        BaseResponse<ChannelNotificationVO> response = new BaseResponse<>();
        response.setData(channelNotificationService.findChannelByQuery(query));
        return response;
    }

    @ApiOperation(value = "获取渠道风险检测列表",notes = "调用大数据平台获取")
    @PostMapping(value = "/findChannelMonitoring")
    public BaseResponse<ChannelMonitoringVO> findChannelMonitoring(@RequestBody ChannelMonitoringQuery query){
        BaseResponse<ChannelMonitoringVO> response = new BaseResponse<>();
        response.setData(channelMonitoringService.findMonitoringData(query));
        return response;
    }

    @ApiOperation(value = "获取刷新后的渠道风险检测列表",notes = "调用大数据平台的刷新接口")
    @PostMapping(value = "/getChannelFlushData")
    public BaseResponse<ChannelStatusEnum> getChannelFlushData(@RequestBody ChannelMonitoringQuery query){
        BaseResponse<ChannelStatusEnum> response = new BaseResponse<>();
        response.setData(channelMonitoringService.getChannelFlushData(query));
        return response;
    }
}
