package cn.ijiami.detection.rest.controller;

import static cn.ijiami.detection.constant.IdbMsgFieldName.TERMINAL_TYPE;
import static cn.ijiami.detection.constant.PinfoConstant.TRACE_ID;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.Objects;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.ijiami.detection.message.MessageSendKit;
import cn.ijiami.detection.server.client.base.enums.TerminalTypeEnum;
import cn.ijiami.detection.service.api.*;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.websocket.idb.ExecutorServiceHelper;
import cn.ijiami.framework.common.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.simp.SimpMessageSendingOperations;
import org.springframework.messaging.simp.annotation.SendToUser;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.ijiami.detection.mapper.TActionNougatMapper;
import cn.ijiami.detection.mapper.TPrivacyOutsideAddressMapper;
import cn.ijiami.detection.mapper.TPrivacySensitiveWordMapper;
import cn.ijiami.detection.mapper.TSensitiveWordMapper;
import cn.ijiami.detection.service.api.IosIdbDetectionService;
import cn.ijiami.detection.utils.IpUtil;
import io.swagger.annotations.ApiOperation;
import net.sf.json.JSONObject;


/**
 * <AUTHOR>
 * @date 2020/6/16
 */
@Slf4j
@RestController
public class StompController {
    @Autowired
    private SimpMessageSendingOperations simpMessageSendingOperations;

    @Autowired
    private IosIdbDetectionService idbDynamicDetectionService;

    @Autowired
    private AndroidIdbDetectionService androidIdbDetectionService;

    @Autowired
    private AppletIdbDetectionService appletIdbDetectionService;

    @Autowired
    private HarmonyIdbDetectionService harmonyIdbDetectionService;

    @Autowired
    private IDynamicWechatAppletDetectionService dynamicAppletDetectionService;

    @Autowired
    private IpUtil ipUtil;

    @Autowired
    private TSensitiveWordMapper tSensitiveWordMapper;

    @Autowired
    private TPrivacySensitiveWordMapper tPrivacySensitiveWordMapper;

    @Autowired
    private TPrivacyOutsideAddressMapper tPrivacyOutsideAddressMapper;

    @Autowired
    private TActionNougatMapper tActionNougatMapper;

    @Autowired
    private IPrivacyLogCrtlService iPrivacyLogCrtlService;

    @Autowired
    private ExecutorServiceHelper executorServiceHelper;

    @Autowired
    private MessageSendKit messageSendKit;

    /**
     * 表示服务端可以接收客户端通过主题“/app/hello”发送过来的消息，客户端需要在主题"/topic/hello"上监听并接收服务端发回的消息
     */
    @MessageMapping("/WebSocket")
    public void greeting(String message) throws Exception {
        if (StringUtils.equals(message, "ping")) {
            return;
        }
        executeLog(message);
    }

    private void executeLog(String message) {
        MDC.put(TRACE_ID, CommonUtil.genTraceId());
        try {
            log.info("executeLog msg:{}", message);
            JSONObject msgObj = JSONObject.fromObject(message);
            TerminalTypeEnum terminalType = TerminalTypeEnum.getItem(msgObj.optInt(TERMINAL_TYPE, TerminalTypeEnum.IOS.getValue()));
            if (Objects.isNull(terminalType)) {
                return;
            }
            messageSendKit.receiveIdbMessage(terminalType, message);
        } catch (Exception e) {
            log.info("日志解析错误");
        } finally {
            MDC.remove(TRACE_ID);
        }
    }

    /**
     * 这里用的是@SendToUser，这就是发送给单一客户端的标志。本例中，
     * 客户端接收一对一消息的主题应该是“/user/” + 用户Id + “/message” ,这里的用户id可以是一个普通的字符串，只要每个用户端都使用自己的id并且服务端知道每个用户的id就行。
     *
     * @return
     */
    @MessageMapping("/message")
    @SendToUser("/message")
    public String handleSubscribe() {
        System.out.println("this is the @SubscribeMapping('/marco')");
        return "I am a msg from SubscribeMapping('/macro').";
    }

    @ApiOperation(value = "测试接口回调", notes = "测试接口回调")
    @PostMapping(value = "/test/send_message")
    public BaseResponse<Object> sendMessage(String result, HttpServletResponse response,
                                            HttpServletRequest request) throws Exception {
        BaseResponse<Object> respones = new BaseResponse<Object>();
        System.out.println("测试接口回调=" + result);
        String bodyContent = ReadAsChars(request);
        System.out.println("body：" + bodyContent);
        respones.setData("success");
        return respones;
    }
	
	public static String ReadAsChars(HttpServletRequest request)
    {
 
        BufferedReader br = null;
        StringBuilder sb = new StringBuilder("");
        try
        {
            br = request.getReader();
            String str;
            while ((str = br.readLine()) != null)
            {
                sb.append(str);
            }
            br.close();
        }
        catch (IOException e)
        {
            e.getMessage();
        }
        finally
        {
            if (null != br)
            {
                try
                {
                    br.close();
                }
                catch (IOException e)
                {
                    e.getMessage();
                }
            }
        }
        return sb.toString();
    }
}
