package cn.ijiami.detection.rest.controller;

import static cn.ijiami.detection.utils.CommonUtil.reportZipName;

import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import cn.ijiami.detection.VO.compliance.ExpertTestingTaskVO;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.github.pagehelper.PageInfo;

import cn.ijiami.base.common.data.DataScope;
import cn.ijiami.base.common.log.OperateLog;
import cn.ijiami.base.common.user.IUser;
import cn.ijiami.detection.DTO.ImageUploadDTO;
import cn.ijiami.detection.DTO.LawDetectionDTO;
import cn.ijiami.detection.DTO.TaskChangeStatusDTO;
import cn.ijiami.detection.VO.AndroidSensorLog;
import cn.ijiami.detection.VO.BehaviorPermissionVo;
import cn.ijiami.detection.VO.CheckStartTaskVO;
import cn.ijiami.detection.VO.CheckUserTaskConditionVO;
import cn.ijiami.detection.VO.CountLawDetectResult;
import cn.ijiami.detection.VO.CountOutsideTypeVO;
import cn.ijiami.detection.VO.CountSensitiveNameVO;
import cn.ijiami.detection.VO.CountSensitiveTypeVO;
import cn.ijiami.detection.VO.CountSharedPrefsNameVO;
import cn.ijiami.detection.VO.CountSharedPrefsTypeVO;
import cn.ijiami.detection.VO.IosRemoteToolConfig;
import cn.ijiami.detection.VO.LawActionDetailVO;
import cn.ijiami.detection.VO.LawDetectDetailVO;
import cn.ijiami.detection.VO.PersonalInfoRiskDetailVO;
import cn.ijiami.detection.VO.PersonalMessageAndTypeVO;
import cn.ijiami.detection.VO.PrivacyActionVO;
import cn.ijiami.detection.VO.PrivacyDetectionUploadFileVO;
import cn.ijiami.detection.VO.PrivacyLawsVO;
import cn.ijiami.detection.VO.RealTimeBehaviorLog;
import cn.ijiami.detection.VO.RealTimeNetLog;
import cn.ijiami.detection.VO.ReportResultVO;
import cn.ijiami.detection.VO.RestartTaskVO;
import cn.ijiami.detection.VO.SafeDetailVO;
import cn.ijiami.detection.VO.SensitiveTypeVO;
import cn.ijiami.detection.VO.StoragePersonalMessageAndTypeVO;
import cn.ijiami.detection.VO.TPrivacyActionNougatVO;
import cn.ijiami.detection.VO.TPrivacyOutsideAddressVO;
import cn.ijiami.detection.VO.TPrivacySensitiveWordVO;
import cn.ijiami.detection.VO.TPrivacySharedPrefsVO;
import cn.ijiami.detection.VO.TaskConditionCheckResult;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.VO.TaskVO;
import cn.ijiami.detection.VO.detection.BaseMessageVO;
import cn.ijiami.detection.VO.detection.SdkResponseVO;
import cn.ijiami.detection.VO.detection.SdkVO;
import cn.ijiami.detection.VO.detection.privacy.ActionComplianceVO;
import cn.ijiami.detection.VO.detection.privacy.ComplianceVO;
import cn.ijiami.detection.VO.detection.privacy.IOSRealTimeLog;
import cn.ijiami.detection.VO.detection.privacy.PrivacyPolicyTypeVO;
import cn.ijiami.detection.aspect.RequestLimit;
import cn.ijiami.detection.config.IjiamiCommonProperties;
import cn.ijiami.detection.controller.DetectionBaseController;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.entity.TDeepShakeValue;
import cn.ijiami.detection.entity.THardwareSensorShakeValue;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacyCheck;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.entity.TPrivacyPolicyResult;
import cn.ijiami.detection.entity.TPrivacyPolicyType;
import cn.ijiami.detection.entity.TPrivacyResultMark;
import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import cn.ijiami.detection.entity.TPrivacySharedPrefs;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.server.client.base.enums.DynamicDeviceTypeEnum;
import cn.ijiami.detection.enums.LawResultTypeEnum;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.server.client.base.enums.TerminalTypeEnum;
import cn.ijiami.detection.helper.ResponseHelper;
import cn.ijiami.detection.mapper.TAssetsMapper;
import cn.ijiami.detection.query.BehaviorQuery;
import cn.ijiami.detection.query.DeepDetectionFinish;
import cn.ijiami.detection.query.ExcelReportQuery;
import cn.ijiami.detection.query.HardwareSensorShakeValueQuery;
import cn.ijiami.detection.query.LawActionDetailQuery;
import cn.ijiami.detection.query.LawItemResultDetailQuery;
import cn.ijiami.detection.query.ReviewFinish;
import cn.ijiami.detection.query.ShakeValueQuery;
import cn.ijiami.detection.query.task.IosLogQuery;
import cn.ijiami.detection.query.task.TaskCreateQuery;
import cn.ijiami.detection.query.task.TaskLogClearQuery;
import cn.ijiami.detection.query.task.TaskLogQuery;
import cn.ijiami.detection.query.task.TaskProgressQuery;
import cn.ijiami.detection.query.task.TaskQuery;
import cn.ijiami.detection.service.api.DetectionConfigService;
import cn.ijiami.detection.service.api.IExcelReportService;
import cn.ijiami.detection.service.api.IGetActionDetailDataService;
import cn.ijiami.detection.service.api.ILawDetectionService;
import cn.ijiami.detection.service.api.IMiitDetectService;
import cn.ijiami.detection.service.api.IPrivacyActionNougatService;
import cn.ijiami.detection.service.api.IPrivacyActionService;
import cn.ijiami.detection.service.api.IPrivacyCheckService;
import cn.ijiami.detection.service.api.IPrivacyDetectionService;
import cn.ijiami.detection.service.api.IPrivacyDetectionTransferRiskService;
import cn.ijiami.detection.service.api.IPrivacyOutsideAddressService;
import cn.ijiami.detection.service.api.IPrivacyPolicyImgService;
import cn.ijiami.detection.service.api.IPrivacySensitiveWordService;
import cn.ijiami.detection.service.api.IPrivacySharedPrefsService;
import cn.ijiami.detection.service.api.ISendMessageService;
import cn.ijiami.detection.service.api.ISensitiveTypeService;
import cn.ijiami.detection.service.api.ITaskReportService;
import cn.ijiami.detection.service.api.ITaskService;
import cn.ijiami.detection.utils.Base64DecodedMultipartFile;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.ConstantsUtils;
import cn.ijiami.detection.utils.HttpUtils;
import cn.ijiami.detection.utils.StfUtils;
import cn.ijiami.framework.common.enums.HiddenEnum;
import cn.ijiami.framework.common.enums.HttpStatusEnum;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.common.exception.IjiamiCommandException;
import cn.ijiami.framework.common.response.BaseResponse;
import cn.ijiami.framework.kit.utils.UuidUtil;
import cn.ijiami.manager.user.entity.User;
import cn.ijiami.manager.user.service.api.IUserService;
import cn.ijiami.message.enums.MessageNotificationEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * <AUTHOR>
 * @description 隐私检测模块
 * @date 2019/3/23
 */
@RestController
@RequestMapping("/api/privacy/")
@Api(value = "/api/privacy", tags = "隐私检测模块")
public class PrivacyDetectionController extends DetectionBaseController {

    private static final Logger LOG = LoggerFactory.getLogger(PrivacyDetectionController.class);
    @Value("${ijiami.logCrtl.config.path}")
    private              String logCrtlConfigPath;

    @Resource
    private       IMiitDetectService                   miitDetectService;
    private final IPrivacyDetectionService             iPrivacyDetectionService;
    private final IjiamiCommonProperties               commonProperties;
    private final IPrivacyDetectionTransferRiskService iPrivacyDetectionTransferRiskService;
    private final IUserService                         userServiceImpl;
    private final ISendMessageService                  sendMessageServiceImpl;
    private final ITaskService                         taskService;
    private final IPrivacyCheckService                 privacyCheckService;
    private final ISensitiveTypeService                sensitiveTypeService;
    private final IPrivacyActionService                privacyActionService;
    private final IPrivacyActionNougatService          privacyActionNougatService;
    private final IPrivacySensitiveWordService         privacySensitiveWordService;
    private final IPrivacySharedPrefsService           privacySharedPrefsService;
    private final IPrivacyOutsideAddressService        privacyOutsideAddressService;
    private final IPrivacyCheckService                 iPrivacyCheckService;
    private final IPrivacyActionNougatService          iPrivacyActionNougatService;
    private final TAssetsMapper                        tAssetsMapper;
    private final ILawDetectionService                 lawDetectionService;
    private final IPrivacyPolicyImgService             privacyPolicyImgServiceImpl;
    private final ITaskReportService                   taskReportService;
    private final IGetActionDetailDataService          getActionDetailDataService;
    private final IExcelReportService                  excelReportService;

    private final DetectionConfigService               detectionConfigService;


    public PrivacyDetectionController(IPrivacyDetectionService iPrivacyDetectionService, IjiamiCommonProperties commonProperties,
                                      IPrivacyDetectionTransferRiskService iPrivacyDetectionTransferRiskService, IUserService userServiceImpl,
                                      ISendMessageService sendMessageServiceImpl, ITaskService taskService, IPrivacyCheckService privacyCheckService,
                                      ISensitiveTypeService sensitiveTypeService, IPrivacyActionService privacyActionService,
                                      IPrivacyActionNougatService privacyActionNougatService, IPrivacySensitiveWordService privacySensitiveWordService,
                                      IPrivacySharedPrefsService privacySharedPrefsService, IPrivacyOutsideAddressService privacyOutsideAddressService,
                                      IPrivacyCheckService iPrivacyCheckService1, IPrivacyActionNougatService iPrivacyActionNougatService,
                                      TAssetsMapper tAssetsMapper, ILawDetectionService lawDetectionService,
                                      IPrivacyPolicyImgService privacyPolicyImgServiceImpl, ITaskReportService taskReportService, 
                                      IGetActionDetailDataService getActionDetailDataService,IExcelReportService excelReportService,
                                      DetectionConfigService detectionConfigService) {
        this.iPrivacyDetectionService = iPrivacyDetectionService;
        this.commonProperties = commonProperties;
        this.iPrivacyDetectionTransferRiskService = iPrivacyDetectionTransferRiskService;
        this.userServiceImpl = userServiceImpl;
        this.sendMessageServiceImpl = sendMessageServiceImpl;
        this.taskService = taskService;
        this.privacyCheckService = privacyCheckService;
        this.sensitiveTypeService = sensitiveTypeService;
        this.privacyActionService = privacyActionService;
        this.privacyActionNougatService = privacyActionNougatService;
        this.privacySensitiveWordService = privacySensitiveWordService;
        this.privacySharedPrefsService = privacySharedPrefsService;
        this.privacyOutsideAddressService = privacyOutsideAddressService;
        this.iPrivacyCheckService = iPrivacyCheckService1;
        this.iPrivacyActionNougatService = iPrivacyActionNougatService;
        this.tAssetsMapper = tAssetsMapper;
        this.lawDetectionService = lawDetectionService;
        this.privacyPolicyImgServiceImpl = privacyPolicyImgServiceImpl;
        this.taskReportService = taskReportService;
        this.getActionDetailDataService = getActionDetailDataService;
        this.excelReportService = excelReportService;
        this.detectionConfigService = detectionConfigService;
    }

    /**
     * 获取检测任务列表
     *
     * @return BaseResponse<TaskVO>
     */
    @RequestLimit(limit = 3, timeMillis = 1000)
    @ApiOperation(value = "获取检测任务列表", notes = "获取检测任务列表")
    @PostMapping("/findTaskByPage")
    @DataScope("taskList")
    public BaseResponse<TaskVO> findTaskByPage(@RequestBody TaskQuery taskQuery) {
        BaseResponse<TaskVO> response = new BaseResponse<>();
        if (!isAdmin()) {
            taskQuery.setUserId(getCurrentUser().getUserId());
        }
        response.setData(taskService.findTaskByPage(taskQuery));
        response.setMessage("查询成功!");
        return response;
    }

    @ApiOperation(value = "获取设备Iframe地址", notes = "获取设备Iframe地址")
    @PostMapping("/getDeviceIframeUrl")
    public BaseResponse<String> getDeviceIframeUrl(@RequestParam("deviceSerial") String deviceSerial, @RequestParam("taskId") Long taskId) {
        BaseResponse<String> response = new BaseResponse<>();
        TTask task = taskService.findById(taskId);
        response.setData(StfUtils.getDeviceIframeUrl(task, commonProperties, deviceSerial));
        response.setMessage("查询成功!");
        return response;
    }

    /**
     * 启动任务
     *
     * @return
     */
    @RequestLimit
    @ApiOperation(value = "启动任务", notes = "启动任务,批量启动传多个资产id即可")
    @PostMapping("/startTask")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "启动检测任务"
    )
    public BaseResponse<List<Long>> startTask(@RequestBody TaskCreateQuery taskCreateQuery) throws IjiamiCommandException {
        BaseResponse<List<Long>> response = new BaseResponse<>();
        try {
            IUser currentUser = getCurrentUser();
            taskCreateQuery.setUserId(currentUser.getUserId());
            List<Long> taskIds = new ArrayList<>();
            for (Long assetsId : taskCreateQuery.getAssetsIds()) {
                List<Long> assetsIds = new ArrayList<>();
                assetsIds.add(assetsId);
                taskCreateQuery.setAssetsIds(assetsIds);
                taskIds.add(taskService.startTask(taskCreateQuery));
            }
            response.setData(taskIds);
        } catch (Exception e) {
            response.setStatus(HttpStatusEnum.FAIL.getValue());
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * 重新检测静态检测任务
     *
     * @param taskId 任务ID
     * @return
     * @throws IjiamiCommandException
     */
    @PostMapping(value = "/restartTask/{taskId}")
    @ApiOperation(value = "重新该任务的静态检测")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "重新发起静态检测"
    )
    public BaseResponse<Long> restartTask(@ApiParam(value = "任务id") @PathVariable Long taskId) throws IjiamiCommandException {
        BaseResponse<Long> response = new BaseResponse<>();
        response.setData(taskService.restartTask(taskId));
        return response;
    }

    /**
     * 重新检测快速检测动态任务
     *
     * @param taskId 任务ID
     * @return
     * @throws IjiamiCommandException
     */
    @RequestLimit
    @PostMapping(value = "/restartDynamicTask/{taskId}")
    @ApiOperation(value = "重新该任务的动态检测")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "重新发起动态检测"
    )
    public BaseResponse<Long> restartDynamicTask(@ApiParam(value = "任务id") @PathVariable Long taskId) throws IjiamiCommandException {
        BaseResponse<Long> response = new BaseResponse<>();
        response.setData(taskService.restartDynamicTask(taskId));
        return response;
    }

    /**
     * 重新检测快速检测动态任务
     *
     * @param taskId 任务ID
     * @return
     * @throws IjiamiCommandException
     */
    @RequestLimit
    @GetMapping(value = "/restartDynamicTaskStage/{taskId}")
    @ApiOperation(value = "重新该任务的动态检测阶段")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "重新该任务的动态检测阶段"
    )
    public BaseResponse<Integer> restartDynamicTaskStage(@ApiParam(value = "任务id") @PathVariable Long taskId) throws IjiamiCommandException {
        BaseResponse<Integer> response = new BaseResponse<>();
        response.setData(taskService.restartDynamicTaskStage(taskId));
        return response;
    }

    /**
     * 重新检测快速检测动态任务
     *
     * @param taskId 任务ID
     * @return
     * @throws IjiamiCommandException
     */
    @RequestLimit
    @PostMapping(value = "/restartDynamicTask2/{taskId}")
    @ApiOperation(value = "5.0版本的重新动态检测任务接口")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "5.0版本的重新动态检测任务接口"
    )
    public BaseResponse<RestartTaskVO> restartDynamicTask2(@ApiParam(value = "任务id") @PathVariable Long taskId) throws IjiamiCommandException {
        BaseResponse<RestartTaskVO> response = new BaseResponse<>();
        RestartTaskVO restartTaskVO = new RestartTaskVO();
        restartTaskVO.setStage(taskService.restartDynamicTaskStage(taskId));
        restartTaskVO.setTaskId(taskService.restartDynamicTask(taskId));
        response.setData(restartTaskVO);
        return response;
    }

    /**
     * 启动任务
     *
     * @param assetsId
     * @param deviceType 1是云手机 2是客户端沙盒
     * @return
     */
    @ApiOperation(value = "启动任务-增加设备类型判断", notes = "启动任务-增加客户端判断- 1是云手机 2是客户端沙盒")
    @PostMapping("/startTask/{assetsId}/{deviceType}")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "通过不同设备类型启动任务"
    )
    public BaseResponse<Long> startTask(@PathVariable Long assetsId, @PathVariable int deviceType) {
        BaseResponse<Long> response = new BaseResponse<>();
        IUser currentUser = getCurrentUser();
        TaskCreateQuery taskCreateQuery = new TaskCreateQuery();
        taskCreateQuery.setUserId(currentUser.getUserId());
        List<Long> assetsIds = new ArrayList<>();
        assetsIds.add(assetsId);
        taskCreateQuery.setAssetsIds(assetsIds);
        taskCreateQuery.setTemplateId(1L);
        taskCreateQuery.setDynamicDeviceTypeEnum(DynamicDeviceTypeEnum.getItem(deviceType));
        taskCreateQuery.setTerminalTypeEnum(TerminalTypeEnum.ANDROID);
        response.setData(taskService.startTask(taskCreateQuery));
        return response;
    }

    @ApiOperation(value = "开始ios动态、法规检测")
    @PostMapping(value = "/startIosDynamicDetect")
    @OperateLog(
            moduleName = "隐私检测模块",
            operateName = "开始ios动态、法规检测"
    )
    public BaseResponse<Long> startIosDynamicDetect(@RequestBody TaskProgressQuery taskProgressQuery) throws IjiamiCommandException {
        BaseResponse<Long> response = new BaseResponse<>();
        IUser currentUser = getCurrentUser();
        taskProgressQuery.setUserId(currentUser.getUserId());
        taskService.startIosDynamicDetect(taskProgressQuery);
        return response;
    }

    /**
     * 前端目前使用这个接口完成动态检测和法规检测，使用changeTaskStatus启动任务
     *
     * @param taskProgressQuery
     * @return
     * @throws IjiamiCommandException
     */
    @ApiOperation(value = "停止或者完成ios动态、法规检测")
    @PostMapping(value = "/stopIosDynamicDetect")
    @OperateLog(
            moduleName = "隐私检测模块",
            operateName = "停止ios动态、法规检测"
    )
    public BaseResponse<Long> stopIosDynamicDetect(@RequestBody TaskProgressQuery taskProgressQuery) throws IjiamiCommandException {
        BaseResponse<Long> response = new BaseResponse<>();
        IUser currentUser = getCurrentUser();
        taskProgressQuery.setUserId(currentUser.getUserId());
        taskService.stopIosDynamicDetect(taskProgressQuery);
        return response;
    }

    @ApiOperation(value = "修改任务状态")
    @PostMapping("/changeTaskStatus")
    public BaseResponse<ExpertTestingTaskVO> changeTaskStatus(@RequestBody TaskChangeStatusDTO taskChangeStatusDTO) {
        BaseResponse<ExpertTestingTaskVO> response = new BaseResponse<>();
        taskChangeStatusDTO.setUserId(getCurrentUser().getUserId());
        response.setData(taskService.changeTaskStatus(taskChangeStatusDTO));
        if(taskChangeStatusDTO.getType() != 4){
            response.setData(null);
        }
        return response;
    }

    @ApiOperation("V3.0 -- 开始完整检测/法规检测/重新检测")
    @PostMapping("/dynamicDetect/{taskId}/{type}")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "开始完整检测/法规检测/重新检测"
    )
    public BaseResponse<Long> startTask(@ApiParam(value = "任务id") @PathVariable Long taskId,
                                        @ApiParam(value = "类型 1完整检测 2法规检测 3重新检测") @PathVariable Integer type) {
        BaseResponse<Long> baseResponse = new BaseResponse<>();
        baseResponse.setData(taskService.startDynamicTask(taskId, type));
        return baseResponse;
    }

    @ApiOperation("V3.0 -- 开始完整检测/法规检测/重新检测/设备类型(1是云手机 2是客户端沙盒)")
    @PostMapping("/dynamicDetect/{taskId}/{type}/{deviceType}")
    public BaseResponse<Long> startTask(@ApiParam(value = "任务id") @PathVariable Long taskId,
                                        @ApiParam(value = "类型 1完整检测 2法规检测 3重新检测") @PathVariable Integer type,
                                        @ApiParam(value = "设备类型(1是云手机 2是客户端沙盒)") @PathVariable int deviceType) {
        BaseResponse<Long> baseResponse = new BaseResponse<>();
        baseResponse.setData(taskService.startDynamicTask(taskId, type, deviceType));
        return baseResponse;
    }

    @ApiOperation("V3.0 -- 任务名额预占接口")
    @PostMapping("/taskPreempted/{taskId}/{deviceType}")
    public BaseResponse<Long> taskPreempted(@ApiParam(value = "任务id") @PathVariable Long taskId,
                                            @ApiParam(value = "设备类型(1是云手机 2是客户端沙盒)") @PathVariable int deviceType) {
        BaseResponse<Long> baseResponse = new BaseResponse<>();
        baseResponse.setData(taskService.taskPreempted(taskId, deviceType));
        return baseResponse;
    }

    /**
     * 停止检测任务
     *
     * @return BaseResponse<String>
     * @throws IjiamiApplicationException
     */
    @ApiOperation(value = "停止检测任务", notes = "停止检测任务")
    @PostMapping(value = "/stopTask/{taskId}")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "停止检测任务"
    )
    public BaseResponse<Void> stopTask(@PathVariable Long taskId) throws IjiamiApplicationException, IjiamiCommandException {
        BaseResponse<Void> response = new BaseResponse<>();

        Long userId = getCurrentUser().getUserId();
        TTask task = taskService.findById(taskId);
        if(!task.getCreateUserId().equals(userId)) {
        	response.setStatus(HttpStatusEnum.FAIL.getValue());
            response.setMessage("操作失败");
            return response;
        }

        if (taskService.stopTask(taskId)) {
            response.setMessage("操作成功");
        } else {
            response.setStatus(HttpStatusEnum.FAIL.getValue());
            response.setMessage("操作失败");
        }
        return response;
    }

    @ApiOperation("删除检测记录")
    @DeleteMapping("/delete/{id}")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "删除检测记录"
    )
    public BaseResponse<Void> delete(@PathVariable("id") Long id) throws IjiamiApplicationException {
        taskService.deleteByTaskId(id, getCurrentUser().getUserId(), isAdmin());
        return new BaseResponse<>();
    }

    @ApiOperation(value = "批量删除检测记录", notes = "多个id用半角逗号隔开，例如：1,2,3,4,5")
    @DeleteMapping("/batchDelete/{ids}")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "批量删除检测记录"
    )
    public BaseResponse<Void> batchDelete(@PathVariable("ids") String ids) throws IjiamiApplicationException {
        List<Long> taskIds = Arrays.stream(ids.split(",")).map(Long::parseLong).collect(Collectors.toList());
        taskService.deleteInTaskId(taskIds, getCurrentUser().getUserId(), isAdmin());
        return new BaseResponse<>();
    }

    /**
     * v2.3.1 用于安卓客户端查询 法规检测评估点
     *
     * @param type
     * @return
     */
    @ApiOperation(value = "查询所有隐私条款评估点")
    @GetMapping("/findPrivacyCheckByType/{type}")
    public BaseResponse<List<TPrivacyCheck>> findPrivacyCheckByType(@PathVariable("type") Integer type) {
        BaseResponse<List<TPrivacyCheck>> response = new BaseResponse<>();
        response.setData(iPrivacyCheckService.findByTypeVo(type, null));
        return response;
    }

    /**
     * 根据终端类型查询法律法规的评估点
     *
     * @param terminalType 终端类型
     * @param lawType      法律法规
     * @return
     */
    @ApiOperation(value = "查询所有隐私条款评估点")
    @GetMapping("/findPrivacyCheck/{terminalType}/{lawType}")
    public BaseResponse<List<PrivacyPolicyTypeVO>> findPrivacyCheckByType(@PathVariable("terminalType") Integer terminalType,
                                                                          @PathVariable("lawType") Integer lawType) {
        BaseResponse<List<PrivacyPolicyTypeVO>> response = new BaseResponse<>();
        List<PrivacyPolicyTypeVO> vo = privacyCheckService.findCheckPointByTerminalAndType(terminalType, lawType);
        response.setData(vo);
        return response;
    }

    @ApiOperation(value = "图片上传")
    @PostMapping("/upload/image")
    public BaseResponse<String> uploadImage(@RequestBody ImageUploadDTO image) throws IjiamiApplicationException {
        if (Objects.isNull(image) || StringUtils.isBlank(image.getBase64())) {
            throw new IjiamiApplicationException("非法参数");
        }

        MultipartFile multipartFile;
        try {
            multipartFile = Base64DecodedMultipartFile.base64ToMultipart(image.getBase64());
        } catch (IOException e) {
            LOG.error("上传失败，图片加载异常");
            throw new IjiamiApplicationException("图片加载异常");
        }

        BaseResponse<String> response = new BaseResponse<>();
        response.setData(lawDetectionService.imgUploadToServer(multipartFile));
        return response;
    }

    @ApiOperation(value = "保存法规检测数据不区分终端")
    @PostMapping(value = "/saveLawDetectionData")
    public BaseResponse<?> saveLawData(@RequestBody LawDetectionDTO lawDetectionDTO) throws IjiamiApplicationException {
        if (Objects.isNull(lawDetectionDTO) || lawDetectionDTO.getTaskId() == null) {
            throw new IjiamiApplicationException("taskId不能为空");
        }
        lawDetectionService.analysisLawDetectData(lawDetectionDTO);
        return new BaseResponse<>();
    }

    @ApiOperation(value = "复核完成")
    @PostMapping(value = "/reviewFinish")
    public BaseResponse<?> reviewFinish(@RequestBody ReviewFinish reviewFinish) throws IjiamiApplicationException {
        if (Objects.isNull(reviewFinish) || reviewFinish.getTaskId() == null) {
            throw new IjiamiApplicationException("taskId不能为空");
        }
        miitDetectService.updateReviewTaskComplianceStatus(reviewFinish.getTaskId());
        return new BaseResponse<>();
    }

    @ApiOperation(value = "深度检测完成")
    @PostMapping(value = "/deepDetectionFinish")
    public BaseResponse<?> deepDetectionFinish(@RequestBody DeepDetectionFinish deepDetectionFinish) throws IjiamiApplicationException {
        if (Objects.isNull(deepDetectionFinish) || deepDetectionFinish.getTaskId() == null) {
            throw new IjiamiApplicationException("taskId不能为空");
        }
        miitDetectService.setDeepDetectionFinish(deepDetectionFinish);
        return new BaseResponse<>();
    }

    /**
     * 获取检测详情
     *
     * @param id
     * @return BaseResponse<TaskDetailVO>
     */
    @ApiOperation(value = "获取检测详情", notes = "获取检测详情")
    @GetMapping(value = "/getTaskDetail/{id}")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "获取检测详情"
    )
    public BaseResponse<TaskDetailVO> getTaskDetail(@PathVariable("id") String id) {
        BaseResponse<TaskDetailVO> response = new BaseResponse<>();
        response.setData(taskService.getTaskDetail(id));
        response.setMessage("查询成功！");
        return response;
    }

    @ApiOperation(value = "详情 --> 信息传输风险")
    @GetMapping(value = "/transferDetail")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "获取信息传输风险详情"
    )
    public BaseResponse<Map<String, Object>> transferDetail(@RequestParam("documentId") String documentId) throws Exception {
        BaseResponse<Map<String, Object>> response = new BaseResponse<>();
        response.setData(iPrivacyDetectionTransferRiskService.getTransferDetail(documentId));
        return response;
    }

    @ApiOperation(value = "详情 --> 个人信息风险漏洞")
    @GetMapping(value = "/personalInfoRiskDetail")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "获取个人信息风险漏洞详情"
    )
    public BaseResponse<PersonalInfoRiskDetailVO> personalInfoRiskDetail(@RequestParam("documentId") String documentId) throws Exception {
        BaseResponse<PersonalInfoRiskDetailVO> response = new BaseResponse<>();
        response.setData(iPrivacyDetectionTransferRiskService.getPersonalInfoRiskDetail(documentId));
        return response;
    }

    @ApiOperation(value = "详情 --> 合规检测")
    @GetMapping(value = "/lawInfoRiskDetail")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "获取合规检测详情"
    )
    public BaseResponse<List<TPrivacyPolicyResult>> lawInfoRiskDetail(@RequestParam("documentId") String documentId) throws Exception {
        BaseResponse<List<TPrivacyPolicyResult>> response = new BaseResponse<>();
        response.setData(iPrivacyDetectionTransferRiskService.getLawInfoRiskDetail(documentId));
        return response;
    }

    @ApiOperation(value = "详情 --> 权限检测")
    @GetMapping(value = "/safeDetail")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "获取权限检测详情"
    )
    public BaseResponse<SafeDetailVO> safeDetail(@RequestParam("documentId") String documentId,
                                                        @RequestParam(value = "behaviorStage", required = false) Integer behaviorStage)
            throws Exception {
        BaseResponse<SafeDetailVO> response = new BaseResponse<>();
        response.setData(iPrivacyDetectionService.getSafeDetail(documentId, behaviorStage));
        return response;
    }

    @ApiOperation(value = "详情 --> 行为SDK名称")
    @GetMapping(value = "/behaviorSdk")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "获取行为SDK名称"
    )
    public BaseResponse<List<String>> behaviorSdk(@RequestParam("documentId") String documentId,
                                                  @RequestParam(value = "behaviorStage", required = false) Integer behaviorStage)
            throws Exception {
        BaseResponse<List<String>> response = new BaseResponse<>();
        response.setData(iPrivacyDetectionService.getBehaviorSdk(documentId, behaviorStage));
        return response;
    }

    @ApiOperation(value = "V3.3详情 --> 法规检测详情行为SDK名称")
    @GetMapping(value = "/getLawDetailBehaviorSdk")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "获取法规检测详情行为SDK名称"
    )
    public BaseResponse<List<String>> getLawDetailBehaviorSdk(@RequestParam("documentId") String documentId,
                                                              @RequestParam(value = "dataType") Integer dataType,
                                                              @RequestParam(value = "itemNo") String itemNo)
            throws Exception {
        BaseResponse<List<String>> response = new BaseResponse<>();
        response.setData(iPrivacyDetectionService.getLawDetailBehaviorSdk(documentId, dataType, itemNo));
        return response;
    }

    @ApiOperation(value = "详情 --> 基础信息")
    @GetMapping(value = "/basicDetail")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "获取基础信息详情"
    )
    public BaseResponse<BaseMessageVO> basicDetail(@RequestParam("documentId") String documentId) throws Exception {
        BaseResponse<BaseMessageVO> response = new BaseResponse<>();
        BaseMessageVO vo = iPrivacyDetectionService.getAppBaseInfo(documentId);
        response.setData(vo);
        return response;
    }

    @ApiOperation(value = "详情 --> SDK信息")
    @GetMapping(value = "/sdkDetail")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "获取SDK信息详情"
    )
    public BaseResponse<List<SdkVO>> sdkDetail(@RequestParam("documentId") String documentId) throws Exception {
        BaseResponse<List<SdkVO>> response = new BaseResponse<>();
        List<SdkVO> list = iPrivacyDetectionService.getSDKList(documentId);
        response.setData(list);
        return response;
    }

    @ApiOperation(value = "详情 --> 疑似SDK信息")
    @GetMapping(value = "/suspiciousSdkDetail")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "获取疑似SDK信息详情"
    )
    public BaseResponse<List<SdkVO>> suspiciousSdkDetail(@RequestParam("documentId") String documentId) throws Exception {
        BaseResponse<List<SdkVO>> response = new BaseResponse<>();
        List<SdkVO> list = iPrivacyDetectionService.getSuspiciousSdkList(documentId);
        response.setData(list);
        return response;
    }
    
    @ApiOperation(value = "详情 --> IOS_SDK信息new")
    @GetMapping(value = "/iosSdkDetail")
    @OperateLog(
            moduleName ="IOS隐私检测模块new",
            operateName = "IOS获取SDK信息详情new"
    )
    public BaseResponse<SdkResponseVO> getIosSDKList(@RequestParam("documentId") String documentId) throws Exception {
        BaseResponse<SdkResponseVO> response = new BaseResponse<>();
        SdkResponseVO vo = iPrivacyDetectionService.getIosSDKList(documentId);
        response.setData(vo);
        return response;
    }

    @ApiOperation(value = "详情 --> 自评估指南、GB/T 35273")
    @GetMapping(value = "/policyDetail")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "获取自评估指南、GB/T 35273"
    )
    public BaseResponse<List<PrivacyPolicyTypeVO>> policyDetail(@ApiParam(value = "任务id") @RequestParam("taskId") Long taskId,
                                                                @ApiParam(value = "类型 1.自评估指南 2.GBT35273") @RequestParam(required = false, defaultValue = "1") Integer type) {
        BaseResponse<List<PrivacyPolicyTypeVO>> response = new BaseResponse<>();
        TTask task = taskService.findById(taskId);
        List<PrivacyPolicyTypeVO> vo = privacyCheckService.findByTaskIdAndType(taskId, type, task.getTerminalType().getValue());
        response.setData(vo);
        return response;
    }

    @ApiOperation(value = "详情 --> 自评估指南、GB/T 35273 --> 法规检测点截图")
    @GetMapping(value = "/policyDetail/images")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "获取法规检测点截图信息"
    )
    public BaseResponse<List<String>> policyDetailImages(@ApiParam(value = "任务id") @RequestParam("taskId") Long taskId,
                                                         @ApiParam(value = "法规检测点id") @RequestParam("policyId") Long policyId) {
        BaseResponse<List<String>> response = new BaseResponse<>();
        List<String> images = privacyPolicyImgServiceImpl.listImagesByTaskIdAndPolicyId(taskId, policyId);
        response.setData(images);
        return response;
    }

    @ApiOperation(value = "查询法律法规条款名称接口")
    @GetMapping("/compliance")
    public BaseResponse<List<Map<String, Object>>> getLawList() {
        BaseResponse<List<Map<String, Object>>> baseResponse = new BaseResponse<>();
        baseResponse.setData(miitDetectService.getLawList(null));
        return baseResponse;
    }
    
    @ApiOperation(value = "查询法律法规条款名称接口")
    @GetMapping("/compliance/{taskId}")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询法律法规条款名称"
    )
    public BaseResponse<List<Map<String, Object>>> getLawList(@PathVariable Long taskId) {
        BaseResponse<List<Map<String, Object>>> baseResponse = new BaseResponse<>();
        baseResponse.setData(miitDetectService.getLawList(taskId));
        return baseResponse;
    }

    @ApiOperation(value = "详情 --> 164号文检测结果 法规列表展示接口")
    @GetMapping("/compliance/{lawId}/{taskId}")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "获取164号文检测结果列表"
    )
    public BaseResponse<CountLawDetectResult> getLawsResultDetail(@PathVariable Long lawId, @PathVariable Long taskId) {
        BaseResponse<CountLawDetectResult> baseResponse = new BaseResponse<>();
        CountLawDetectResult result = miitDetectService.findLawDetectResultByTaskId(lawId, taskId);
        baseResponse.setData(result);
        return baseResponse;
    }


    @ApiOperation(value = "v3.3详情 --> 法规检测结果详情接口，其中行为数据是分页")
    @PostMapping("/compliance/detailByPage")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "获取自动化检测的法规检测结果详情"
    )
    public BaseResponse<LawDetectDetailVO> getLawsResultByPage(@RequestBody LawItemResultDetailQuery query) {
        BaseResponse<LawDetectDetailVO> baseResponse = new BaseResponse<>();
        LawDetectDetailVO detail = miitDetectService.findDetailPageByTaskIdAndItemNo(query);
        baseResponse.setData(detail);
        return baseResponse;
    }

    @ApiOperation(value = "v3.3详情 --> 法规检测行为数据分页获取接口")
    @PostMapping("/compliance/actionsByPage")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "获取自动化检测的行为数据列表"
    )
    public BaseResponse<PageInfo<LawActionDetailVO>> findLawActionDetailByPage(@Valid @RequestBody LawActionDetailQuery query) {
        BaseResponse<PageInfo<LawActionDetailVO>> baseResponse = new BaseResponse<>();
        PageInfo<LawActionDetailVO> detail = miitDetectService.findLawActionDetailByPage(query);
        baseResponse.setData(detail);
        return baseResponse;
    }

    @ApiOperation(value = "v2.5详情 --> 164号文 法规检测结果详情接口-->修改状态")
    @PostMapping("/compliance/updateComplianceStatus")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "修改164号文检测结果状态"
    )
    public BaseResponse<LawDetectDetailVO> updateComplianceStatus(@RequestBody ComplianceVO compliance) throws IjiamiApplicationException {
        BaseResponse<LawDetectDetailVO> baseResponse = new BaseResponse<>();
        IUser currentUser = getCurrentUser();
        compliance.setCreateUserId(currentUser.getUserId());
        miitDetectService.updateComplianceStatus(compliance);
        return baseResponse;
    }
    
    @ApiOperation(value = "详情 --> 164号文 法规检测结果详情接口-->多个批量修改状态")
    @PostMapping("/compliance/updateList/{taskId}")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "批量修改164号文检测结果状态"
    )
    public BaseResponse<LawDetectDetailVO> updateListComplianceStatus(@ApiParam(value = "任务ID") @PathVariable Long taskId, @RequestBody List<ComplianceVO> compList) {
        BaseResponse<LawDetectDetailVO> baseResponse = new BaseResponse<>();
        miitDetectService.updateComplianceStatus(taskId, compList);
        return baseResponse;
    }

    @ApiOperation(value = "详情 --> 个人信息")
    @GetMapping("/personal/{taskId}")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询个人信息详情"
    )
    public BaseResponse<List<SensitiveTypeVO>> personalInfo(@PathVariable Long taskId) {
        BaseResponse<List<SensitiveTypeVO>> baseResponse = new BaseResponse<>();
        List<SensitiveTypeVO> sensitiveTypeVOS = sensitiveTypeService.findByTaskId(taskId);
        baseResponse.setData(sensitiveTypeVOS);
        return baseResponse;
    }

    @GetMapping(value = "/delPersonalWord/{type}/{id}")
    @ApiOperation(value = "删除存储个人信息和传输个人信息第三层")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "删除存储个人信息和传输个人信息"
    )
    public BaseResponse delPersonalWord(@ApiParam(value = "1.传输个人信息2.存储个人信息") @PathVariable(value = "type", required = true) Integer type,
                                        @ApiParam(value = "第三层主键id") @PathVariable(value = "id", required = true) Long id) throws IjiamiCommandException {
        taskService.delPersonalWord(type, id);
        return new BaseResponse();
    }

    @ApiOperation(value = "V3.0--详情--传输个人信息--第一层")
    @GetMapping("/transfer/personal/type")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询传输个人信息"
    )
    public BaseResponse<List<CountSensitiveTypeVO>> countSensitiveType(Long taskId, Integer behaviorStage) {
        BaseResponse<List<CountSensitiveTypeVO>> baseResponse = new BaseResponse<>();
        baseResponse.setData(privacySensitiveWordService.countSensitiveTypeByTaskId(taskId, behaviorStage));
        return baseResponse;
    }

    @ApiOperation(value = "V3.0--详情--传输个人信息--第二层")
    @GetMapping("/transfer/personal/name")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询传输个人信息"
    )
    public BaseResponse<List<CountSensitiveNameVO>> countSensitiveName(Long taskId, Long typeId, Integer behaviorStage) {
        BaseResponse<List<CountSensitiveNameVO>> baseResponse = new BaseResponse<>();
        baseResponse.setData(privacySensitiveWordService.countSensitiveNameByTaskId(taskId, typeId, behaviorStage));
        return baseResponse;
    }

    @ApiOperation(value = "V3.0--详情--传输个人信息--第三层")
    @GetMapping("/transfer/personal/word")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询传输个人信息"
    )
    public BaseResponse<List<TPrivacySensitiveWord>> findSensitiveWord(Long taskId, Long typeId, String name, Integer behaviorStage) {
        BaseResponse<List<TPrivacySensitiveWord>> baseResponse = new BaseResponse<>();
        baseResponse.setData(privacySensitiveWordService.findByTaskIdAndTypeIdAndName(taskId, typeId, name, behaviorStage));
        return baseResponse;
    }

    @ApiOperation(value = "v2.5详情--传输个人信息 -->标记数据(正确或者误判)")
    @PostMapping("/transfer/updateSensitiveWordStatus")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "标记传输个人信息数据正确或者误判"
    )
    public BaseResponse<LawDetectDetailVO> updateSensitiveWordStatus(@RequestBody ActionComplianceVO actionComplianceVO) throws IjiamiApplicationException {
        BaseResponse<LawDetectDetailVO> baseResponse = new BaseResponse<>();
        IUser currentUser = getCurrentUser();
        actionComplianceVO.setCreateUserId(currentUser.getUserId());
        privacySensitiveWordService.updateSensitiveWordStatus(actionComplianceVO);
        return baseResponse;
    }

    @ApiOperation(value = "V3.0--详情--存储个人信息--第一层")
    @GetMapping("/sharedPrefs/personal/type")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询存储个人信息"
    )
    public BaseResponse<List<CountSharedPrefsTypeVO>> countSharedPrefsType(Long taskId, Integer behaviorStage) {
        BaseResponse<List<CountSharedPrefsTypeVO>> baseResponse = new BaseResponse<>();
        baseResponse.setData(privacySharedPrefsService.countSharedPrefsTypeByTaskId(taskId, behaviorStage));
        return baseResponse;
    }

    @ApiOperation(value = "V3.0--详情--存储个人信息--第二层")
    @GetMapping("/sharedPrefs/personal/name")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询存储个人信息"
    )
    public BaseResponse<List<CountSharedPrefsNameVO>> countSharedPrefsName(Long taskId, Long typeId, Integer behaviorStage) {
        BaseResponse<List<CountSharedPrefsNameVO>> baseResponse = new BaseResponse<>();
        baseResponse.setData(privacySharedPrefsService.countSharedPrefsNameByTaskId(taskId, typeId, behaviorStage));
        return baseResponse;
    }

    @ApiOperation(value = "V3.0--详情--存储个人信息--第三层")
    @GetMapping("/sharedPrefs/personal/word")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询存储个人信息"
    )
    public BaseResponse<List<TPrivacySharedPrefs>> findSharedPrefs(Long taskId, Long typeId, String name, Integer behaviorStage) {
        BaseResponse<List<TPrivacySharedPrefs>> baseResponse = new BaseResponse<>();
        LOG.info("V3.0--详情--存储个人信息--第三层---name={}", name);
        baseResponse.setData(privacySharedPrefsService.findByTaskIdAndTypeIdAndName(taskId, typeId, name, behaviorStage));
        return baseResponse;
    }

    @ApiOperation(value = "v2.5详情--存储个人信息 -->标记数据(正确或者误判)")
    @PostMapping("/sharedPrefs/updateSharedPrefsStatus")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "标记存储个人信息数据为正确或者误判"
    )
    public BaseResponse<LawDetectDetailVO> updateSharedPrefsStatus(@RequestBody ActionComplianceVO actionComplianceVO) throws IjiamiApplicationException {
        BaseResponse<LawDetectDetailVO> baseResponse = new BaseResponse<>();

        IUser currentUser = getCurrentUser();
        actionComplianceVO.setCreateUserId(currentUser.getUserId());
        privacySharedPrefsService.updateSharedPrefsStatus(actionComplianceVO);
        return baseResponse;
    }

    @ApiOperation(value = "V3.0--详情--信息传输风险--第一层")
    @GetMapping("/outside/address/count")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询传输信息风险数据"
    )
    public BaseResponse<List<CountOutsideTypeVO>> countOutsideAddress(@ApiParam(value = "任务id") Long taskId, @ApiParam(value = "阶段类型") Integer behaviorStage) {
        BaseResponse<List<CountOutsideTypeVO>> baseResponse = new BaseResponse<>();
        baseResponse.setData(privacyOutsideAddressService.countByOutside(taskId, behaviorStage));
        return baseResponse;
    }

    @ApiOperation(value = "V3.0--详情--信息传输风险--第二层")
    @GetMapping("/outside/address/detail")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询传输信息风险数据"
    )
    public BaseResponse<List<TPrivacyOutsideAddress>> findByOutside(@ApiParam(value = "任务id") Long taskId, @ApiParam(value = "1境外 0境内") Integer outside,
                                                                    @ApiParam(value = "阶段类型") Integer behaviorStage) {
        BaseResponse<List<TPrivacyOutsideAddress>> baseResponse = new BaseResponse<>();
        baseResponse.setData(privacyOutsideAddressService.findByTaskIdAndOutside(taskId, outside, behaviorStage));
        return baseResponse;
    }

    @ApiOperation(value = "V3.0--详情--信息传输风险--第三层")
    @GetMapping("/outside/address/detail/stackInfo")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询传输信息风险数据"
    )
    public BaseResponse<List<TPrivacyOutsideAddress>> findByOutsideStackInfo(@ApiParam(value = "任务id") Long taskId, @ApiParam(value = "ip") String ip,
                                                                             @ApiParam(value = "host") String host,
                                                                             @ApiParam(value = "阶段类型") Integer behaviorStage) {
        BaseResponse<List<TPrivacyOutsideAddress>> baseResponse = new BaseResponse<>();
        baseResponse.setData(privacyOutsideAddressService.findByTaskIdAndOutsideStackInfo(taskId, ip, host, behaviorStage));
        return baseResponse;
    }

    @ApiOperation(value = "应用行为信息枚举值")
    @GetMapping("/behaviors/actionInfo/{terminalType}")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询应用行为信息枚举值"
    )
    public BaseResponse<List<TActionNougat>> behaviors(@PathVariable Integer terminalType) {
        BaseResponse<List<TActionNougat>> baseResponse = new BaseResponse<>();
        baseResponse.setData(privacyActionNougatService.findTActionNougatByTerminalType(terminalType));
        return baseResponse;
    }

    @ApiOperation(value = "详情 --> 应用行为（5.1沙箱）")
    @GetMapping("/behaviors/{taskId}")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询应用行为信息枚举值"
    )
    public BaseResponse<List<PrivacyActionVO>> behaviors(@PathVariable Long taskId) {
        BaseResponse<List<PrivacyActionVO>> baseResponse = new BaseResponse<>();
        baseResponse.setData(privacyActionService.findActionsByTaskId(taskId));
        return baseResponse;
    }

    @ApiOperation(value = "详情 --> 查询是否为7.1沙箱的行为数据")
    @GetMapping("/behaviors/isNougat/{taskId}")
    public BaseResponse<Boolean> isNougat(@PathVariable Long taskId) {
        BaseResponse<Boolean> baseResponse = new BaseResponse<>();
        baseResponse.setData(privacyActionNougatService.isNougat(taskId));
        return baseResponse;
    }

    @ApiOperation(value = "详情 --> 应用行为（7.1沙箱）")
    @PostMapping(value = {"/behaviors/nougat" })
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询应用行为信息"
    )
    public BaseResponse<List<TPrivacyActionNougat>> nougatBehaviors(@RequestBody BehaviorQuery behaviorQuery) {
        BaseResponse<List<TPrivacyActionNougat>> baseResponse = new BaseResponse<>();
        baseResponse.setData(
                privacyActionNougatService.findByTaskIdAndExecutors(behaviorQuery.getTaskId(), behaviorQuery.getExecutors(), behaviorQuery.getBehaviorStage()));
        //        List<TPrivacyActionNougat> privacyActionNougats = privacyActionNougatService.countActionByTaskId(taskId);//TODO这块代码没看到使用
        return baseResponse;
    }

    @ApiOperation(value = "详情 --> 应用行为（7.1沙箱）详细数据列表")
    @PostMapping("/behaviors/nougat/detail")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询应用行为信息"
    )
    public BaseResponse<List<TPrivacyActionNougat>> nougatBehaviorsDetail(@RequestBody BehaviorQuery behaviorQuery) {
        BaseResponse<List<TPrivacyActionNougat>> baseResponse = new BaseResponse<>();
        baseResponse.setData(privacyActionNougatService
                .findByTaskIdAndActionIdAndExecutors(behaviorQuery.getTaskId(), behaviorQuery.getActionId(), behaviorQuery.getExecutors(),
                        behaviorQuery.getBehaviorStage()));
        return baseResponse;
    }

    @ApiOperation(value = "详情 --> 应用行为统计类型总数")
    @GetMapping("/behaviors/statisticsCount/{taskId}")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询应用行为数据数量"
    )
    public BaseResponse<Integer> behaviorsStatisticsCategoryCount(@PathVariable Long taskId) {
        BaseResponse<Integer> baseResponse = new BaseResponse<>();
        baseResponse.setData(privacyActionNougatService.countBehaviorsCategoryByTaskId(taskId));
        return baseResponse;
    }

    @ApiOperation(value = "详情 --> 应用行为统计按照行为运行状态")
    @GetMapping("/behaviors/statisticsStage/{taskId}/{behaviorStage}/{merge}")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询应用行为信息"
    )
    public BaseResponse<List<TPrivacyActionNougat>> behaviorsStatisticsStage(@PathVariable Long taskId, @PathVariable int behaviorStage,
                                                                             @PathVariable Boolean merge) {
        BaseResponse<List<TPrivacyActionNougat>> baseResponse = new BaseResponse<>();
        baseResponse.setData(privacyActionNougatService.countBehaviorsByTaskIdAndStage(taskId, behaviorStage, merge));
        return baseResponse;
    }

    @ApiOperation(value = "详情 --> 应用行为统计完整统计")
    @GetMapping("/behaviors/statisticsAll/{taskId}/{merge}")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询应用行为信息"
    )
    public BaseResponse<List<TPrivacyActionNougat>> behaviorsStatistics(@PathVariable Long taskId, @PathVariable Boolean merge) {
        BaseResponse<List<TPrivacyActionNougat>> baseResponse = new BaseResponse<>();
        baseResponse.setData(privacyActionNougatService.countBehaviorsByTaskId(taskId, merge));
        return baseResponse;
    }

    @ApiOperation(value = "查询检测任务的法律法规类型", notes = "法律法规，至少存在一个，最多同时存在。")
    @GetMapping("/countType/{taskId}")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询检测任务的法律法规类型"
    )
    public BaseResponse<List<TPrivacyPolicyType>> countType(@PathVariable Long taskId) {
        BaseResponse<List<TPrivacyPolicyType>> baseResponse = new BaseResponse<>();
        TTask tTask = taskService.findById(taskId);
        List<TPrivacyPolicyType> tPrivacyPolicyTypes = new ArrayList<>();
        tPrivacyPolicyTypes = privacyCheckService.countLaw(taskId,tTask.getTerminalType().getValue());
        baseResponse.setData(tPrivacyPolicyTypes);
        return baseResponse;
    }

    @ApiOperation(value = "查询法律法规列表，用于动态检测前选择法律法规")
    @GetMapping("/law/{terminalType}")
    public BaseResponse<List<TPrivacyPolicyType>> findLaw(@PathVariable int terminalType) {
        BaseResponse<List<TPrivacyPolicyType>> baseResponse = new BaseResponse<>();
        baseResponse.setData(privacyCheckService.findLaw(terminalType));
        return baseResponse;
    }

    @ApiOperation(value = "报告下载", notes = "报告下载")
    @GetMapping(value = "/downloadReport")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "下载报告"
    )
    public void downloadReport(@ApiParam(value = "文档id") @RequestParam("documentId") String documentId,
                               @ApiParam(value = "标签页通知id，用来区分给哪个标签页发消息") @RequestParam(value = "notificationId", required = false) String notificationId,
                               @ApiParam(value = "法律法规类型 1.自评估指南 2.GB/T 35273") @RequestParam(value = "type", required = false) Integer[] type,
                               @ApiParam(value = "报告文件类型 1.word 2.pdf") @RequestParam("reportType") Integer[] reportType,
                               @ApiParam(value = "模板类型 1.监管者 2.开发者") @RequestParam("reportObject") Integer[] reportObject,
                               @ApiParam(value = "终端平台 1.Android 2.ios") @RequestParam("terminalType") Integer terminalType,
                               @ApiParam(value = "勾选的报告模板选项") @RequestParam(value = "itemNo", required = false) String[] itemNo, HttpServletRequest request,
                               HttpServletResponse response) throws IjiamiApplicationException, IOException {

        Long userId = getCurrentUser().getUserId();
        User user = userServiceImpl.findUserById(userId);
        File file = null;
        if (type.length == 1 && reportType.length == 1 && reportObject.length == 1) {
            //单个下载
            ReportResultVO report = iPrivacyDetectionService.downloadReport(documentId, type[0], reportType[0], reportObject[0], userId, terminalType,itemNo);
            if (report == null || report.report() == null) {
                sendMessageServiceImpl.sendNoticeToBrowserTab(MessageNotificationEnum.ERROR,
                        "report_build_failed", "报告下载失败", user, notificationId);
                return;
            }
            file = report.report();
        } else {
            //批量下载
            String reportRootPath = commonProperties.getProperty("ijiami.report.root.path");
            File reportDir = new File(reportRootPath + "out" + File.separator + UuidUtil.uuid());

            ReportResultVO reportResultVO = null;

            if (reportDir.mkdirs()) {
                for (Integer t : type) {
                    for (Integer r : reportType) {
                        for (Integer o : reportObject) {
                            ReportResultVO report = iPrivacyDetectionService.downloadReport(documentId, t, r, o, userId, terminalType,itemNo);
                            if (report == null || report.report() == null) {
                                continue;
                            }
                            FileUtils.copyFileToDirectory(report.report(), reportDir);
                            reportResultVO = report;
                        }
                    }
                }
                if (reportResultVO != null) {
                    String zipName = reportZipName(reportResultVO);
                    String compress = CommonUtil.compress(reportDir.getAbsolutePath(), reportDir.getParent() + File.separator + zipName);
                    file = new File(compress);
                }
            }
        }
        //发送信息给前端
        sendMessageServiceImpl.sendNoticeToBrowserTab(MessageNotificationEnum.SUCCESS, "report_build_sucess",
                "报告生成成功", user, notificationId);
        // 返回给浏览器
        HttpUtils.copyFile(file, request, response);
    }

    @ApiOperation("下载自动化报告")
    @GetMapping("/download/auto/report")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "下载自动化报告"
    )
    public void downloadAutoDetectReport(@ApiParam(value = "任务id") @RequestParam("taskId") Long taskId,
                                         @ApiParam(value = "标签页通知id，用来区分给哪个标签页发消息") @RequestParam(value = "notificationId", required = false) String notificationId,
                                         @ApiParam(value = "报告类型 1.word 2.pdf 3.html") @RequestParam("type") Integer[] type, 
                                         @ApiParam(value = "勾选的报告模板选项") @RequestParam(value = "itemNo", required = false) String[] itemNo,
                                         HttpServletRequest request,
                                         HttpServletResponse response) throws IjiamiApplicationException, IOException {
        Long userId = getCurrentUser().getUserId();
        User user = userServiceImpl.findUserById(userId);
        File file = null;
        if (type.length == 1) {
            //单个下载
          file = iPrivacyDetectionService.downloadAutoDetectReport(taskId, type[0]);
            if (file == null) {
                sendMessageServiceImpl.sendNoticeToBrowserTab(MessageNotificationEnum.ERROR, "report_build_failed", "报告下载失败", user, notificationId);
                return;
            }
        } else {
            //批量下载
            String reportRootPath = commonProperties.getProperty("ijiami.report.root.path");
            File reportDir = new File(reportRootPath + "out" + File.separator + UuidUtil.uuid());
            ReportResultVO reportResultVO = null;
            if (reportDir.mkdirs()) {
                for (Integer t : type) {
                    ReportResultVO report = iPrivacyDetectionService.downloadAutoDetectReportPlus(taskId, t);
                    if (report == null || report.report() == null) {
                        continue;
                    }
                    FileUtils.copyFileToDirectory(report.report(), reportDir);
                    reportResultVO = report;
                }
                if (reportResultVO != null) {
                    String zipName = reportZipName(reportResultVO);
                    String compress = CommonUtil.compress(reportDir.getAbsolutePath(), reportDir.getParent() + File.separator + zipName);
                    file = new File(compress);
                }
            }
        }
        sendMessageServiceImpl.sendNoticeToBrowserTab(MessageNotificationEnum.SUCCESS, "report_build_sucess", "报告下载成功", user, notificationId);
        HttpUtils.copyFile(file, request, response);
    }

    @ApiOperation(value = "V3.0--详情--下载抓包数据")
    @GetMapping("/download/result")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "下载抓包数据"
    )
    public void downloadResult(@RequestParam("documentId") String documentId,
                               @RequestParam(value = "notificationId", required = false) String notificationId,
                               HttpServletRequest request,
                               HttpServletResponse response) {
        Long userId = getCurrentUser().getUserId();
        User user = userServiceImpl.findUserById(userId);

        File file = iPrivacyDetectionService.downloadZip(documentId);
        if (file == null) {
            sendMessageServiceImpl.sendNoticeToBrowserTab(MessageNotificationEnum.ERROR, "zip_download_failed",
                    StringUtils.EMPTY, "数据包下载失败", user, HiddenEnum.SHOW, notificationId);
            response.setStatus(201);  //文件不存在
            try {
                response.setHeader("message", URLEncoder.encode("文件不存在，下载失败", "utf-8"));
            } catch (UnsupportedEncodingException e) {
                LOG.error("编码错误", e);
            }
            return;
        }

        sendMessageServiceImpl.sendNoticeToBrowserTab(MessageNotificationEnum.SUCCESS, "zip_download_success", StringUtils.EMPTY,
                "数据包下载成功", user, HiddenEnum.SHOW, notificationId);
        HttpUtils.copyFile(file, request, response);
    }

    @ApiOperation(value = "下载行为数据")
    @GetMapping("/download/actResult")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "下载行为数据"
    )
    public void downloadactResult(@RequestParam("taskId") String taskId,
    							  @RequestParam(value = "notificationId", required = false) String notificationId,
                                  HttpServletRequest request, HttpServletResponse response) {
        Long userId = getCurrentUser().getUserId();
        User user = userServiceImpl.findUserById(userId);
        File reportDir = null;
        try {
            //查询资产信息
            //查询行为数据
            String reportRootPath = commonProperties.getProperty("ijiami.report.root.path");
            reportDir = new File(reportRootPath + "out" + File.separator + UuidUtil.uuid());
            reportDir.mkdirs();
            ExcelReportQuery query = new ExcelReportQuery();
        	query.setTaskId(Long.valueOf(taskId));
        	query.setId(ConstantsUtils.ACTION_NUM_99);
            
            File file = excelReportService.downloadExcelData(query,reportDir,user);
            if (file==null || !file.exists()) {
                sendMessageServiceImpl.sendNoticeToBrowserTab(MessageNotificationEnum.ERROR, "act_download_failed", "下载行为数据失败", user, notificationId);
                response.setStatus(201);  //文件不存在
                try {
                    response.setHeader("message", URLEncoder.encode("该应用无行为数据,下载失败", "utf-8"));
                } catch (UnsupportedEncodingException e) {
                    LOG.error("编码错误", e);
                }
                return;
            }
            HttpUtils.copyFile(file, request, response);
        } catch (Exception ex) {
            LOG.error("行为数据下载失败", ex);
        } finally {
        	if(new File(reportDir.getAbsolutePath()).exists()){
            	try {
    				FileUtils.deleteDirectory(new File(reportDir.getAbsolutePath()));
    			} catch (IOException e) {
                    LOG.error("删除错误", e);
    			}
            }
        }
    }

    @ApiOperation(value = "查询数据标记记录（标记的历史记录）")
    @GetMapping("/mark/resultRecord")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询历史标记数据"
    )
    public BaseResponse<List<TPrivacyResultMark>> getMarkResultRecord(@ApiParam(value = "数据（当前记录的）Id") @RequestParam(value = "bId", required = true) Long bId,
    		@ApiParam(value = "数据类型 1.传输个人信息 2.存储个人信息  3.164号文法规 4. 191号文法规 5. 35273号文法规 6. 41391号文法规") @RequestParam(value = "resultType", required = true) Integer resultType) {
        BaseResponse<List<TPrivacyResultMark>> baseResponse = new BaseResponse<>();
        baseResponse.setData(iPrivacyDetectionService.getMarkResultRecord(bId, LawResultTypeEnum.getItem(resultType)));
        return baseResponse;
    }


    /**
     * 返回应用行为中行为权限和应用行为互相关联的条件数据
     * @param documentId
     * @param behaviorStage
     * @return
     * @throws Exception
     * @date 2021/6/8
     */
    @ApiOperation(value = "V2.5详情-->应用行为-->行为权限跟应用行为关联条件")
    @GetMapping(value = "/behaviorApplyNameAndPermission")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询行为权限跟应用行为关联的条件数据"
    )
    public BaseResponse<BehaviorPermissionVo> behaviorApplyNameAndPermission(@RequestParam("documentId") String documentId,
                                                                             @RequestParam(value = "behaviorStage", required = false) Integer behaviorStage,
                                                                             @RequestParam("packageName") String packageName) {
        BaseResponse<BehaviorPermissionVo> response = new BaseResponse<>();
        response.setData(iPrivacyDetectionService.getBehaviorApplyNameAndPermission(documentId, behaviorStage,packageName));
        return response;
    }

    /**
     * 返回应用行为中行为权限和应用行为互相关联的条件数据
     * @param documentId
     * @return
     * @throws Exception
     * @date 2021/6/8
     */
    @ApiOperation(value = "V3.3详情-->法规详情应用行为-->行为权限跟应用行为关联条件")
    @GetMapping(value = "/lawDetailBehaviorApplyNameAndPermission")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询法规详情行为权限跟应用行为关联的条件数据"
    )
    public BaseResponse<BehaviorPermissionVo> lawDetailBehaviorApplyNameAndPermission(@RequestParam("documentId") String documentId,
                                                                             @RequestParam(value = "dataType") Integer dataType,
                                                                             @RequestParam("itemNo") String itemNo) {
        BaseResponse<BehaviorPermissionVo> response = new BaseResponse<>();
        response.setData(iPrivacyDetectionService.getLawDetailBehaviorApplyNameAndPermission(documentId, dataType, itemNo));
        return response;
    }

    /**
     * v2.5版本根据不同的筛选条件查询应用行为数据，平铺展示
     * 增加分页展示
     * @param behaviorQuery
     * @return
     */
    @ApiOperation(value = "V2.5详情-->应用行为--> 应用行为（7.1沙箱）详细数据列表-->所有数据平铺展示")
    @PostMapping("/behaviors/nougat/detail/newData")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询应用行为数据"
    )
    public BaseResponse<TPrivacyActionNougatVO> nougatBehaviorsDetailNewData(@RequestBody BehaviorQuery behaviorQuery) {
        BaseResponse<TPrivacyActionNougatVO> baseResponse = new BaseResponse<>();
        baseResponse.setData(privacyActionNougatService.findByTaskIdAndActionIdAndQuerys(behaviorQuery));
        return baseResponse;
    }

    /**
     * v2.5新增通信行为页面筛选条件
     * @param documentId
     * @param behaviorStage
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "V2.5详情-->通信行为-->筛选条件")
    @GetMapping(value = "/behaviorNoun")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询通信行为筛选条件"
    )
    public BaseResponse<Map<String,List<String>>> behaviorNoun(@RequestParam("documentId") String documentId,
                                                               @RequestParam(value = "behaviorStage", required = false) Integer behaviorStage) {
        BaseResponse<Map<String,List<String>>> response = new BaseResponse<>();
        response.setData(privacyOutsideAddressService.getBehaviorNoun(documentId, behaviorStage));
        return response;
    }

    /**
     * v2.5版本通信行为数据展示，包含页面上所有的筛选条件
     * 增加分页展示
     * @param behaviorQuery
     * @return
     * @Date 2021/6/2
     */
    @ApiOperation(value = "V2.5详情-->通信行为-->通信行为平铺展示")
    @PostMapping("/outside/address/tileShow")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询通信行为数据"
    )
    public BaseResponse<TPrivacyOutsideAddressVO> tileShowOutsideAddress(@RequestBody BehaviorQuery behaviorQuery) {
        BaseResponse<TPrivacyOutsideAddressVO> baseResponse = new BaseResponse<>();
        baseResponse.setData(privacyOutsideAddressService.findOutsideDataByTaskId(behaviorQuery));
        return baseResponse;
    }

    /**
     *v2.5新增传输个人信息触发主体筛选条件
     * @param documentId
     * @param behaviorStage
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "V2.5详情-->传输个人信息-->触发主体筛选条件")
    @GetMapping(value = "/behaviorTransmission")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询传输个人信息筛选条件"
    )
    public BaseResponse<List<String>> behaviorTransmission(@RequestParam("documentId") String documentId,
                                                           @RequestParam(value = "behaviorStage", required = false) Integer behaviorStage) {
        BaseResponse<List<String>> response = new BaseResponse<>();
        response.setData(privacySensitiveWordService.getBehaviorTransmission(documentId, behaviorStage));
        return response;
    }

    /**
     * v2.5新增传输个人信息信息分类筛选条件
     * @param documentId
     * @param behaviorStage
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "V2.5详情-->传输个人信息-->信息分类筛选条件")
    @GetMapping(value = "/behaviorMessageType")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询传输个人信息筛选条件"
    )
    public BaseResponse<List<String>> behaviorMessageType(@RequestParam("documentId") String documentId,
                                                          @RequestParam(value = "behaviorStage", required = false) Integer behaviorStage) {
        BaseResponse<List<String>> response = new BaseResponse<>();
        response.setData(privacySensitiveWordService.getBehaviorBehaviorMessageType(documentId, behaviorStage));
        return response;
    }

    /**
     * v2.5新增传输个人信息个人信息筛选条件
     * @param documentId
     * @param behaviorStage
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "V2.5详情-->传输个人信息-->个人信息筛选条件")
    @GetMapping(value = "/behaviorPersonalMessage")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询传输个人信息筛选条件"
    )
    public BaseResponse<List<String>> behaviorPersonalMessage(@RequestParam("documentId") String documentId,
                                                              @RequestParam(value="behaviorStage", required = false) Integer behaviorStage) {
        BaseResponse<List<String>> response = new BaseResponse<>();
        response.setData(privacySensitiveWordService.getBehaviorPersonalMessage(documentId, behaviorStage));
        return response;
    }

    /**
     * v2.5新增个人信息与信息分类关联筛选条件
     * @param documentId
     * @param behaviorStage
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "V2.5详情-->传输个人信息-->个人信息与信息分类联合筛选条件")
    @GetMapping(value = "/behaviorPersonalMessageAndType")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询传输个人信息筛选条件"
    )
    public BaseResponse<PersonalMessageAndTypeVO> behaviorPersonalMessageAndType(@RequestParam("documentId") String documentId,
                                                                                 @RequestParam(value="behaviorStage", required = false) Integer behaviorStage) {
        BaseResponse<PersonalMessageAndTypeVO> response = new BaseResponse<>();
        response.setData(privacySensitiveWordService.getBehaviorPersonalMessageAndType(documentId, behaviorStage));
        return response;
    }
    /**
     * v2.5版本传输个人信息数据展示，包含所有筛选条件
     * 增加分页展示
     * @param behaviorQuery
     * @return
     */
    @ApiOperation(value = "V2.5详情-->传输个人信息-->传输个人信息平铺展示")
    @PostMapping("/transfer/personal/word/show")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询传输个人信息数据"
    )
    public BaseResponse<TPrivacySensitiveWordVO> findSensitiveWordTileShow(@RequestBody BehaviorQuery behaviorQuery) {
        Long userId = getCurrentUser().getUserId();//获取当前用户信息进行业务判断
        BaseResponse<TPrivacySensitiveWordVO> baseResponse = new BaseResponse<>();
        baseResponse.setData(privacySensitiveWordService.findByTaskIdAndAllQuerys(behaviorQuery,userId));
        return baseResponse;
    }

    /**
     *v2.5新增存储个人信息触发主体筛选条件
     * @param documentId
     * @param behaviorStage
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "V2.5详情-->存储个人信息-->触发主体筛选条件")
    @GetMapping(value = "/behaviorStorage")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询存储个人信息筛选条件"
    )
    public BaseResponse<List<String>> behaviorStorage(@RequestParam("documentId") String documentId,
                                                      @RequestParam(value="behaviorStage", required = false) Integer behaviorStage) {
        BaseResponse<List<String>> response = new BaseResponse<>();
        response.setData(privacySharedPrefsService.getBehaviorStorage(documentId, behaviorStage));
        return response;
    }

    /**
     * v2.5新增存储个人信息信息分类筛选条件
     * @param documentId
     * @param behaviorStage
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "V2.5详情-->存储个人信息-->信息分类筛选条件")
    @GetMapping(value = "/behaviorStorageMessageType")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询存储个人信息筛选条件"
    )
    public BaseResponse<List<String>> behaviorStorageMessageType(@RequestParam("documentId") String documentId,
                                                                 @RequestParam(value="behaviorStage", required = false) Integer behaviorStage) {
        BaseResponse<List<String>> response = new BaseResponse<>();
        response.setData(privacySharedPrefsService.getBehaviorStorageMessageType(documentId, behaviorStage));
        return response;
    }

    /**
     * v2.5新增存储个人信息个人信息筛选条件
     * @param documentId
     * @param behaviorStage
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "V2.5详情-->存储个人信息-->个人信息筛选条件")
    @GetMapping(value = "/behaviorStoragePersonalMessage")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询存储个人信息筛选条件"
    )
    public BaseResponse<List<String>> behaviorStoragePersonalMessage(@RequestParam("documentId") String documentId,
                                                                     @RequestParam(value="behaviorStage", required = false) Integer behaviorStage) {
        BaseResponse<List<String>> response = new BaseResponse<>();
        response.setData(privacySharedPrefsService.getBehaviorStoragePersonalMessage(documentId, behaviorStage));
        return response;
    }

    /**
     * v2.5新增存储个人信息个人信息与信息分类关联筛选条件
     * @param documentId
     * @param behaviorStage
     * @return
     * @throws Exception
     * @date 2021/6/8 14:13:02
     */
    @ApiOperation(value = "V2.5详情-->存储个人信息-->个人信息与信息分类联合筛选条件")
    @GetMapping(value = "/behaviorStoragePersonalMessageAndType")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询存储个人信息筛选条件"
    )
    public BaseResponse<StoragePersonalMessageAndTypeVO> behaviorStoragePersonalMessageAndType(@RequestParam("documentId") String documentId,
                                                                                               @RequestParam(value="behaviorStage", required = false) Integer behaviorStage) {
        BaseResponse<StoragePersonalMessageAndTypeVO> response = new BaseResponse<>();
        response.setData(privacySharedPrefsService.getBehaviorStoragePersonalMessageAndType(documentId, behaviorStage));
        return response;
    }

    /**
     * v2.5版本存储个人信息数据展示，包含所有筛选条件
     * @param behaviorQuery
     * @return
     */
    @ApiOperation(value = "V2.5详情-->存储个人信息-->存储个人信息平铺展示")
    @PostMapping("/sharedPrefs/personal/word/show")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询存储个人信息数据"
    )
    public BaseResponse<TPrivacySharedPrefsVO> findSharedPrefsTileShow(@RequestBody BehaviorQuery behaviorQuery) {
        Long userId = getCurrentUser().getUserId();//获取当前用户信息进行业务判断
        BaseResponse<TPrivacySharedPrefsVO> baseResponse = new BaseResponse<>();
        baseResponse.setData(privacySharedPrefsService.findByTaskIdAndBehaviorStageAndQuerys(behaviorQuery,userId));
        return baseResponse;
    }

    @ApiOperation(value = "上传文件到服务器")
    @PostMapping("/uploadFile")
    public BaseResponse<PrivacyDetectionUploadFileVO> uploadFile(@RequestParam("file") MultipartFile file,
                                                          @RequestParam(value = "taskId", required = true) Long taskId) {
        BaseResponse<PrivacyDetectionUploadFileVO> response = new BaseResponse<>();
        try {
            response.setData(iPrivacyDetectionService.uploadFile(file, taskId));
            response.setMessage("上传成功");
        } catch (Exception e) {
            response.setStatus(HttpStatusEnum.FAIL.getValue());
            response.setMessage("上传失败");
            LOG.error("上传失败", e);
        }
        return response;
    }

    @GetMapping(value = "/getIosAutoDetectionRemoteConfig")
    @ApiOperation(value = "获取Ios自动检测远程工具配置")
    public BaseResponse<IosRemoteToolConfig> getIosAutoDetectionRemoteConfig(@RequestParam(value = "taskId", required = true) Long taskId) {
        BaseResponse<IosRemoteToolConfig> response = new BaseResponse<>();
        response.setData(iPrivacyDetectionService.getIosAutoDetectionRemoteConfig(taskId));
        return response;
    }

    /**
     * v2.5版本 应用行为、通信行为、传输个人信息、存储个人信息点操作展示详细数据公共接口
     * @param id
     * @return
     */
    @ApiOperation(value = "v2.5详情-->点击操作展示数据详情查询公共接口")
    @GetMapping("/behaviors/detail/data")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "查询应用行为/通讯行为/传输个人信息/存储个人信息详情数据"
    )
    public BaseResponse<Object> getActionDetailData(@RequestParam(value = "id") Long id, @RequestParam("packageName") String packageName,@RequestParam(value = "taskId") Long taskId ,@ApiParam(value = "1：应用行为；2：通信行为；3：传输个人信息行为；4：存储个人信息行为")@RequestParam(value = "type") Integer type){
        BaseResponse<Object> baseResponse = new BaseResponse<>();
        baseResponse.setData(getActionDetailDataService.getActionDetailDataById(id,packageName,type,taskId));
        return baseResponse;
    }

    /**
     * 深度检测应用行为点操作展示详细数据接口
     * @param id
     * @return
     */
    @ApiOperation(value = "v2.5详情-->根据行为id查询深度检测实时日志详情")
    @GetMapping("/behaviors/dynamic/log/detail")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "根据行为id查询深度检测实时日志详情"
    )
    public BaseResponse<RealTimeBehaviorLog> getDynamicLogDetail(@RequestParam(value = "id") String id, @RequestParam(value = "taskId") Long taskId) throws Throwable {
        BaseResponse<RealTimeBehaviorLog> baseResponse = new BaseResponse<>();
        baseResponse.setData(getActionDetailDataService.getActionDetail(id,taskId));
        return baseResponse;
    }

    /**
     * 根据行为查询深度检测实时日志
     * @param actionIds
     * @param taskId
     * @return
     */
    @ApiOperation(value = "v2.5详情-->根据行为查询深度检测实时日志")
    @GetMapping("/behaviors/dynamic/log/list")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "根据行为id查询深度检测实时日志详情"
    )
    public BaseResponse<List<RealTimeBehaviorLog>> getDynamicLogList(@ApiParam(value = "行为id，传多个是用,分割 例如 1,2,3") @RequestParam(value = "actionIds") String actionIds,
                                                                     @RequestParam(value = "taskId") Long taskId,
                                                                     @ApiParam(value = "行为阶段，传多个是用,分割 例如 1,2,3") @RequestParam(value = "behaviorStage", required = false) String behaviorStage,
                                                                     @ApiParam(value = "是否属于个人信息相关 1 是， 0 否，，传多个是用,分割 例如 0,1") @RequestParam(value = "isPersonal", required = false) String isPersonal){
        BaseResponse<List<RealTimeBehaviorLog>> baseResponse = new BaseResponse<>();
        List<Long> actionIdList = Arrays.stream(actionIds.split(","))
                .filter(StringUtils::isNotBlank)
                .map(Long::parseLong)
                .collect(Collectors.toList());
        List<BehaviorStageEnum> behaviorStageList = behaviorStage == null ? Collections.emptyList() : Arrays.stream(behaviorStage.split(","))
                .filter(StringUtils::isNotBlank)
                .map(p -> BehaviorStageEnum.getItem(Integer.parseInt(p)))
                .collect(Collectors.toList());
        List<PrivacyStatusEnum> isPersonalList = isPersonal == null ? Collections.emptyList() : Arrays.stream(isPersonal.split(","))
                .filter(StringUtils::isNotBlank)
                .map(p -> PrivacyStatusEnum.getItem(Integer.parseInt(p)))
                .collect(Collectors.toList());
        baseResponse.setData(getActionDetailDataService.getActionListInActionIds(taskId, actionIdList, isPersonalList, behaviorStageList));
        return baseResponse;
    }

    /**
     * 根据行为查询深度检测实时日志
     * @return
     */
    @ApiOperation(value = "v2.6.2详情-->分页获取深度检测实时日志")
    @PostMapping(value = "/behaviors/dynamic/log/findListByPage", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "分页获取深度检测实时日志"
    )
    public BaseResponse<PageInfo<RealTimeBehaviorLog>> findDynamicLogListByPage(@RequestBody TaskLogQuery query) {
        BaseResponse<PageInfo<RealTimeBehaviorLog>> baseResponse = new BaseResponse<>();
        baseResponse.setData(getActionDetailDataService.getDynamicLogListByPage(query));
        return baseResponse;
    }

    /**
     * 深度检测已有日志的行为列表
     * @param taskId
     * @return
     */
    @ApiOperation(value = "v2.5详情-->深度检测实时已产生的行为类型列表")
    @GetMapping("/behaviors/dynamic/action/type/list")
    @OperateLog(moduleName = "隐私检测模块",operateName = "获取深度检测实时行为列表数据")
    public BaseResponse<List<TActionNougat>> getDynamicActionTypeList(@RequestParam(value = "taskId") Long taskId){
        BaseResponse<List<TActionNougat>> baseResponse = new BaseResponse<>();
        baseResponse.setData(getActionDetailDataService.getDynamicActionList(taskId));
        return baseResponse;
    }

    /**
     * 深度检测应用行为点操作展示详细数据接口
     * @param id
     * @return
     */
    @ApiOperation(value = "v2.5详情-->根据行为id查询深度检测实时日志详情")
    @GetMapping("/behaviors/ios/dynamic/log/detail")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "根据行为id查询深度检测实时日志详情"
    )
    public BaseResponse<IOSRealTimeLog> getIosDynamicLogDetail(@RequestParam(value = "id") String id, @RequestParam(value = "taskId") Long taskId) throws Throwable {
        BaseResponse<IOSRealTimeLog> baseResponse = new BaseResponse<>();
        baseResponse.setData(getActionDetailDataService.getIosDetailLog(id,taskId));
        return baseResponse;
    }


    /**
     * 根据行为查询深度检测实时日志
     * @return
     */
    @ApiOperation(value = "v2.6.2详情-->根据行为查询深度检测实时日志")
    @PostMapping("/behaviors/ios/dynamic/log/list")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "根据行为id查询深度检测实时日志详情"
    )
    public BaseResponse<List<IOSRealTimeLog>> getIosDynamicLogList(@RequestBody IosLogQuery query){
        BaseResponse<List<IOSRealTimeLog>> baseResponse = new BaseResponse<>();
        baseResponse.setData(getActionDetailDataService.getIosDetailLogList(query.getTypes(), query.getBehaviorStage(), query.getIsPersonal(), query.getTaskId()));
        return baseResponse;
    }

    /**
     * 根据行为查询深度检测实时日志
     * @return
     */
    @ApiOperation(value = "v2.6.2详情-->分页获取深度检测实时日志")
    @PostMapping(value = "/behaviors/ios/dynamic/log/findListByPage", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "分页获取深度检测实时日志",
            value = "分页获取深度检测实时日志"
    )
    public BaseResponse<PageInfo<IOSRealTimeLog>> findIosDynamicLogListByPage(@RequestBody TaskLogQuery query) {
        BaseResponse<PageInfo<IOSRealTimeLog>> baseResponse = new BaseResponse<>();
        baseResponse.setData(getActionDetailDataService.getIosDetailLogListByPage(query));
        return baseResponse;
    }

    /**
     * 深度检测已有日志的行为列表
     * @param taskId
     * @return
     */
    @ApiOperation(value = "v2.5详情-->深度检测实时已产生的行为类型列表")
    @GetMapping("/behaviors/ios/dynamic/action/type/list")
    @OperateLog(moduleName = "隐私检测模块",operateName = "获取深度检测实时行为列表数据")
    public BaseResponse<List<String>> getIosDynamicActionTypeList(@RequestParam(value = "taskId") Long taskId){
        BaseResponse<List<String>> baseResponse = new BaseResponse<>();
        baseResponse.setData(getActionDetailDataService.getIosDynamicActionList(taskId));
        return baseResponse;
    }

    /**
     * 深度检测应用行为点操作展示详细数据接口
     * @param id
     * @return
     */
    @ApiOperation(value = "v2.5详情-->根据行为id查询深度检测的网络日志详情")
    @GetMapping("/behaviors/net/log/detail")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "根据行为id查询深度检测网络日志详情"
    )
    public BaseResponse<RealTimeNetLog> getIosNetLogDetail(@RequestParam(value = "id") String id, @RequestParam(value = "taskId") Long taskId) throws Throwable {
        BaseResponse<RealTimeNetLog> baseResponse = new BaseResponse<>();
        baseResponse.setData(getActionDetailDataService.getNetLog(id,taskId));
        return baseResponse;
    }

    /**
     * 根据行为查询深度检测实时日志
     * @return
     */
    @ApiOperation(value = "v2.6.2详情-->分页获取深度检测的网络日志")
    @PostMapping(value = "/behaviors/net/log/findListByPage", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "分页获取深度检测网络日志"
    )
    public BaseResponse<PageInfo<RealTimeNetLog>> findIosNetLogListByPage(@RequestBody TaskLogQuery query) {
        BaseResponse<PageInfo<RealTimeNetLog>> baseResponse = new BaseResponse<>();
        baseResponse.setData(getActionDetailDataService.getNetLogListByPage(query));
        return baseResponse;
    }

    @ApiOperation(value = "v3.0.0--->记录深度检测摇一摇参数")
    @PostMapping(value = "/deep/saveShakeValue",consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
    @OperateLog(
            moduleName = "隐私检测模块",
            operateName = "记录深度检测摇一摇参数"
    )
    public BaseResponse<List<TDeepShakeValue>> saveDeepShakeValue(@RequestBody ShakeValueQuery query){
        BaseResponse<List<TDeepShakeValue>> baseResponse = new BaseResponse<>();
        baseResponse.setData(iPrivacyDetectionService.saveDeepShakeValue(query));
        return baseResponse;
    }

    @ApiOperation(value = "v3.0.0--->获取深度检测摇一摇参数")
    @OperateLog(
            moduleName = "隐私检测模块",
            operateName = "获取深度检测摇一摇参数"
    )
    @GetMapping("/deep/getShakeValue/{taskId}")
    public BaseResponse<List<TDeepShakeValue>> getDeepShakeValue(@PathVariable("taskId")Long taskId){
        BaseResponse<List<TDeepShakeValue>> baseResponse = new BaseResponse<>();
        baseResponse.setData(iPrivacyDetectionService.getShakeValueData(taskId));
        return baseResponse;
    }

    @ApiOperation(value = "v3.0.0--->更新摇一摇是否触发跳转状态")
    @OperateLog(
            moduleName = "隐私检测模块",
            operateName = "更新深度检测摇一摇触发跳转状态"
    )
    @PostMapping(value = "/deep/updateShakeStatus",consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse updateShakeStatus(@RequestBody ShakeValueQuery query){
        BaseResponse baseResponse = new BaseResponse();
        int result = iPrivacyDetectionService.updateShakeStatus(query.getId(),query.getFlag(),query.getIsViolation(),query.getDescription());
        if(result == 0){
            baseResponse.setStatus(201);
            baseResponse.setMessage("更新失败!");
        }else{
            baseResponse.setStatus(200);
            baseResponse.setMessage("更新成功!");
        }
        return baseResponse;
    }
    
    @ApiOperation(value = "v3.0.0--->获取当前用户的检测中任务数量")
    @GetMapping(value = "/task/findAppletDynamicDetectionCount/{terminalType}",consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse findAppletDynamicDetectionCount(@PathVariable("terminalType") Integer terminalType){
        BaseResponse baseResponse = new BaseResponse();
        int result = taskService.findAppletDynamicDetectionCount(getCurrentUser().getUserId(), terminalType);
        baseResponse.setData(result);
        return baseResponse;
    }

    @ApiOperation(value = "v3.2.0--->记录深度检测真实传感器摇一摇参数")
    @PostMapping(value = "/deep/saveHardwareSensorShakeValue",consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
    @OperateLog(
            moduleName = "隐私检测模块",
            operateName = "记录深度检测真实传感器摇一摇参数"
    )
    public BaseResponse<List<THardwareSensorShakeValue>> saveHardwareSensorShakeValue(@Valid @RequestBody HardwareSensorShakeValueQuery query){
        BaseResponse<List<THardwareSensorShakeValue>> baseResponse = new BaseResponse<>();
        baseResponse.setData(iPrivacyDetectionService.saveHardwareSensorShakeValue(query));
        return baseResponse;
    }

    @ApiOperation(value = "v3.2.0--->获取深度检测真实传感器摇一摇参数")
    @OperateLog(
            moduleName = "隐私检测模块",
            operateName = "获取深度检测真实传感器摇一摇参数"
    )
    @GetMapping("/deep/getHardwareSensorShakeValueData/{taskId}")
    public BaseResponse<List<THardwareSensorShakeValue>> getHardwareSensorShakeValueData(@PathVariable("taskId") Long taskId){
        BaseResponse<List<THardwareSensorShakeValue>> baseResponse = new BaseResponse<>();
        baseResponse.setData(iPrivacyDetectionService.getHardwareSensorShakeValueData(taskId));
        return baseResponse;
    }

    /**
     * 根据行为查询深度检测实时日志
     * @return
     */
    @ApiOperation(value = "v3.2.0-->分页获取深度检测摇一摇真实传感器日志")
    @PostMapping(value = "/behaviors/sensor/log/findSensorLogListByPage", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "分页获取深度检测网络日志"
    )
    public BaseResponse<PageInfo<AndroidSensorLog>> findSensorLogListByPage(@RequestBody TaskLogQuery query) {
        BaseResponse<PageInfo<AndroidSensorLog>> baseResponse = new BaseResponse<>();
        baseResponse.setData(getActionDetailDataService.getSensorLogListByPage(query));
        return baseResponse;
    }


    /**
     * 根据行为查询深度检测实时日志
     * @return
     */
    @ApiOperation(value = "v3.3详情-->清除深度检测日志")
    @PostMapping(value = "/behaviors/ios/dynamic/log/clear", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "清除深度检测日志"
    )
    public BaseResponse<PageInfo<RealTimeNetLog>> clearDynamicLog(@RequestBody TaskLogClearQuery query) {
        getActionDetailDataService.cleanDynamicAction(query.getTaskId());
        return new BaseResponse<>();
    }

    @ApiOperation(value = "查询minSdkVersion给前端进行弹窗提醒")
    @GetMapping(value = "/remind/message/{id}")
    @OperateLog(
            moduleName = "隐私检测模块",
            operateName = "查询应用参数"
    )
    public BaseResponse<String> getRemindMessage(@ApiParam(value = "资产id") @PathVariable(value = "id") Long id){
        BaseResponse<String> response = new BaseResponse();
        response.setData(iPrivacyDetectionService.getAppMinSdk(id));
        return response;
    }

    /**
     * 重新检测静态检测任务
     *
     * @param taskId 任务ID
     * @return
     * @throws IjiamiCommandException
     */
    @PostMapping(value = "/startAiDetection/{taskId}")
    @ApiOperation(value = "开始ai智能检测")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "开始ai智能检测"
    )
    public BaseResponse<Long> startAiDetection(@ApiParam(value = "任务id") @PathVariable Long taskId) throws IjiamiCommandException {
        BaseResponse<Long> response = new BaseResponse<>();
        response.setData(taskService.startAiDetection(taskId));
        return response;
    }

    @ApiOperation(value = "获取可检测的法规")
    @GetMapping("/getLawsParentName/{terminalType}")
    @OperateLog(
            moduleName ="获取可检测的法规",
            operateName = "获取可检测的法规"
    )
    public BaseResponse<List<PrivacyLawsVO>> selectPrivacyLawsParentName(@PathVariable("terminalType") Integer terminalType) {
        BaseResponse<List<PrivacyLawsVO>> response = new BaseResponse<>();
        response.setData(taskReportService.selectPrivacyLawsParentName(terminalType));
        return response;
    }

    @ApiOperation(value = "发起任务前检查是否满足检测配置")
    @PostMapping(value = "/preTaskConditionCheck")
    public BaseResponse<TaskConditionCheckResult> preTaskConditionCheck(@RequestBody CheckStartTaskVO checkStartTaskVO) throws IjiamiApplicationException {
        return ResponseHelper.successData("", detectionConfigService.preTaskConditionCheck(getCurrentUser().getUserId(), checkStartTaskVO.getAssetsIds()));
    }

    @ApiOperation(value = "用户是否能进行任务检测")
    @PostMapping(value = "/userTaskConditionCheck")
    public BaseResponse<TaskConditionCheckResult> userTaskConditionCheck(@RequestBody CheckUserTaskConditionVO checkUserTaskConditionVO) throws IjiamiApplicationException {
        return ResponseHelper.successData("",
                detectionConfigService.userTaskConditionCheck(getCurrentUser().getUserId(), TerminalTypeEnum.getAndValid(checkUserTaskConditionVO.getTerminalType())));
    }

    @ApiOperation(value = "获取当前任务详情", notes = "获取当前任务详情")
    @GetMapping(value = "/getTaskById/{taskId}")
    @OperateLog(
            moduleName ="隐私检测模块",
            operateName = "获取当前任务详情"
    )
    public BaseResponse<TaskDetailVO> getTaskById(@PathVariable("taskId") Long taskId) {
        BaseResponse<TaskDetailVO> response = new BaseResponse<>();
        response.setData(taskService.getTaskDetailByTaskId(taskId));
        response.setMessage("查询成功！");
        return response;
    }


    /**
     * 获取专家检测任务列表
     *
     * @param taskQuery
     * @return BaseResponse<TaskVO>
     */
    @RequestLimit(limit = 3, timeMillis = 1000)
    @ApiOperation(value = "获取专家检测任务列表", notes = "获取专家检测任务列表")
    @PostMapping("/findExpertTaskByPage")
    @DataScope("taskList")
    public BaseResponse<TaskVO> findExpertTaskByPage(@RequestBody TaskQuery taskQuery) {
        BaseResponse<TaskVO> response = new BaseResponse<>();
        if (!isAdmin()) {
            taskQuery.setUserId(getCurrentUser().getUserId());
        }
        response.setData(taskService.findExpertTaskByPage(taskQuery));
        response.setMessage("查询成功!");
        return response;
    }
}
