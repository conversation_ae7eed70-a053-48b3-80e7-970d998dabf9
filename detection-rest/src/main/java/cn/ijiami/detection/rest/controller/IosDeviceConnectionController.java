package cn.ijiami.detection.rest.controller;

import java.io.IOException;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.ijiami.detection.VO.IOSConnectDeviceInfoVO;
import cn.ijiami.detection.VO.QRCodeVO;
import cn.ijiami.detection.controller.DetectionBaseController;
import cn.ijiami.detection.service.api.IOSDeviceConnectionService;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.common.response.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping(value = "/api/quickmark")
@Api(tags = "ios二维码接口")
public class IosDeviceConnectionController extends DetectionBaseController {

    @Autowired
    private IOSDeviceConnectionService iosDeviceConnectionService;

    @ApiOperation(value = "ios扫码连接的二维码", notes = "")
    @GetMapping(value = "/getQr/{taskId}")
    public BaseResponse<QRCodeVO> qrCode(@PathVariable("taskId") Long taskId) throws IOException {
        BaseResponse<QRCodeVO> response = new BaseResponse<>();
        response.setData(iosDeviceConnectionService.qrCode(getCurrentUser().getUserId(), taskId));
        return response;
    }

    @ApiOperation(value = "ios扫码后的设备信息回调", notes = "")
    @PostMapping(value = "/callback", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<String> callback(@RequestBody @Valid IOSConnectDeviceInfoVO deviceInfoVO)
            throws IjiamiApplicationException {
        BaseResponse<String> response = new BaseResponse<>();
        iosDeviceConnectionService.callback(deviceInfoVO);
        return response;
    }

}
