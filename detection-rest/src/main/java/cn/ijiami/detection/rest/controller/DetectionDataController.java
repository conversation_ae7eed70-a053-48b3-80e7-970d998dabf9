package cn.ijiami.detection.rest.controller;

import cn.ijiami.detection.controller.DetectionBaseController;
import cn.ijiami.detection.query.DeleteRealtimeLogs;
import cn.ijiami.detection.result.AppDetailsResult;
import cn.ijiami.detection.service.DetectionDataService;
import cn.ijiami.detection.utils.HttpUtils;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.common.response.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TaskDataExportController.java
 * @Description 任务数据导出
 * @createTime 2024年05月29日 18:10:00
 */
@RestController
@RequestMapping(value = "/api/detection/data")
@Api(value = "/api/detection/data", tags = "深度检测数据")
public class DetectionDataController extends DetectionBaseController {


    @Autowired
    private DetectionDataService detectionDataService;

    @ApiOperation(value = "深度检测实时的sdk数据导出", notes = "sdk数据导出")
    @GetMapping(value = "/realtime/sdk/export")
    public void sdkExport(@ApiParam(value = "任务id") @RequestParam("taskId") Long taskId, HttpServletRequest request, HttpServletResponse response){
        HttpUtils.copyFile(detectionDataService.exportRealtimeSdk(getCurrentUser(), taskId), request, response);
    }

    @ApiOperation(value = "深度检测实时的行为数据导出", notes = "行为数据导出")
    @GetMapping(value = "/realtime/behavior/export")
    public void behaviorExport(@ApiParam(value = "任务id") @RequestParam("taskId") Long taskId, HttpServletRequest request, HttpServletResponse response){
        HttpUtils.copyFile(detectionDataService.exportRealtimeBehavior(getCurrentUser(), taskId), request, response);
    }

    @ApiOperation(value = "删除实时的深度检测行为", notes = "删除实时的深度检测行为")
    @PostMapping(value = "/realtime/deleteActionLogs", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<String> deleteActionLogs(@RequestBody DeleteRealtimeLogs deleteRealtimeLogs)
            throws IjiamiApplicationException {
        BaseResponse<String> response = new BaseResponse<>();
        detectionDataService.deleteActionLogs(deleteRealtimeLogs.getTaskId(), deleteRealtimeLogs.getLogIds());
        response.setData("删除成功");
        return response;
    }

    @ApiOperation(value = "应用详情", notes = "应用详情")
    @GetMapping(value = "/realtime/appDetails")
    public BaseResponse<AppDetailsResult> appDetails(@ApiParam(value = "任务id") @RequestParam("taskId") Long taskId) throws IjiamiApplicationException {
        return BaseResponse.SUCCESS(detectionDataService.getAppDetails(taskId));
    }

}
