package cn.ijiami.detection.rest.controller;


import java.io.IOException;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.ijiami.detection.controller.DetectionBaseController;
import cn.ijiami.detection.service.api.IAuthTokenService;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
/**
 * @ClassName AuthenticationController.java
 * @Description oauth2单点登录相关
 * @createTime 2025年02月27日 17:08:00
 */
@RestController
@RequestMapping("/manager/authentication/common")
@Api(value = "/manager/authentication/common", tags = "oauth2单点登录相关")
public class AuthenticationCommonController extends DetectionBaseController {

    private static final Logger logger = LoggerFactory.getLogger(AuthenticationCommonController.class);

    @Autowired
    private IAuthTokenService authTokenService;


    /**
     * 接收code,保存或者查询用户,生成token,保存到Cookie
     * 并进行登录认证跳转
     *
     * @param code
     * @param response
     * @throws IjiamiApplicationException
     */
    @ApiOperation(value = "接收统一认证平台返回的code", notes = "并接收第三方统一认证平台返回的AccessToken信息")
    @RequestMapping(value = "/receiveCode")
    public void receiveCode(@RequestParam("code") String code, HttpServletRequest request, HttpServletResponse response) throws IjiamiApplicationException {
        logger.info("=======接收统一认证平台返回的code=========code:  [{}]", code);
        if (StringUtils.isEmpty(code)) {
            throw new IjiamiApplicationException("接收统一认证平台返回的code为空");
        }
        ServletOutputStream output = null;
        try {
            output = response.getOutputStream();
            String authHandler = authTokenService.getTokenFromCommonCode(code, request, response);
            if (StringUtils.isNotBlank(authHandler)) {
                logger.info(authHandler);
                output.write(authHandler.getBytes());
                output.flush();
            }
        } catch (Exception e) {
            e.getMessage();
        } finally {
            if (null != output) {
                try {
                    output.close();
                } catch (IOException e) {
                }
            }
        }
    }
}
