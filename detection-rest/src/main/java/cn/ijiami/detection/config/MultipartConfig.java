package cn.ijiami.detection.config;

import javax.servlet.MultipartConfigElement;

import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.unit.DataSize;
import org.springframework.util.unit.DataUnit;

/**
 * 上传文件资源配置
 *
 * <AUTHOR>
 */
@Configuration
public class MultipartConfig {
	
	private final IjiamiCommonProperties commonProperties;
	public MultipartConfig(IjiamiCommonProperties commonProperties) {
        this.commonProperties = commonProperties;
    }
	
    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        
        String size = commonProperties.getProperty("spring.http.multipart.maxFileSize")==null?(20480 + "MB"):commonProperties.getProperty("spring.http.multipart.maxFileSize");
        if(size.toUpperCase().contains("MB")) {
        	size = size.toUpperCase().replace("MB", "");
        }
        factory.setMaxRequestSize(DataSize.of(Long.parseLong(size), DataUnit.MEGABYTES));
//        factory.setMaxFileSize(commonProperties.getProperty("spring.http.multipart.maxFileSize")==null?(20480 + "MB"):commonProperties.getProperty("spring.http.multipart.maxFileSize"));
        return factory.createMultipartConfig();
    }
}
