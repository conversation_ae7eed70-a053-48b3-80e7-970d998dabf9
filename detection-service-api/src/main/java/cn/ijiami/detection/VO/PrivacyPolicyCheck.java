package cn.ijiami.detection.VO;

import java.util.Comparator;
import java.util.HashSet;
import java.util.Set;

import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PrivacyPolicyCheck.java
 * @Description 合规风险的结果
 * @createTime 2021年11月30日 15:47:00
 */
@Data
public class PrivacyPolicyCheck {

    private Set<BehaviorStageEnum> behaviorStageEnums = new HashSet<>();

    private boolean nonCompliance = false;

    /**
     * 获取违规原因的行为阶段
     * @return
     */
    public BehaviorStageEnum getNonComplianceBehaviorStage() {
        return behaviorStageEnums.stream().min(Comparator.comparingInt(BehaviorStageEnum::getValue))
                .orElseThrow(() -> new IjiamiRuntimeException("数据错误，没有违规阶段"));
    }

}
