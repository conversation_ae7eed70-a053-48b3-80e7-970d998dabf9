package cn.ijiami.detection.VO.ai;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.LowerCamelCaseStrategy.class)
public class FormattedPrivacyAnalysisDataVO {

    @ApiModelProperty(value = "法律条款列表")
    private List<LawCategoryItemVO> lawItems;
} 