package cn.ijiami.detection.VO.detection;

import cn.ijiami.detection.VO.PermissionVO;
import cn.ijiami.detection.VO.compliance.SdkAnalysisVO;
import cn.ijiami.detection.entity.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 第三方SDK信息VO
 *
 * <AUTHOR>
 */
@ApiModel
public class SdkVO extends TSdkLibrary implements Serializable {

    private static final long serialVersionUID = 7453227714287101091L;

    @ApiModelProperty(value = "权限列表")
    private List<PermissionVO> permissions = new ArrayList<>();

    private List<TPrivacyOutsideAddress> outsideAddresses = new ArrayList<>();
    
    /**
     * SDK行为分析
     */
    private List<TPrivacyActionNougat> actionNougatList = new  ArrayList<>();
    
    /**
     * 存储个人信息
     */
    private List<TPrivacySharedPrefs> sharedPrefs = new ArrayList<>();
    
    
    private Integer isPersonalActionNougatCount; //与个人信息相关行为（单位：种）
    private Integer isPersonalPermissionCount; //与个人信息相关权限（单位：个）	
    private Integer outsideIpCount;  //通信境外IP（单位：个）
    
    @ApiModelProperty(value = "是否为ios解析PrivacyInfo.xcprivacy文件得到数据")
    private boolean xcprivacy;

	/**
	 * 境外通讯行为
	 */
	private List<TPrivacyOutsideAddress> outsideAbroadAddresses = new ArrayList<>();

	/**
	 * 境内通讯行为
	 */
	private List<TPrivacyOutsideAddress> outsideTerrtoryAddresses = new ArrayList<>();

	/**
	 * 个人传输信息
	 */
	private List<TPrivacySensitiveWord> sensitiveWords = new ArrayList<>();

	/**
	 * 境内通讯行为
	 */
	private SdkAnalysisVO sdkAnalysisVo;

	public boolean isXcprivacy() {
		return xcprivacy;
	}

	public void setXcprivacy(boolean xcprivacy) {
		this.xcprivacy = xcprivacy;
	}

	public Integer getIsPersonalActionNougatCount() {
		return isPersonalActionNougatCount;
	}

	public void setIsPersonalActionNougatCount(Integer isPersonalActionNougatCount) {
		this.isPersonalActionNougatCount = isPersonalActionNougatCount;
	}

	public Integer getIsPersonalPermissionCount() {
		return isPersonalPermissionCount;
	}

	public void setIsPersonalPermissionCount(Integer isPersonalPermissionCount) {
		this.isPersonalPermissionCount = isPersonalPermissionCount;
	}

	public Integer getOutsideIpCount() {
		return outsideIpCount;
	}

	public void setOutsideIpCount(Integer outsideIpCount) {
		this.outsideIpCount = outsideIpCount;
	}

	public List<TPrivacySharedPrefs> getSharedPrefs() {
		return sharedPrefs;
	}

	public void setSharedPrefs(List<TPrivacySharedPrefs> sharedPrefs) {
		this.sharedPrefs = sharedPrefs;
	}

	public List<TPrivacyActionNougat> getActionNougatList() {
		return actionNougatList;
	}

	public void setActionNougatList(List<TPrivacyActionNougat> actionNougatList) {
		this.actionNougatList = actionNougatList;
	}

	public List<PermissionVO> getPermissions() {
        return permissions;
    }

    public void setPermissions(List<PermissionVO> permissions) {
        this.permissions = permissions;
    }

    public List<TPrivacyOutsideAddress> getOutsideAddresses() {
        return outsideAddresses;
    }

    public void setOutsideAddresses(List<TPrivacyOutsideAddress> outsideAddresses) {
        this.outsideAddresses = outsideAddresses;
    }

	public List<TPrivacyOutsideAddress> getOutsideAbroadAddresses() {
		return outsideAbroadAddresses;
	}

	public void setOutsideAbroadAddresses(List<TPrivacyOutsideAddress> outsideAbroadAddresses) {
		this.outsideAbroadAddresses = outsideAbroadAddresses;
	}

	public List<TPrivacyOutsideAddress> getOutsideTerrtoryAddresses() {
		return outsideTerrtoryAddresses;
	}

	public void setOutsideTerrtoryAddresses(List<TPrivacyOutsideAddress> outsideTerrtoryAddresses) {
		this.outsideTerrtoryAddresses = outsideTerrtoryAddresses;
	}

	public List<TPrivacySensitiveWord> getSensitiveWords() {
		return sensitiveWords;
	}

	public void setSensitiveWords(List<TPrivacySensitiveWord> sensitiveWords) {
		this.sensitiveWords = sensitiveWords;
	}

	public SdkAnalysisVO getSdkAnalysisVo() {
		return sdkAnalysisVo;
	}

	public void setSdkAnalysisVo(SdkAnalysisVO sdkAnalysisVo) {
		this.sdkAnalysisVo = sdkAnalysisVo;
	}
}
