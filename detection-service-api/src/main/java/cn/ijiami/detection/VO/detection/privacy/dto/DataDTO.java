package cn.ijiami.detection.VO.detection.privacy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DataDTO.java
 * @Description
 * @createTime 2023年04月03日 14:55:00
 */
@NoArgsConstructor
@Data
public class DataDTO {
    @JsonProperty("code")
    private String code;
    @JsonProperty("nowcode")
    private String nowcode;
    @JsonProperty("xcx_ver")
    private String xcxVer;
}
