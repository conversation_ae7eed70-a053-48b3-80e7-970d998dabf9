package cn.ijiami.detection.VO.detection.privacy.deserialize;


import cn.ijiami.detection.VO.detection.privacy.dto.AppletInputParamDTO;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName InputParamDeserialize.java
 * @createTime 2023年04月03日 14:59:00
 */
public class AppletInputParamDeserializer extends JsonDeserializer<AppletInputParamDTO> {

    @Override
    public AppletInputParamDTO deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
        if (p.hasTextCharacters()) {
            String json = p.getText();
            if (StringUtils.startsWith(json, "{")) {
                return DeserializerUtils.toBean(json, AppletInputParamDTO.class);
            } else {
                return null;
            }
        } else {
            try {
                return p.readValueAs(AppletInputParamDTO.class);
            } catch (Exception e) {
                return null;
            }
        }
    }
}
