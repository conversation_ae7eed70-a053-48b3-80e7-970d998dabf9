package cn.ijiami.detection.VO.statistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectFalsePositivesApp.java
 * @Description 误报app
 * @createTime 2023年10月20日 17:09:00
 */
@Data
public class DetectFalsePositivesApp {

    @ApiModelProperty(value = "资产id", hidden = false, example = "1")
    private Long assetsId;

    @ApiModelProperty(value = "资产名称", hidden = false, example = "连连看")
    private String assetsName;

    @ApiModelProperty(value = "误报数", hidden = false, example = "1")
    private Integer detectFalsePositivesCount;

    @ApiModelProperty(value = "版本", hidden = false, example = "1.1.0")
    private String version;

}
