package cn.ijiami.detection.VO;

import cn.ijiami.detection.entity.TShakeBasicParametes;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class TShakeBasicParametesVO implements Serializable {

    private static final long serialVersionUID = -12023252L;

    @ApiModelProperty(value = "加速度列表")
    private List<TShakeBasicParametes> acceleratParametes;

    @ApiModelProperty(value = "角速度列表")
    private List<TShakeBasicParametes> gyroscopeParametes;
    @ApiModelProperty(value = "X,Y,Z加速度,综合加速度",example = "[{acceleratX: \"1\", acceleratY: \"1\", acceleratZ: \"1\" ,acceleratS:\"1.0\"}]")
    private List<Map<String,String>> resourceIds;

    @ApiModelProperty(value = "陀螺仪参数,最大角度",example = "[{gyroscopeX: \"2\", gyroscopeY: \"2\", gyroscopeZ: \"2\" , gyroscopeR:\"2\"}]")
    private List<Map<String,String>> gyroscopeIds;
}
