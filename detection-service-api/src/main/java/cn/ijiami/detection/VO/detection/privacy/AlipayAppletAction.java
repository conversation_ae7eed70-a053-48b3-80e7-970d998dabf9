package cn.ijiami.detection.VO.detection.privacy;

import cn.ijiami.detection.VO.detection.privacy.deserialize.AppletDataDeserializer;
import cn.ijiami.detection.VO.detection.privacy.dto.AlipayAppletHeadersDTO;
import cn.ijiami.detection.VO.detection.privacy.dto.AppletActionBO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AlipayAppletAction.java
 * @Description 支付宝小程序行为
 * @createTime 2023年09月13日 10:48:00
 */
@NoArgsConstructor
@Data
public class AlipayAppletAction {

    @JsonProperty("__appxCaller")
    private String appxCaller;
    @JsonProperty("__appxDomain")
    private String appxDomain;
    @JsonDeserialize(using = AppletDataDeserializer.class)
    @JsonProperty("data")
    private AppletActionBO data;
    @JsonProperty("dataType")
    private String dataType;
    @JsonProperty("enableAppxProfile")
    private Boolean enableAppxProfile;
    @JsonProperty("enableSubErrorCode")
    private Boolean enableSubErrorCode;
    @JsonProperty("headers")
    private List<AlipayAppletHeadersDTO> headers;
    @JsonProperty("method")
    private String method;
    @JsonProperty("requestTaskId")
    private Integer requestTaskId;
    @JsonProperty("responseType")
    private String responseType;
    @JsonProperty("url")
    private String url;
    private Integer behaviorStage;


}