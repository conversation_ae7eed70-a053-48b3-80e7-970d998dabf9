package cn.ijiami.detection.VO.expert;

import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class BehaviorWebInfo implements Serializable {

    private static final long serialVersionUID = 8493042773121279266L;

    /**
     * id信息
     */
    private Long id;

    /**
     * 触发时间
     */
    private Date actionTime;

    /**
     * 时间搓
     */
    private Long actionTimeStamp;

    /**
     * 行为id
     */
    private Long actionId;

    /**
     * 行为名称
     */
    private String actionName;

    /**
     * 行为信息相关
     */
    private Boolean isPersonal;

    /**
     * 堆栈信息
     */
    private String stackInfo;

    /**
     * 详情数据
     */
    private String detailsData;

    //给前端展示的主体信息
    private String executorShow;

    //行为阶段 授权前行为、前台运行行为、后台运行行为
    private BehaviorStageEnum behaviorStage;
}
