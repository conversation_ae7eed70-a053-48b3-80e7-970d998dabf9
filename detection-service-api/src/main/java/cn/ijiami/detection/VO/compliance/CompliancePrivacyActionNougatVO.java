package cn.ijiami.detection.VO.compliance;

import cn.ijiami.detection.VO.TPrivacyActionNougatVO;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/20
 */
@Data
@ApiModel(value = "行为数据VO")
public class CompliancePrivacyActionNougatVO implements Serializable {

    private static final long serialVersionUID = 3087019614631496622L;

    private Long id;

    private String name;

    @ApiModelProperty(value = "行为权限，查询的权限表取出来的")
    private String remark;

    @ApiModelProperty(value = "任务id")
    private Long taskId;

    @ApiModelProperty(value = "行为id")
    private Long actionId;

    @ApiModelProperty(value = "函数调用栈")
    private String stackInfo;

    @ApiModelProperty(value = "详细数据")
    private String detailsData;

    @ApiModelProperty(value = "行为触发时间搓")
    private Long actionTime;

    @ApiModelProperty(value = "行为触发时间")
    private Date actionDate;

    @ApiModelProperty(value = "类型 是否是使用APP前触发")
    private Boolean type;

    @ApiModelProperty(value = "行为阶段 授权前行为、前台运行行为、后台运行行为")
    private BehaviorStageEnum behaviorStage;

    @ApiModelProperty(value = "主体类型 1.APP 2.SDK")
    private Integer executorType = 2;

    @ApiModelProperty(value = "主体")
    private String executor;

    @ApiModelProperty(value = "主体包名")
    private String packageName;

    @Transient
    @ApiModelProperty(value = "调用次数")
    private Integer counter;

    @Transient
    @ApiModelProperty(value = "行为名称")
    private String actionName;

    @Transient
    @ApiModelProperty(value = "行为权限")
    private String actionPermission;

    @Transient
    @ApiModelProperty(value = "行为权限")
    private String actionPermissionAlias;

    @Transient
    @ApiModelProperty(value = "是否敏感")
    private Boolean sensitive;

    @Transient
    @ApiModelProperty(value = "详细信息")
    List<TPrivacyActionNougatVO> privacyActionNougats;
    
    @ApiModelProperty(value = "APP调用次数")
    private Integer appCounter;
    
    @ApiModelProperty(value = "SDK调用次数")
    private Integer sdkCounter;

    @ApiModelProperty(value = "APP是否敏感")
    private Boolean appSensitive;

    @ApiModelProperty(value = "SDK是否敏感")
    private Boolean sdkSensitive;

    @ApiModelProperty(value = "主体为2存储sdkId，json格式")
    private String sdkIds;

    /**
     * 是否为个人信息相关行为
     */
    private Integer isPersonal;

    //行为对应的类型名称
    private String actionTypeName;

    //给前端展示的主体信息
    private String executorShow;
}
