package cn.ijiami.detection.VO.gongxinbu;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2019/11/14 19:45
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GongxinbuVO implements Serializable {
    private static final long serialVersionUID = -2775973366700122950L;
    // 应用名称
    private String appName;

    // 包名
    private String packageName;

    // apk 大小
    private String apkSize;

    // 版本
    private String versionName;

    //编译时的安卓SDK版本
    private String targetSdkVersion;

    // 文件MD5
    private String apkMD5;

    // 签名MD5
    private String signMd5;

    // 签名
    private String signDetail;

    // 是否加固
    private String encryptDetail;

    private boolean col1 = false;

    private boolean col2 = false;

    private boolean col3 = false;

    private boolean col4 = false;

    private boolean col5 = false;

    private boolean col6 = false;

    private boolean col7 = false;

    private boolean col8 = false;
}
