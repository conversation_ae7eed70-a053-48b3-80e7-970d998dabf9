package cn.ijiami.detection.VO.statistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SdkStatistics.java
 * @Description sdk合规统计详情
 * @createTime 2022年07月06日 10:47:00
 */
@Data
public class SdkStatisticsDetail {

    @ApiModelProperty(value = "sdk名称", hidden = false, example = "连连看")
    private String sdkName;

    @ApiModelProperty(value = "sdk包名", hidden = false, example = "com.aaa.xxx")
    private String sdkPackageName;

    @ApiModelProperty(value = "厂家", hidden = false, example = "阿里巴巴")
    private String manufacturer;

    @ApiModelProperty(value = "合规风险总数", example = "1")
    private Integer nonComplianceTotalCount;

    @ApiModelProperty(value = "涉及应用问题")
    private List<String> lawItemList;

}
