package cn.ijiami.detection.VO.sdk;

import cn.hutool.core.date.DateTime;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@NoArgsConstructor
@Data
public class SdkChannelNotificationResponse {
        private Boolean success;
        private String errorCode;
        private String detailInfo;
        private String errorMsg;
        private Integer totalPage;
        private Integer pageSize;
        private Integer pageNum;
        private Integer total;
        private List<ChannelNoticationDTO> list;

        @Data
        @NoArgsConstructor
        @ToString
        public static class ChannelNoticationDTO{
            private Long id;
            private Long bulletinSpiderId;
            private String armpAppId;
            private String packageName;
            private String signMd5;
            private Integer matchType;
            private DateTime matchTime;
            private String appType;
            private String packageMd5;
            private String name;
            private String appVersion;
            private String bussinessSubjectId;
            private String bussinessSubjectName;
            private String channelId;
            private String channelName;
            private String problem;
            private String bulletinOrgan;
            private String bulletinBatchNumber;
            private String bulletinPageUrl;
            private String bulletinOffShelf;
            private List<String> status;
            private String isUpdate;
            private String latestVersion;
            private String outDownUrl;
            private DateTime bulletinTime;
            private DateTime createTime;
            private String createUserId;
            private String createUserLoginname;
            private List<DevelopSubject> developSubjectId;
            private List<CityArea> areaList;
            private Boolean existVersionApk;
            private String logoPath;
            private String bulletinAppTime;

            @Data
            @NoArgsConstructor
            @ToString
            public static class DevelopSubject{
                private String id;
                private String name;
                private String noHightLightName;
                private String clean_type;
            }
            @Data
            @NoArgsConstructor
            @ToString
            public static class CityArea{
                private Integer province_code;
                private String province_name;
                private Integer city_code;
                private String city_name;
                private Integer county_code;
                private String county_name;
                private String id;
                private String name_path;
                private String clean_type;
            }
        }
}
