package cn.ijiami.detection.VO.detection.privacy.deserialize;

import cn.ijiami.detection.bean.*;
import cn.ijiami.detection.enums.DetectionItemIdEnum;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.reflect.TypeUtils;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ResultContentDeserializer.java
 * @Description 任务数据解析
 * @createTime 2023年04月28日 15:09:00
 */
@Slf4j
public class ResultContentDeserializer extends JsonDeserializer<ResultContent> {

    @Override
    public ResultContent deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
        JsonNode treeNode = p.getCodec().readTree(p);
        if (Objects.nonNull(treeNode.get("appName"))) {
            return DeserializerUtils.toBean(treeNode, new TypeReference<AppletBaseResultContentDTO>() {
            });
        } else if (Objects.nonNull(treeNode.get("permissionList"))) {
            return DeserializerUtils.toBean(treeNode, new TypeReference<AppletPermissionResultContentDTO>() {
            });
        } else if (Objects.nonNull(treeNode.get("riskList"))) {
            return DeserializerUtils.toBean(treeNode, new TypeReference<AppletPersonalInfoRiskResultContentDTO>() {
            });
        } else if (Objects.nonNull(treeNode.get("bundleName"))) {
            return DeserializerUtils.toBean(treeNode, new TypeReference<HarmonyBaseResultContentDTO>() {
            });
        }
        return null;
    }



}
