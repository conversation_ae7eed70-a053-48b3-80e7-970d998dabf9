package cn.ijiami.detection.VO;

import javax.persistence.Column;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName LawsConclusionActionVo.java
 * @Description 行为数据
 * @createTime 2022年08月12日 17:02:00
 */
@Data
public class LawConclusionActionVO {

    @ApiModelProperty(value = "sdk名称")
    @Column(name = "sdk_name")
    private String sdkName;

    @ApiModelProperty(value = "行为")
    @Column(name = "action")
    private String action;

}
