package cn.ijiami.detection.VO;

import cn.ijiami.detection.entity.TPrivacyActionNougat;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(value = "TPrivacyActionNougatVO", description="应用行为vo")
public class TPrivacyActionNougatVO implements Serializable {

    private static final long serialVersionUID = 750353209846409885L;

    @ApiModelProperty(value = "应用行为分页列表")
    private PageInfo<TPrivacyActionNougat> pageInfo;

    public PageInfo<TPrivacyActionNougat> getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo<TPrivacyActionNougat> pageInfo) {
        this.pageInfo = pageInfo;
    }
}
