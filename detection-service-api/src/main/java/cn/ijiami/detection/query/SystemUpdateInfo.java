package cn.ijiami.detection.query;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SystemUpdateSave.java
 * @Description 系统更新
 * @createTime 2024年11月18日 15:36:00
 */
@Data
public class SystemUpdateInfo {

    @NotBlank
    @ApiModelProperty(value = "名称")
    private String name;

    @NotBlank
    @ApiModelProperty(value = "版本号")
    private String version;

    @ApiModelProperty(value = "版本信息")
    private String releaseNotes;

    @Min(value = 1, message = "类型错误")
    @Max(value = 2, message = "类型错误")
    @ApiModelProperty(value = "类型 1 系统 2 工具")
    private Integer type;

    @ApiModelProperty(value = "数据包文件id")
    private String fileId;
}
