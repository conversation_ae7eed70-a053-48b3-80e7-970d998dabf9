package cn.ijiami.detection.query;

import cn.ijiami.framework.mybatis.page.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectionLawItemResultQuery.java
 * @Description 自动化法规检测结果详情查询
 * @createTime 2024年04月28日 10:07:00
 */
@Data
@ApiModel(value = "LawItemResultDetailCompareQuery", description = "自动化法规检测结果详情查询query对象")
@JsonIgnoreProperties(ignoreUnknown = true)
public class LawItemResultDetailCompareQuery extends BaseEntity {

    @NotNull(message="缺少reportId")
    @ApiModelProperty(value = "报告id")
    private Long reportId;

    @NotEmpty(message="缺少itemNo")
    @ApiModelProperty(value = "法规检测项编号")
    private String itemNo;

}
