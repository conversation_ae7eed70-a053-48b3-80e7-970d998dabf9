package cn.ijiami.detection.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import cn.ijiami.framework.mybatis.page.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SdkDetailQuery.java
 * @Description 检测内容sdk统计查询
 * @createTime 2022年07月06日 10:48:00
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SdkDetailsQuery extends BaseEntity {

    /**
     * 所属终端
     */
    @ApiModelProperty(value = "所属终端, 1 Android, 2 iOS")
    private Integer terminalType;

    @ApiModelProperty(value = "根据sdk名搜索", example = "微信")
    private String sdkName;

    @ApiModelProperty(value = "资产名", example = "微信")
    private String assetsName;

    @ApiModelProperty(value = "用户名", hidden = false, example = "abc")
    private String userName;

    @ApiModelProperty(value = "法规检测类型 1. 164检测 2. 191检测 不传则返回全部", hidden = false, example = "1")
    private Integer lawType;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date endDate;

}
