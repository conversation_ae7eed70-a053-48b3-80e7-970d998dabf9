package cn.ijiami.detection.query;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ManagerSystemNoticePageQuery.java
 * @Description 管理员发送的通知列表查询
 * @createTime 2023年09月21日 12:07:00
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserReadSystemNotice {

    @NotNull(message = "通知id不能为空")
    @ApiModelProperty(value = "通知id", example = "2")
    private Long noticeId;

}
