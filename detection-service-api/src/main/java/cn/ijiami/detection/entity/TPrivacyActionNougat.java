package cn.ijiami.detection.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.detection.entity.interfaces.ActionExecutor;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/11/12 15:31
 */
@Data
@ApiModel(value = "行为数据")
@Table(name = "t_privacy_action_nougat")
public class TPrivacyActionNougat implements Serializable, ActionExecutor {

    private static final long serialVersionUID = 3087019614631496622L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "任务id")
    @Column(name = "task_id")
    private Long taskId;

    @ApiModelProperty(value = "行为id")
    @Column(name = "action_id")
    private Long actionId;

    @ApiModelProperty(value = "函数调用栈")
    @Column(name = "stack_info")
    private String stackInfo;

    @ApiModelProperty(value = "详细数据")
    @Column(name = "details_data")
    private String detailsData;

    @ApiModelProperty(value = "行为触发时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "action_time")
    private Date actionTime;

    @ApiModelProperty(value = "行为触发时间搓")
    @Column(name = "action_time_stamp")
    private Long actionTimeStamp;

    @ApiModelProperty(value = "类型 是否是使用APP前触发")
    @Column(name = "type")
    private Boolean type;

    @ApiModelProperty(value = "行为阶段 授权前行为、前台运行行为、后台运行行为")
    @Column(name = "behavior_stage")
    private BehaviorStageEnum behaviorStage;

    @ApiModelProperty(value = "主体类型 1.APP 2.SDK")
    @Column(name = "executor_type")
    private Integer executorType = 2;

    @ApiModelProperty(value = "主体")
    @Column(name = "executor")
    private String executor;

    @ApiModelProperty(value = "主体包名")
    @Column(name = "package_name")
    private String packageName;

    @Transient
    @ApiModelProperty(value = "调用次数")
    private Integer counter;

    @Transient
    @ApiModelProperty(value = "行为名称")
    private String actionName;

    @Transient
    @ApiModelProperty(value = "行为权限")
    private String actionPermission;

    @Transient
    @ApiModelProperty(value = "行为权限")
    private String actionPermissionAlias;

    @Transient
    @ApiModelProperty(value = "是否敏感")
    private Boolean sensitive;

    @Transient
    @ApiModelProperty(value = "详细信息")
    List<TPrivacyActionNougat> privacyActionNougats;
    
    @ApiModelProperty(value = "APP调用次数")
    private Integer appCounter;
    
    @ApiModelProperty(value = "SDK调用次数")
    private Integer sdkCounter;

    @ApiModelProperty(value = "APP是否敏感")
    @Column(name = "app_sensitive")
    private Boolean appSensitive;

    @ApiModelProperty(value = "SDK是否敏感")
    @Column(name = "sdk_sensitive")
    private Boolean sdkSensitive;

    @ApiModelProperty(value = "主体为2存储sdkId，json格式")
    @Column(name = "sdk_ids")
    private String sdkIds;

    @Transient
    @ApiModelProperty(value = "是否个人信息相关")
    private String isPersonal;

    @Column(name = "number_action")
    @ApiModelProperty(value = "触发周期次数")
    private Integer numberAction;

    @ApiModelProperty(value = "触发周期")
    @Column(name = "trigger_cycle_time")
    private Long triggerCycleTime;

    @Transient
    @ApiModelProperty(value = "触发周期（秒/次）")
    private String triggerCycle;

    @Transient
    @ApiModelProperty(value = "主体类型 1.APP 2.SDK（String类型)")
    private String executorTypeString;

    @Transient
    @ApiModelProperty(value = "行为截图集")
    private String screenshots;

    @Transient
    @ApiModelProperty(value = "备注")
    private String remarks;

    @Transient
    @ApiModelProperty(value = "jni函数调用栈")
    private String jniStackInfo;

    @Transient
    @ApiModelProperty(value = "触发行为的api名")
    private String apiName;
}
