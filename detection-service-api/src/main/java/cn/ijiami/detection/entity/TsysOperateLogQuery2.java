package cn.ijiami.detection.entity;

import cn.ijiami.manager.syslog.query.TsysOperateLogQuery;
import io.swagger.annotations.ApiParam;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TsysOperateLogQuery2.java
 * @Description 新增了通知id的日志查询类
 * @createTime 2021年12月24日 17:15:00
 */
public class TsysOperateLogQuery2 extends TsysOperateLogQuery {

   
	private static final long serialVersionUID = 5727124773912811600L;
	
	@ApiParam(value = "标签页通知id，用来区分给哪个标签页发消息")
    private String notificationId;

    public String getNotificationId() {
        return notificationId;
    }

    public void setNotificationId(String notificationId) {
        this.notificationId = notificationId;
    }
}
