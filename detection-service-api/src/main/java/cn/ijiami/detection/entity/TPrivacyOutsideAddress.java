package cn.ijiami.detection.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.detection.entity.interfaces.ActionExecutor;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019-06-06 17:45
 */
@Data
@ApiModel(value = "境外访问信息")
@Table(name = "t_privacy_outside_address")
public class TPrivacyOutsideAddress implements Serializable, ActionExecutor {

    private static final long serialVersionUID = -6106592319439437258L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @ApiModelProperty(value = "任务id")
    @Column(name = "task_id")
    private Long taskId;

    @ApiModelProperty(value = "IP")
    @Column(name = "ip")
    private String ip;

    @ApiModelProperty(value = "域名")
    @Column(name = "host")
    private String host;

    @ApiModelProperty(value = "Cookie")
    @Column(name = "cookie")
    private String cookie;

    @ApiModelProperty(value = "协议")
    @Column(name = "protocol")
    private String protocol;

    @ApiModelProperty(value = "端口")
    @Column(name = "port")
    private String port;

    @ApiModelProperty(value = "归属地")
    @Column(name = "address")
    private String address;

    @ApiModelProperty(value = "是否为境外IP")
    @Column(name = "outside")
    private Integer outside = 0;

    @ApiModelProperty(value = "访问次数")
    @Column(name = "counter")
    private Integer counter = 0;

    @ApiModelProperty(value = "主体类型 1APP 2SDK")
    @Column(name = "executor_type")
    private Integer executorType=2;

    @ApiModelProperty(value = "主体")
    @Column(name = "executor")
    private String executor;

    @ApiModelProperty(value = "调用栈")
    @Column(name = "stack_info")
    private String stackInfo;

    @ApiModelProperty(value = "详细数据")
    @Column(name = "details_data")
    private String detailsData;

    @ApiModelProperty(value = "响应数据")
    @Column(name = "response_data")
    private String responseData;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "str_time")
    private Date strTime;

    @ApiModelProperty(value = "行为阶段 授权前行为、前台运行行为、后台运行行为")
    @Column(name = "behavior_stage")
    private BehaviorStageEnum behaviorStage;

    @ApiModelProperty(value = "行为时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "action_time")
    private Date actionTime;

    @Transient
    private Integer appCount;

    @Transient
    private Integer sdkCount;

    @ApiModelProperty(value = "主体为2存储sdkId，json格式")
    @Column(name = "sdk_ids")
    private String sdkIds;

    @ApiModelProperty(value = "单个sdkI")
    @Column(name = "sdk_id")
    private Integer sdkId;

    @ApiModelProperty(value = "(String类型)主体类型 1APP 2SDK")
    @Transient
    private String executorTypeString;

    @ApiModelProperty(value = "是否为境外IP,0境内，1境外(String类型)")
    @Transient
    private String outsideString;

    @ApiModelProperty(value = "网络数据类型")
    @Column(name = "request_method")
    private String requestMethod;

    @ApiModelProperty(value = "url地址")
    @Column(name = "url")
    private String url;

    @ApiModelProperty(value = "包名")
    @Column(name = "package_name")
    private String packageName;

    @ApiModelProperty(value = "是否有cookie标识，1是,用于网络数据类型后面生成cookie图标")
    @Column(name = "cookie_mark")
    private Integer cookieMark;

    @Transient
    @ApiModelProperty(value = "行为截图集")
    private String screenshots;

    @Transient
    @ApiModelProperty(value = "备注")
    private String remarks;

    @Transient
    private Integer serialNumber;

}
