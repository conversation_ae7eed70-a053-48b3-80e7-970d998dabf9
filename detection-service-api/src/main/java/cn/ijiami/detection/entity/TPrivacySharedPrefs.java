package cn.ijiami.detection.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.detection.entity.interfaces.ActionExecutor;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/1/2 15:35
 */
@ApiModel(value = "cn-ijiami-detection-entity-TPrivacySharedPrefs")
@Data
@Table(name = "t_privacy_shared_prefs")
public class TPrivacySharedPrefs implements Serializable, ActionExecutor {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    @Column(name = "task_id")
    private Long taskId;

    @ApiModelProperty(value = "类型id")
    @Column(name = "type_Id")
    private Long typeId;

    @ApiModelProperty(value = "个人信息")
    @Column(name = "name")
    private String name;

    @ApiModelProperty(value = "关键词")
    @Column(name = "sensitive_word")
    private String sensitiveWord;

    /**
     * 敏感词id
     */
    @ApiModelProperty(value = "敏感词id")
    @Column(name = "sensitive_id")
    private Long sensitiveId;

    /**
     * 路径
     */
    @ApiModelProperty(value = "存储位置")
    @Column(name = "path")
    private String path;

    /**
     * 内容
     */
    @ApiModelProperty(value = "代码片段")
    @Column(name = "content")
    private String content;

    /**
     * 图片文件key
     */
    @ApiModelProperty(value = "图片文件key")
    @Column(name = "file_key")
    private String fileKey;

    /**
     * 主体类型 1APP 2SDK
     */
    @ApiModelProperty(value = "主体类型 1APP 2SDK")
    @Column(name = "executor_type")
    private Integer executorType;

    /**
     * 主体
     */
    @ApiModelProperty(value = "主体")
    @Column(name = "executor")
    private String executor;

    @ApiModelProperty(value = "行为阶段 授权前行为、前台运行行为、后台运行行为")
    @Column(name = "behavior_stage")
    private BehaviorStageEnum behaviorStage;

    @ApiModelProperty(value = "调用栈")
    @Column(name = "stack_info")
    private String stackInfo;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "触发时间")
    @Column(name = "action_time")
    private Date actionTime;

    @ApiModelProperty(value = "主体为2存储sdkId，json格式")
    @Column(name = "sdk_ids")
    private String sdkIds;
    
    @ApiModelProperty(value = "信息分类类型名称")
    @Transient
    private String typeName;

    @ApiModelProperty(value = " 1正确    2误判")
    @Column(name = "result_status")
	private Integer resultStatus;

    /**
     * 2021/6/3新增
     */
    @ApiModelProperty(value = "主体类型（String类型）1APP,2SDK")
    @Transient
    private String executorTypeString;

    //v2.5新增，用于存储个人信息的用户修改权限
    @Transient
    @ApiModelProperty(value = "当前用户是否可编辑，1可编辑，0不可编辑")
    private Integer isEdit;

    @Column(name = "package_name")
    @ApiModelProperty(value = "包名")
    private String packageName;

    @Transient
    @ApiModelProperty(value = "行为截图集")
    private String screenshots;

    @Transient
    @ApiModelProperty(value = "备注")
    private String remarks;

    public Integer getIsEdit() {
        return isEdit;
    }

    public void setIsEdit(Integer isEdit) {
        this.isEdit = isEdit;
    }
}