package cn.ijiami.detection.entity;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

@Data
@Table(name = "t_privacy_action")
public class TPrivacyAction {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Long taskId;

    private Long actionId;

    private String actionName;

    private Integer safeLevel;

    private String imgs;

}
