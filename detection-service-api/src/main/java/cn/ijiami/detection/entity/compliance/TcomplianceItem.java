package cn.ijiami.detection.entity.compliance;

import cn.ijiami.detection.server.client.base.enums.TerminalTypeEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 合规检测项
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Table(name = "t_compliance_item")
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class TcomplianceItem extends TcomplianceBaseEntity {

    private static final long serialVersionUID = 3614256981119444156L;

    @Column(name = "item_name")
    @ApiModelProperty(value = "名称")
    private String itemName;

    @Column(name = "item_code")
    @ApiModelProperty(value = "编码")
    private String itemCode;

    @Column(name = "description")
    @ApiModelProperty(value = "描述信息")
    private String description;

    @Column(name = "tip_info")
    @ApiModelProperty(value = "提示信息")
    private String tipInfo;

    @Column(name = "category_code")
    @ApiModelProperty(value = "分类编码")
    private String categoryCode;

    @Column(name = "sort")
    @ApiModelProperty(value = "排序")
    private int sort;

    @Column(name = "is_template")
    @ApiModelProperty(value = "是否是模板")
    private Boolean isTemplate;

    @Transient
    @ApiModelProperty(value = "个人信息类型数据列表")
    private List<TcompliancePersonalInfo> tCompliancePersonalInfos;

    @Transient
    @ApiModelProperty(value = "标记个人信息场景是否选中，前端使用")
    private Boolean ifChecked;

    @Column(name = "terminal_type")
    @ApiModelProperty(value = "类型")
    private TerminalTypeEnum terminalType;
}
