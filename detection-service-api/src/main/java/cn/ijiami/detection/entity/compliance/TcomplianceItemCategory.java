package cn.ijiami.detection.entity.compliance;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;

/**
 * 专家、专项检测项分类
 * <AUTHOR>
 * @date 2024/12/17
 */
@Data
@Table(name = "t_compliance_item_category")
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class TcomplianceItemCategory extends TcomplianceBaseEntity {

    private static final long serialVersionUID = -1229591618486377398L;

    @Column(name = "category_code")
    @ApiModelProperty(value = "编码")
    private String categoryCode;

    @Column(name = "category_name")
    @ApiModelProperty(value = "名称")
    private String categoryName;

    @Column(name = "sort")
    @ApiModelProperty(value = "排序")
    private int sort;

}
