package cn.ijiami.detection.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TInteractKey.java
 * @Description 系统交互的key，提供给其他服务。当其他系统调用当前服务的接口时，校验是否合法调用
 * @createTime 2022年03月01日 17:31:00
 */
@Data
@Table(name = "t_interact_key")
public class TInteractKey {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "access_key")
    private String accessKey;

    @Column(name = "secret_key")
    private String secretKey;

    @Column(name = "status")
    private Integer status;

    @Column(name = "description")
    private String description;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

}
