package cn.ijiami.detection.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.apache.commons.lang.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.detection.VO.CheckList;
import cn.ijiami.detection.VO.XiaomiAppStoreDetectItemVO;
import cn.ijiami.detection.VO.sdk.SdkContrastVo;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.PrivacyPolicyResultStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "合规风险检测结果")
@Table(name = "t_privacy_policy_result")
public class TPrivacyPolicyResult implements Serializable {

    private static final long serialVersionUID = 7201107415975554683L;

    public TPrivacyPolicyResult() {
        this.createTime = new Date();
        this.updateTime = new Date();
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "任务id")
    @Column(name = "task_id")
    private Long taskId;

    @ApiModelProperty(value = "合规风险检测项id")
    @Column(name = "policy_item_id")
    private Long policyItemId;

    /**
     * 图片文件key
     */
    @ApiModelProperty(value = "图片文件key")
    @Column(name = "file_key")
    private String fileKey;

    @ApiModelProperty(value = "状态1安全2风险 3自评估")
    @Column(name = "status")
    private Integer status;

    @ApiModelProperty(value = "1 合规风险 2 应用商店合规检测专项")
    @Column(name = "category")
    private Integer category;

    @ApiModelProperty(value = "详细信息")
    @Column(name = "detail_result")
    private String detailResult;

    @ApiModelProperty(value = "行为阶段 授权前行为、前台运行行为、后台运行行为、应用退出")
    @Column(name = "behavior_stage")
    private BehaviorStageEnum behaviorStage;

    @Column(name = "create_time")
    @ApiModelProperty(value = "创建时间", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date createTime;

    @Column(name = "update_time")
    @ApiModelProperty(value = "修改时间", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date updateTime;

    @Transient
    @ApiModelProperty(value = "检测结果(存在风险/安全)")
    private String result;

    @Transient
    @ApiModelProperty(value = "检测内容")
    private String content;

    @Transient
    @ApiModelProperty(value = "风险描述")
    private String riskDescription;

    @Transient
    @ApiModelProperty(value = "风险描述")
    private String safeDescription;

    @Transient
    @ApiModelProperty(value = "结果描述")
    private String description;

    @Transient
    @ApiModelProperty(value = "检测项编号")
    private String itemNo;

    @Transient
    @ApiModelProperty(value = "检测目的")
    private String name;

    @Transient
    @ApiModelProperty(value = "第三方SDK共享清单文本")
    private String thirdPartySharingText;

    @Transient
    @ApiModelProperty(value = "检测标准")
    private String standards;

    @Transient
    @ApiModelProperty(value = "第三方SDK共享清单列表")
    private List<CheckList.Row> thirdPartySharingChecklist;

    @Transient
    @ApiModelProperty(value = "小米应用市场检测专项违规")
    private List<XiaomiAppStoreDetectItemVO> xiaomiAppStoreDetectItemList;
    
    @ApiModelProperty(value = "全国sdk对比结果")
    @Transient
    private List<SdkContrastVo> sdkContrastList;
    
    @Transient
    @ApiModelProperty(value = "堆栈信息")
    private String stackInfo;
    

    public String getDescription() {
        // 安全
        if (this.getStatus() == 1) {
            this.description = this.getSafeDescription();
        }
        // 风险
        else {
            this.description = this.getRiskDescription();
        }
        if (StringUtils.isBlank(this.description)) {
            this.description = this.getDetailResult();
        }
        return description;
    }

    public String getResult() {
        if (status == PrivacyPolicyResultStatusEnum.SAFE.status) {
            return "安全";
        } else if (status == PrivacyPolicyResultStatusEnum.RISK.status) {
            return "存在风险";
        } else {
            return "自主评估";
        }
    }

}
