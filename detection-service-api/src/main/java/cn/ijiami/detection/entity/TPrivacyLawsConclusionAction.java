package cn.ijiami.detection.entity;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TPrivacyLawsResultConclusionAction.java
 * @Description 违规数据
 * @createTime 2022年08月12日 16:03:00
 */
@Data
@Table(name = "t_privacy_laws_conclusion_action")
public class TPrivacyLawsConclusionAction {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @ApiModelProperty(value = "法规编码")
    @Column(name = "item_no")
    private String itemNo;

    @ApiModelProperty(value = "任务id")
    @Column(name = "task_id")
    private Long taskId;

    @ApiModelProperty(value = "sdk名称")
    @Column(name = "sdk_name")
    private String sdkName;

    @ApiModelProperty(value = "行为")
    @Column(name = "action")
    private String action;

    @ApiModelProperty(value = "行为阶段")
    @Column(name = "behavior_stage")
    private BehaviorStageEnum behaviorStage;
}
