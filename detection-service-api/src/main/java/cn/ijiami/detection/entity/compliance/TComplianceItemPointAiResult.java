package cn.ijiami.detection.entity.compliance;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 专家-AI检测点结果
 * <AUTHOR>
 *
 */
@Data
@Table(name = "t_compliance_item_point_ai_result")
public class TComplianceItemPointAiResult implements Serializable{

    private static final long serialVersionUID = -1081422606948741493L;
    
    public TComplianceItemPointAiResult(Long taskId, String imgUrl, Integer type, Date createTime, String itemNo){
    	this.taskId = taskId;
    	this.imgUrl = imgUrl;
    	this.type = type;
    	this.createTime = createTime;
    	this.itemNo = itemNo;
    }

    @Id
    @Column(name = "id",insertable = false)
    @GeneratedValue(generator = "JDBC",strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "task_id")
    @ApiModelProperty(value = "任务ID")
    private Long taskId;
    
    @Column(name = "item_no")
    @ApiModelProperty(value = "检测项ID")
    private String itemNo;
    
    @Column(name = "img_url")
    @ApiModelProperty(value = "结果图片地址")
    private String imgUrl;
    
    @Column(name = "type")
    @ApiModelProperty(value = "类型1图片 ， 2检测项结果")
    private Integer type;
    
    @Column(name = "item_result")
    @ApiModelProperty(value = "检测结果")
    private Integer itemResult;
    
    @Column(name = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    

}
