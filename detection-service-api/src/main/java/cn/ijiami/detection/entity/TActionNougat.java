package cn.ijiami.detection.entity;

import cn.ijiami.detection.server.client.base.enums.TerminalTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2019/11/12 15:29
 */
@Data
@Table(name = "t_action_nougat")
public class TActionNougat {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "action_id")
    private Long actionId;

    @Column(name = "action_name")
    private String actionName;

    @Column(name = "action_api")
    private String actionApi;

    @Column(name = "action_permission")
    private String actionPermission;

    @Column(name = "action_permission_alias")
    private String actionPermissionAlias;

    //mysql8.0 关键字入库报错
    @ColumnType(column = "`sensitive`",jdbcType = JdbcType.VARCHAR)
    private Boolean sensitive;

    @Column(name = "terminal_type")
    private TerminalTypeEnum terminalType;

    /**
     * 是否为个人信息相关行为
     */
    @Column(name = "is_personal")
    private Integer personal;

    @Transient
    @ApiModelProperty(value = "标记是否要勾选父类检测任务个人信息")
    private Boolean hasTrigger;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getActionId() {
        return actionId;
    }

    public void setActionId(Long actionId) {
        this.actionId = actionId;
    }

    public String getActionName() {
        return actionName;
    }

    public void setActionName(String actionName) {
        this.actionName = actionName;
    }

    public String getActionPermission() {
        return actionPermission;
    }

    public void setActionPermission(String actionPermission) {
        this.actionPermission = actionPermission;
    }

    public String getActionPermissionAlias() {
        return actionPermissionAlias;
    }

    public void setActionPermissionAlias(String actionPermissionAlias) {
        this.actionPermissionAlias = actionPermissionAlias;
    }

    public Boolean getSensitive() {
        return sensitive;
    }

    public void setSensitive(Boolean sensitive) {
        this.sensitive = sensitive;
    }

    public TerminalTypeEnum getTerminalType() {
        return terminalType;
    }

    public void setTerminalType(TerminalTypeEnum terminalType) {
        this.terminalType = terminalType;
    }

    public Integer getPersonal() {
        return personal;
    }

    public void setPersonal(Integer personal) {
        this.personal = personal;
    }

    public Boolean getHasTrigger() {
        return hasTrigger;
    }

    public void setHasTrigger(Boolean hasTrigger) {
        this.hasTrigger = hasTrigger;
    }
}
