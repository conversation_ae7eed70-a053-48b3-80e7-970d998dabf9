package cn.ijiami.detection.entity;
import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TAiAuthCodes.java
 * @Description ai授权码
 * @createTime 2025年03月24日 11:33:00
 */
@Data
@Table(name = "t_ai_auth_codes")
public class TAiAuthCodes {

    @Id
    @Column(name = "auth_code_id")
    private String authCodeId;

    @Column(name = "client_id")
    private Long clientId;

    @Column(name = "start_date")
    private Date startDate;

    @Column(name = "end_date")
    private Date endDate;

    @Column(name = "total_quota")
    private Integer totalQuota;

    @Column(name = "remaining_quota")
    private Integer remainingQuota;

    @Column(name = "max_concurrent_calls")
    private Integer maxConcurrentCalls;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "is_delete")
    private Integer isDelete;
}
