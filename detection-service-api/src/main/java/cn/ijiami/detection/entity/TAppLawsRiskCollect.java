package cn.ijiami.detection.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TAppLawsRiskCollect.java
 * @Description 法规问题汇总统计
 * @createTime 2022年07月05日 15:16:00
 */
@Data
@Table(name = "t_app_laws_risk_collect")
@ApiModel(value = "TAppLawsRiskCollect", description = "法规问题汇总统计")
public class TAppLawsRiskCollect implements Serializable {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    @ApiModelProperty(value = "主键", hidden = false, example = "1")
    private Long id;

    /**
     * 任务id
     */
    @Column(name = "task_id")
    @ApiModelProperty(value = "任务id", hidden = false, example = "1")
    private Long taskId;

    /**
     * 资产id
     */
    @Column(name = "assets_id")
    @ApiModelProperty(value = "资产id", hidden = false, example = "1")
    private Long assetsId;


    /**
     * 最后一次检测时间
     */
    @Column(name = "detection_time")
    @ApiModelProperty(value = "检测时间", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date detectionTime;

    @Column(name ="risk_count")
    @ApiModelProperty(value = "风险数", hidden = false, example = "2")
    private Integer riskCount;

    /**
     * 法规id
     */
    @Column(name = "law_id")
    @ApiModelProperty(value = "法规类型（1.164  2.191）", hidden = false, example = "2")
    private Integer lawId;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    @ApiModelProperty(value = "用户id", hidden = false, example = "1")
    private Long userId;

    /**
     * 状态
     */
    @Column(name = "status")
    @ApiModelProperty(value = "状态 1 最后一次检测", hidden = false, example = "0")
    private Integer status;

    /**
     * 终端类型
     */
    @Column(name = "terminal_type")
    @ApiModelProperty(value = "终端类型（1：android  2：ios）", hidden = false, example = "1")
    private Integer terminalType;


    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value = "创建时间", hidden = true, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date createTime;
}
