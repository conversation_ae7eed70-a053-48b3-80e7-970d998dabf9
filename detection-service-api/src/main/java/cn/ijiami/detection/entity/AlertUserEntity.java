package cn.ijiami.detection.entity;

import cn.ijiami.base.common.validation.Mobilephone;
import cn.ijiami.framework.common.enums.StatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-05-28 15:05
 */
@Data
@Table(name = "t_alert_user")
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class AlertUserEntity {

    @Id
    @Column(name = "user_id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "用户ID", example = "1")
    private Long userId;

    @Column(name = "user_name")
    @ApiModelProperty(value = "用户名称", example = "TLH")
    private String userName;

    @Column(name = "real_name")
    @ApiModelProperty(value = "真实姓名", example = "LihuaTang")
    private String realName;

    @Column(name = "email")
    @ApiModelProperty(value = "邮箱", example = "<EMAIL>")
    @Length(max = 50, message = "邮箱长度不能超过50")
    private String email;

    @Column(name = "mobilephone")
    @ApiModelProperty(value = "手机号码", example = "13590127979")
    @Mobilephone
    @Length(max = 11, message = "手机号码不能超过11位")
    private String mobilephone;

    @Column(name = "status")
    @ApiModelProperty(value = "用户状态", example = "1")
    private StatusEnum status;

    @Column(name = "valid_start_date")
    @ApiModelProperty(value = "有效开始日期", hidden = true, example = "2017-07-18")
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date validStartDate;

    @Column(name = "valid_end_date")
    @ApiModelProperty(value = "有效结束日期", hidden = true, example = "2017-07-18")
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date validEndDate;

    @Column(name = "alert_send_status")
    @ApiModelProperty(value = "是否发送")
    private Integer alertSendStatus;

    @Column(name = "role_name")
    @ApiModelProperty(value = "角色")
    private String roleName;

    @Transient
    @ApiModelProperty(value = "账号期限是否有效")
    private Boolean isEffective;

    @Transient
    @ApiModelProperty("前端标记可选")
    private Boolean canSelected;
}
