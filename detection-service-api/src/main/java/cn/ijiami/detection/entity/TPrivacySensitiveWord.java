package cn.ijiami.detection.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.detection.entity.interfaces.ActionExecutor;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "个人信息传输敏感词")
@Data
@Table(name = "t_privacy_sensitive_word")
public class TPrivacySensitiveWord implements Serializable, ActionExecutor {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    @Column(name = "task_id")
    private Long taskId;

    /**
     * 类型id
     */
    @ApiModelProperty(value = "类型id")
    @Column(name = "type_id")
    private Long typeId;

    @ApiModelProperty(value = "敏感词名称")
    @Column(name = "name")
    private String name;

    /**
     * 敏感词
     */
    @ApiModelProperty(value = "敏感词")
    @Column(name = "sensitive_word")
    private String sensitiveWord;

    /**
     * 请求类型
     */
    @ApiModelProperty(value = "请求类型")
    @Column(name = "method")
    private String method;

    @ApiModelProperty(value = "url地址")
    @Column(name = "url")
    private String url;

    /**
     * 域名
     */
    @ApiModelProperty(value = "域名")
    @Column(name = "host")
    private String host;

    /**
     * 端口
     */
    @ApiModelProperty(value = "端口")
    @Column(name = "port")
    private String port;

    /**
     * cookie
     */
    @ApiModelProperty(value = "Cookie")
    @Column(name = "cookie")
    private String cookie;


    /**
     * 协议
     */
    @ApiModelProperty(value = "协议")
    @Column(name = "protocol")
    private String protocol;

    /**
     * 请求地址
     */
    @ApiModelProperty(value = "请求地址")
    @Column(name = "address")
    private String address;

    /**
     * 代码段
     */
    @ApiModelProperty(value = "代码段")
    @Column(name = "code")
    private String code;

    @ApiModelProperty(value = "敏感等级")
    @Column(name = "risk_level")
    private Boolean riskLevel;

    @ApiModelProperty(value = "整改建议")
    @Column(name = "suggestion")
    private String suggestion;

    @ApiModelProperty(value = "图片文件key")
    @Column(name = "file_key")
    private String fileKey;

    @ApiModelProperty(value = "sdk主键")
    @Column(name = "sdk_id")
    private Long sdkId;

    @ApiModelProperty(value = "sdk名称")
    @Column(name = "sdk_name")
    private String sdkName;

    @ApiModelProperty(value = "主体类型 1APP 2SDK")
    @Column(name = "executor_type")
    private Integer executorType;

    @ApiModelProperty(value = "主体")
    @Column(name = "executor")
    private String executor;

    @ApiModelProperty(value = "调用栈")
    @Column(name = "stack_info")
    private String stackInfo;

    @ApiModelProperty(value = "详细数据")
    @Column(name = "details_data")
    private String detailsData;

    @ApiModelProperty(value = "响应数据")
    @Column(name = "response_data")
    private String responseData;

    @ApiModelProperty(value = "行为阶段 授权前行为、前台运行行为、后台运行行为")
    @Column(name = "behavior_stage")
    private BehaviorStageEnum behaviorStage;

    @ApiModelProperty(value = "触发时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "action_time")
    private Date actionTime;

    @ApiModelProperty(value = "主体为2存储sdkId，json格式")
    @Column(name = "sdk_ids")
    private String sdkIds;
    
    @Transient
    @ApiModelProperty(value = "类型名称(信息分类类型)")
    private String typeName;

    @ApiModelProperty(value = " 1正确    2误判")
    @Column(name = "result_status")
	private Integer resultStatus;


    @Transient
    @ApiModelProperty(value = "主体类型（String类型）1APP,2SDK")
    private String executorTypeString;

    //v2.5新增，用于传输个人信息的用户修改权限
    @Transient
    @ApiModelProperty(value = "当前用户是否可编辑，1为可编辑，0为不可编辑")
    private Integer isEdit;

    @Column(name = "attributively")
    @ApiModelProperty(value = "地理位置")
    private String attributively;

    @ApiModelProperty(value = "ip")
    @Column(name = "ip")
    private String ip;

    @ApiModelProperty(value = "包名")
    @Column(name = "package_name")
    private String packageName;

    @Transient
    @ApiModelProperty(value = "动态行为截图集合")
    private String screenshots;

    @Transient
    @ApiModelProperty(value = "截图备注")
    private String remarks;

    @ApiModelProperty(value = "是否有cookie标识，1是,用于网络数据类型后面生成cookie图标")
    @Column(name = "cookie_mark")
    private Integer cookieMark;

    @ApiModelProperty(value = "是否明文传输信息， 1是 0否")
    @Column(name = "plaintext_transmission")
    private Integer plaintextTransmission;
    /**
     * 是否cookie检测出的违规关键词
     */
    @Transient
    private boolean isCookieWord;

    public Integer getIsEdit() {
        return isEdit;
    }

    public void setIsEdit(Integer isEdit) {
        this.isEdit = isEdit;
    }

}

