package cn.ijiami.detection.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.detection.server.client.base.enums.DynamicAutoSubStatusEnum;
import cn.ijiami.detection.enums.TaskDataStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TTaskData.java
 * @Description 任务阶段数据
 * @createTime 2024年09月30日 18:02:00
 */
@Data
@Table(name = "t_task_data")
public class TTaskData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "task_id")
    private Long taskId;

    @Column(name = "dynamic_sub_status")
    private DynamicAutoSubStatusEnum dynamicSubStatus;

    @Column(name = "dynamic_start_time")
    @ApiModelProperty(value = "动态检测开始时间")
    private Date dynamicStartTime;

    @Column(name = "data_path")
    private String dataPath;

    @Column(name = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;

    @Column(name = "status")
    @ApiModelProperty(value = "任务数据状态 1 等待解析 2 解析中 3 解析完成 4 解析失败")
    private TaskDataStatusEnum status;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    @ApiModelProperty(value = "修改时间", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date updateTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value = "数据创建时间", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date createTime;

}
