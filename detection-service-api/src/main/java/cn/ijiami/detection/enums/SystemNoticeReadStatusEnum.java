package cn.ijiami.detection.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.framework.common.enums.BaseValueEnum;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SystemNoticeTypeEnum.java
 * @Description 系统通知用户阅读状态
 * @createTime 2023年09月19日 17:47:00
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum SystemNoticeReadStatusEnum implements BaseValueEnum {

    UNREAD(1, "未读"), READ(2, "已读");

    private int    value;
    private String name;

    SystemNoticeReadStatusEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    @JsonCreator
    public static SystemNoticeReadStatusEnum getItem(Integer value) {
        if (value == null) {
            return null;
        }
        for (SystemNoticeReadStatusEnum item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public int itemValue() {
        return value;
    }

    @Override
    public String itemName() {
        return name;
    }

}
