package cn.ijiami.detection.enums;


import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.framework.common.enums.BaseValueEnum;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum UserUseDeviceBusinessTypeEnum implements BaseValueEnum {


    /**
     * 检测任务
     */
    TASK(0, "检测任务");

    private final int value;
    private final String name;

    UserUseDeviceBusinessTypeEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    @JsonCreator
    public static UserUseDeviceBusinessTypeEnum getItem(int value) {
        for (UserUseDeviceBusinessTypeEnum item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    @Override
    public int itemValue() {
        return value;
    }

    @Override
    public String itemName() {
        return name;
    }
}
