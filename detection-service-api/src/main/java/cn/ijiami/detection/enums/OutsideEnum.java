package cn.ijiami.detection.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.framework.common.enums.BaseValueEnum;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName OutsideEnum.java
 * @Description
 * @createTime 2021年11月23日 16:33:00
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum OutsideEnum implements BaseValueEnum {

    DOMESTIC(0, "国内"), FOREIGN(1, "国外");

    private int value;

    private String name;

    OutsideEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    @JsonCreator
    public static OutsideEnum getItem(int value) {
        for (OutsideEnum item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }

    @Override
    public int itemValue() {
        return value;
    }

    @Override
    public String itemName() {
        return name;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
