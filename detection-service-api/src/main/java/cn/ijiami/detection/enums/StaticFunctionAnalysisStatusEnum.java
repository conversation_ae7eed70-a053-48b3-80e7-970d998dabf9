package cn.ijiami.detection.enums;

public enum StaticFunctionAnalysisStatusEnum {

    NONE(-1, "未检测"), WAITING(0, "待检测"), SUCCESS(1, "成功"), FAILURE(2, "失败"), PROCESSING(3, "进行中");

    private int value;
    private String name;

    StaticFunctionAnalysisStatusEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
