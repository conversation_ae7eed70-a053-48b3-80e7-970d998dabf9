package cn.ijiami.detection.enums;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName StopIosHitShellStateEnum.java
 * @Description 停止砸壳状态
 * @createTime 2022年01月19日 10:09:00
 */
public enum StopIosHitShellStateEnum {

    SUCCESS(1, "停止砸壳成功"), FAILURE(2, "停止砸壳失败");

    private final Integer value;
    private final String name;

    StopIosHitShellStateEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

}
