package cn.ijiami.detection.enums;

public enum ReinforceEnum {

    NOT_REINFORCE(0,"未加固"),

    REINFORCE(1,"加固");

    private int value;
    private String name;

    ReinforceEnum(int value,String name){
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static ReinforceEnum getEnum(int value){
        for(ReinforceEnum item : values()){
            if(item.getValue() == value){
                return item;
            }
        }
        return null;
    }
}
