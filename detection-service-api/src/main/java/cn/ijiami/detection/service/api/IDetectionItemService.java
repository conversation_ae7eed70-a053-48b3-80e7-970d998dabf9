package cn.ijiami.detection.service.api;

import com.github.pagehelper.PageInfo;

import cn.ijiami.detection.VO.DetectionItemListVO;
import cn.ijiami.detection.VO.DetectionItemVO;
import cn.ijiami.detection.entity.TDetectionItem;
import cn.ijiami.detection.query.TemplateDetectionItemQuery;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;

/**
 * 检测项接口类
 * 
 * <AUTHOR>
 *
 */
public interface IDetectionItemService {
	/**
	 * 新增检测项接口
	 * 
	 * @param detectionItem
	 * @return
	 */
	TDetectionItem addDetectionItem(TDetectionItem detectionItem);

	/**
	 * 根据检测项QUERY查询检测项列表并分页
	 * 
	 * @return
	 * @throws IjiamiApplicationException
	 */
	PageInfo<DetectionItemVO> findDetectionItemByQuery(TemplateDetectionItemQuery detectionItemQuery) throws IjiamiApplicationException;

	/**
	 * 根据检测项对象查询检测项集合并分页
	 * 
	 * @param detectionItem
	 * @return
	 */
	DetectionItemListVO findDetectionItemListVO(TDetectionItem detectionItem);

	PageInfo<DetectionItemVO> findDetectionItemByPage(TDetectionItem detectionItem);
}
