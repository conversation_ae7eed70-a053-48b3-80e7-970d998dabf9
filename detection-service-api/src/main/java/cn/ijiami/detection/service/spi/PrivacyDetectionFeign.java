package cn.ijiami.detection.service.spi;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import cn.ijiami.detection.service.api.vo.DetectionToolVO;

/**
 * <AUTHOR>
 * @since 2019/3/27 11:54
 */
@FeignClient(value = "${ijiami-cloud-privacy-detection-server-name:privacy-detection-tools-cloud-server}")
public interface PrivacyDetectionFeign {

    @PostMapping(
            value = {"${ijiami-cloud-privacy-detection-server-context:/privacy-detection}/common/detection/common/stopTask"},
            produces = {"application/json"}
    )
    Boolean stopTask(@RequestParam("threadId") Long var1);

    @PostMapping(
            value = {"${ijiami-cloud-privacy-detection-server-context:/privacy-detection}/api/detection/tool/startTask"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    void startTask(@RequestBody DetectionToolVO var1);

    @PostMapping(
            value = {"${ijiami-cloud-privacy-detection-server-context:/privacy-detection}/common/detection/common/startTask"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    void startTask1(@RequestBody DetectionToolVO var1);
}
