package cn.ijiami.detection.service.api;

import java.util.List;

import cn.ijiami.detection.DTO.TaskChangeStatusDTO;
import cn.ijiami.detection.VO.BigDataVO;
import cn.ijiami.detection.VO.ChongqinVO;
import cn.ijiami.detection.VO.ClientUpdateProgressVO;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.VO.TaskVO;
import cn.ijiami.detection.VO.compliance.ExpertTestingTaskVO;
import cn.ijiami.detection.VO.detection.BaseMessageVO;
import cn.ijiami.detection.VO.detection.SdkResponseVO;
import cn.ijiami.detection.VO.detection.SdkVO;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.query.task.TaskCreateQuery;
import cn.ijiami.detection.query.task.TaskProgressQuery;
import cn.ijiami.detection.query.task.TaskQuery;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.common.exception.IjiamiCommandException;

/**
 * 任务模块接口定义
 *
 * <AUTHOR>
 */
public interface ITaskService {

    /**
     * 启动静态检测
     *
     * @param taskCreateQuery 请求参数实体
     * @return 任务id
     */
    Long startTask(TaskCreateQuery taskCreateQuery);

    /**
     * 终止静态检测任务
     *
     * @param taskId      任务ID
     * @return
     * @throws IjiamiApplicationException
     */
    boolean stopTask(Long taskId) throws IjiamiApplicationException;

    /**
     * 终止静态检测任务
     *
     * @param taskId      任务ID
     * @param forceStop 是否是强制终止
     * @return
     * @throws IjiamiApplicationException
     */
    boolean stopTask(Long taskId, boolean forceStop) throws IjiamiApplicationException;

    /**
     * 重启静态检测
     *
     * @param taskId 任务ID
     * @return
     * @throws IjiamiCommandException
     */
    Long restartTask(Long taskId) throws IjiamiCommandException;

    /**
     * 重启动态检测
     *
     * @param taskId 任务ID
     * @return
     * @throws IjiamiCommandException
     */
    Long restartDynamicTask(Long taskId) throws IjiamiCommandException;

    Integer restartDynamicTaskStage(Long taskId);

    /**
     * 获取任务列表
     *
     * @param taskQuery
     * @return
     */
    TaskVO findTaskByPage(TaskQuery taskQuery);

    List<TTask> findDetectedTasks(Long userId);

    TTask findByDocumentId(String documentId);

    TTask findById(Long taskId);

    // 获取检测详情
    TaskDetailVO getTaskDetail(String id);
    
    TaskDetailVO getTaskDetailByTaskId(Long taskId);

    // 返回基础信息
    BaseMessageVO getBaseMessage(String documentId);

    // 返回第三方SDK信息
    List<SdkVO> getSdkList(String documentId, Long taskId) throws IjiamiApplicationException;
    // ios SDK信息
    SdkResponseVO getIosSDKList(String documentId, Long taskId);
    
    List<SdkVO> getAPISdkList(String documentId, Long taskId) throws IjiamiApplicationException;

    void deleteByTaskId(Long taskId, Long userId, boolean isAdmin) throws IjiamiApplicationException;

    void deleteInTaskId(List<Long> taskId, Long userId, boolean isAdmin) throws IjiamiApplicationException;

    List<BigDataVO> findMd5();

    TTask findDocumentId(String md5);

    void bigDataCallback(BigDataVO bigDataVO);

    void chongqinCallback(ChongqinVO chongqinVO);

    /**
     * 客户端更新动态检测进度
     *
     * @param clientUpdateProgressVO
     */
    void updateProgress(ClientUpdateProgressVO clientUpdateProgressVO);

    /**
     * 1开始完整检测、2法规检测、3重新检测、4开始全自动检测、5开始沙箱手机检测、6沙箱手机连接失败 7任务超时检测中断
     *
     * @param taskId 任务id
     * @param type   类型
     * @return 任务id
     */
    Long startDynamicTask(Long taskId, Integer type);

    Long startDynamicTask(Long taskId, Integer type, int deviceType);

    /**
     * 根据时间戳查询大于当前时间的任务
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    List<String> findDetectedTasksByTime(String beginTime, String endTime);

    /**
     * 启动ios动态、法规检测
     *
     * @param taskProgressQuery
     */
    void startIosDynamicDetect(TaskProgressQuery taskProgressQuery) throws IjiamiCommandException;

    /**
     * ios 动态检测状态更新
     *
     * @param taskProgressQuery
     * @return
     */
    boolean stopIosDynamicDetect(TaskProgressQuery taskProgressQuery) throws IjiamiCommandException;


    void delPersonalWord(Integer type, Long id) throws IjiamiCommandException;

    void refreshTaskSort(TTask task);

    /**
     * 修改任务状态
     */
    ExpertTestingTaskVO changeTaskStatus(TaskChangeStatusDTO taskChangeStatusDTO);

    void stopDynamicByIdbCallback(Long taskId, String deviceId, String reason, String frpcPort);

    TTask findDetectionCompleteByMd5(String md5);

    void updateSdkPermission(List<TPrivacyActionNougat> nougatList);
    Integer findAppletDynamicDetectionCount(Long userId, Integer terminalType);

    Long taskPreempted(Long taskId, int deviceType);

    /**
     * 获取检测出的热更新sdk
     * @param taskId
     * @return
     */
    List<String> getHotfixSdk(Long taskId);

    List<SdkVO> getSuspiciousSdkList(Long taskId);

    /**
     * 开始ai智能检测
     *
     * @param taskId 任务ID
     * @return
     * @throws IjiamiCommandException
     */
    Long startAiDetection(Long taskId) throws IjiamiCommandException;

    /**
     * 获取专家检测任务列表
     * @param taskQuery
     * @return
     */
    TaskVO findExpertTaskByPage(TaskQuery taskQuery);
}
