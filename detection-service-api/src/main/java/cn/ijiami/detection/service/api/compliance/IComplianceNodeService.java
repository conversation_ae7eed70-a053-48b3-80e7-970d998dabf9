package cn.ijiami.detection.service.api.compliance;

import cn.ijiami.detection.VO.compliance.ComplianceBaseVO;
import cn.ijiami.detection.entity.compliance.TcomplianceNode;
import cn.ijiami.detection.entity.compliance.TpermissonGroup;
import cn.ijiami.detection.server.client.base.dto.compliance.CategoryDTO;
import cn.ijiami.detection.server.client.base.dto.compliance.ComplianceNodeDTO;

import java.util.List;

public interface IComplianceNodeService {

    /**
     * 获取所有节点信息
     */
    List<ComplianceNodeDTO> getAllNode();

    /**
     * 获取节点信息
     */
    List<CategoryDTO> getNodeInfo(ComplianceBaseVO complianceBaseVO);


    /**
     * 获取下一个节点(为空即当前节点是最后一个节点)
     */
    ComplianceNodeDTO getNextNode(String nodeCode);
}
