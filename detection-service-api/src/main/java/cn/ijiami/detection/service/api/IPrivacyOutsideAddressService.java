package cn.ijiami.detection.service.api;

import java.util.List;
import java.util.Map;

import cn.ijiami.detection.VO.CountOutsideTypeVO;
import cn.ijiami.detection.VO.TPrivacyOutsideAddressVO;
import cn.ijiami.detection.VO.compliance.OutsideAddressDetailsVO;
import cn.ijiami.detection.VO.networkcheck.DialDetectResult;
import cn.ijiami.detection.entity.TIpAddressCheckRecord;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.query.BehaviorQuery;
import cn.ijiami.detection.query.IpAddressCheckQuery;

/**
 * <AUTHOR>
 * @date 2019/12/3 15:19
 */
public interface IPrivacyOutsideAddressService {

    List<TPrivacyOutsideAddress> getOutSideAddress(Long taskId, Integer behaviorStage);

    List<TPrivacyOutsideAddress> findByTaskIdAndOutside(Long taskId, Integer outside, Integer behaviorStage);

    List<CountOutsideTypeVO> countByOutside(Long taskId, Integer behaviorStage);


    /**
     * 静态检测完成时更新境内外IP检测项检测结果
     *
     * @param taskId
     * @param documentId
     */
    void updateOutSideWhenStaticComplete(Long taskId, String documentId);

    /**
     * 更新境内外ip信息
     *
     * @param taskId 任务ID
     * @param outsideAddresses 获取到的结果集
     * @param clean 清理原数据
     */
    void updateOutSide(Long taskId, List<TPrivacyOutsideAddress> outsideAddresses,boolean clean);

    List<TPrivacyOutsideAddress> findByTaskIdAndOutsideStackInfo(Long taskId, String ip, String host, Integer behaviorStage);

    /**
     * 查询通讯行为数据 v2.5新改
     * @param behaviorQuery
     * @return
     */
    TPrivacyOutsideAddressVO findOutsideDataByTaskId(BehaviorQuery behaviorQuery);

    /**
     * 获取通信行为的筛选条件（主体类型，协议类型）
     * @param documentId
     * @param behaviorStage
     * @return
     */
    Map<String,List<String>> getBehaviorNoun(String documentId, Integer behaviorStage);


    /**
     * ip地址拨测
     * @param query
     * @param userId
     * @return
     */
    void ipAddressCheck(IpAddressCheckQuery query, Long userId);

    void saveIpAddressCheckData(DialDetectResult result);

    DialDetectResult queryIpAddressCheckDataByRecord(TIpAddressCheckRecord record);

    void ipAddressCheckFailure(TIpAddressCheckRecord record);

    OutsideAddressDetailsVO queryAnalysisNetwork(Long id);
}
