package cn.ijiami.detection.service;

import cn.ijiami.base.common.user.IUser;
import cn.ijiami.detection.VO.RealTimeBehaviorLog;
import cn.ijiami.detection.VO.RealTimeNetLog;
import cn.ijiami.detection.VO.detection.privacy.IOSRealTimeLog;
import cn.ijiami.detection.VO.detection.privacy.dto.AppletActionBO;
import cn.ijiami.detection.bean.DynamicTaskContext;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.result.AppDetailsResult;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import com.github.pagehelper.PageInfo;

import java.io.File;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectionDataService.java
 * @Description  检测数据
 * @createTime 2024年05月29日 18:24:00
 */
public interface DetectionDataService {

    File exportRealtimeSdk(IUser user, Long taskId);
    File exportRealtimeBehavior(IUser user, Long taskId);

    void deleteActionLogs(Long taskId, List<String> actionLogIds);

    AppDetailsResult getAppDetails(Long taskId) throws IjiamiApplicationException;

    void insertDynamicAction(TPrivacyActionNougat actionNougat, DynamicTaskContext taskData);
    void cleanDynamicAction(Long taskId);
    List<RealTimeBehaviorLog> getAllActionList(Long taskId);
    List<IOSRealTimeLog> getAllIosActionList(Long taskId);
    RealTimeBehaviorLog getActionById(Long id);
    PageInfo<RealTimeBehaviorLog> getActionPage(Long taskId, List<Long> actionIdList,
                                                List<PrivacyStatusEnum> isPersonalList,
                                                List<BehaviorStageEnum> behaviorStageList,
                                                int page, int rows);
    List<String> getActionTypeList(Long taskId);

    IOSRealTimeLog getIosActionById(Long id);

    PageInfo<IOSRealTimeLog> getIosActionPage(Long taskId, List<Long> actionIdList,
                                              List<PrivacyStatusEnum> isPersonalList,
                                              List<BehaviorStageEnum> behaviorStageList,
                                              int page, int rows);

    List<String> getIosActionTypeList(Long taskId);

    void insertAndroidNetAction(TPrivacyOutsideAddress address, DynamicTaskContext taskData);
    void insertIosNetAction(TPrivacyOutsideAddress address, DynamicTaskContext taskData);
    void insertAppletNetAction(TPrivacyOutsideAddress address, DynamicTaskContext taskData, AppletActionBO data);
    void insertHarmonyNetAction(TPrivacyOutsideAddress address, DynamicTaskContext taskContext);


    void cleanNetAction(Long taskId);

    PageInfo<RealTimeNetLog> getNetActionPage(Long taskId, int page, int rows);

    RealTimeNetLog getNetActionById(Long id);

    void deleteActionList(Long taskId, List<String> logIds);

}
