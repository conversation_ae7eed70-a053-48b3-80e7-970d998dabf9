package cn.ijiami.detection.service.spi;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import cn.ijiami.detection.DTO.ocr.OcrResponse;
import cn.ijiami.detection.VO.OcrDetectParams;

@FeignClient(name = "ocr", url = "${ijiami.ocr.server:localhost}")
public interface OcrFeign {

    @PostMapping(
            value = {"/ocr/extractPicTextSyn"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    OcrResponse extractPicText(@RequestBody OcrDetectParams params);

}
