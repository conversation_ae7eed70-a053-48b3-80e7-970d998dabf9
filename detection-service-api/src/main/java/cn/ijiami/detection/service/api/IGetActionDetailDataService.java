package cn.ijiami.detection.service.api;

import java.util.List;

import cn.ijiami.detection.VO.AndroidSensorLog;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import com.github.pagehelper.PageInfo;

import cn.ijiami.detection.VO.RealTimeBehaviorLog;
import cn.ijiami.detection.VO.RealTimeNetLog;
import cn.ijiami.detection.VO.detection.privacy.IOSRealTimeLog;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.query.task.TaskLogQuery;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;

/**
 * 获取应用行为、通信行为、传输个人信息行为、存储个人信息行为详细数据公共服务
 */
public interface IGetActionDetailDataService {

    Object getActionDetailDataById(Long id,String packageName,Integer type,Long taskId);

    RealTimeBehaviorLog getActionDetail(String id, Long taskId) throws IjiamiRuntimeException;

    List<RealTimeBehaviorLog> getActionListInActionIds(Long taskId, List<Long> actionIdList, List<PrivacyStatusEnum> isPersonalList, List<BehaviorStageEnum> behaviorStageList);

    PageInfo<RealTimeBehaviorLog> getDynamicLogListByPage(TaskLogQuery query);

    List<TActionNougat> getDynamicActionList(Long taskId);

    List<String> getIosDynamicActionList(Long taskId);

    IOSRealTimeLog getIosDetailLog(String id, Long taskId) throws IjiamiRuntimeException;

    List<IOSRealTimeLog> getIosDetailLogList(List<String> typeList, List<Integer> behaviorStage, List<Integer> isPersonal, Long taskId);

    PageInfo<IOSRealTimeLog> getIosDetailLogListByPage(TaskLogQuery query);

    RealTimeNetLog getNetLog(String id, Long taskId) throws IjiamiRuntimeException;

    PageInfo<RealTimeNetLog> getNetLogListByPage(TaskLogQuery query);

    PageInfo<AndroidSensorLog> getSensorLogListByPage(TaskLogQuery query);

    void cleanDynamicAction(Long taskId);
}
