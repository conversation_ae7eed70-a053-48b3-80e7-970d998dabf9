package cn.ijiami.detection.service.api;

import java.io.File;
import java.util.List;
import java.util.Map;

import cn.ijiami.detection.bean.DynamicTaskContext;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import org.springframework.web.multipart.MultipartFile;

import com.ijm.ios.RuntimeDetection.ctrl.LogCtrl;

import cn.ijiami.detection.entity.TPrivacySensitiveWord;

/**
 * <AUTHOR>
 * @date 2020/6/13
 */
public interface IPrivacyLogCrtlService {

    void logDataAnalysis(LogCtrl logCtrl, DynamicTaskContext taskData);

    void uploadIosDepthDetectionSharedPrefs(Long taskId, MultipartFile data);

    void testAct();

    List<TPrivacySensitiveWord> saveSensitiveWords(Long taskId, TPrivacyOutsideAddress outsideAddress);

    void analysisIosManualTaskData(Long taskId, String appName, Map<BehaviorStageEnum,  List<File>> sharedPrefsFileMap, String packageName);

}
