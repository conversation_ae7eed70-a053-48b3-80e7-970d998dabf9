package cn.ijiami.detection.service.api.compliance;

import cn.ijiami.detection.DTO.compliance.CompliancePersonalInfoDto;
import cn.ijiami.detection.VO.compliance.ComplianceBaseVO;
import cn.ijiami.detection.entity.compliance.TcompliancePersonalInfo;

import java.util.List;

public interface ICompliancePersonalInfoService {
    List<CompliancePersonalInfoDto> queryPersonalInfo(ComplianceBaseVO complianceBaseVo);

    Object updatePersonalInfo(List<TcompliancePersonalInfo> compliancePersonalInfoList);
}
