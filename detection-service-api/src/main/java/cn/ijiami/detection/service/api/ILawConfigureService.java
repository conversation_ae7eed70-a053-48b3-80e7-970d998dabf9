package cn.ijiami.detection.service.api;

import cn.ijiami.detection.VO.detection.privacy.PrivacyPolicyTypeVO;
import cn.ijiami.detection.entity.TPrivacyCheck;
import cn.ijiami.detection.query.DetailLawsQuery;
import cn.ijiami.detection.query.LawQuery;
import com.github.pagehelper.PageInfo;

/**
 * Description:法规配置服务接口类
 *
 * @Author:lyl
 * @Date:2023/11/28 17:12
 */

public interface ILawConfigureService {

    PageInfo<PrivacyPolicyTypeVO> findLaws(LawQuery lawQuery);

    PageInfo<TPrivacyCheck> findLawDetails(Integer terminalType,String lawName, Integer page, Integer rows) throws IllegalAccessException;

    void saveLaws(DetailLawsQuery query,Long userId);

    void updateLawStatus(Long id,Integer terminalType,Long userId,Integer pushStatus) throws Exception;

    void deleteLaws(Long id,Long userId);

    void deletelawDetails(DetailLawsQuery query,Long userId);

    void saveCache(DetailLawsQuery query,Long userId) throws IllegalAccessException;

    void clearCache(DetailLawsQuery query);
}
