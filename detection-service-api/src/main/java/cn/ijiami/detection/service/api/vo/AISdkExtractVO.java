package cn.ijiami.detection.service.api.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AISdkExtractVO.java
 * @Description ai提取的sdk
 * @createTime 2025年01月20日 11:24:00
 */
@Data
public class AISdkExtractVO {

    /**
     * sdk名称
     */
    @JsonProperty("sdk_name")
    private String sdkName;

    /**
     *
     * sdk所属公司或者机构名称
     */
    @JsonProperty("sdk_company")
    private String sdkCompany;

    /**
     *
     * sdk收集的信息
     */
    @JsonProperty("collection_info_name")
    private String collectionInfoName;

    /**
     *
     * sdk使用目的
     */
    @JsonProperty("usage_purpose")
    private String usagePurpose;

    /**
     *
     * sdk使用场景
     */
    @JsonProperty("usage_scenarios")
    private String usageScenarios;

    /**
     *
     * sdk隐私政策链接
     */
    @JsonProperty("url")
    private String url;

}
