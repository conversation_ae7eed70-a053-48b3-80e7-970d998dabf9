package cn.ijiami.detection.DTO.compliance;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class ChannelDto {

    @ApiModelProperty(value = "序号")
    private Integer serialNumber;

    private Date channel_create_time;

    private String channel_id;

    private String channel_name;

    private String detail_url;

    private Integer detail_url_status;

    private Long download_count;

    private Integer is_official_tag;

    private String out_down_url;

    private String inside_down_url;

    private Date publish_time;

    private String channel_page_name;

    private Date channel_refresh_time;

    private Integer name_similar;

    private String version;


}
