package cn.ijiami.detection.service.impl;

import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import cn.ijiami.detection.BaseJunit;
import cn.ijiami.detection.bean.DynamicTaskContext;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.service.api.CacheService;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SendMessageImplTest.java
 * @Description
 * @createTime 2021年11月19日 16:01:00
 */
public class SendMessageImplTest extends BaseJunit {

    @Autowired
    private SendMessageImpl sendMessage;

    @Autowired
    private TTaskMapper taskMapper;

    @Autowired
    private CacheService cacheService;

    @Test
    public void testSendTaskLogMessage() {
        TTask task = taskMapper.selectByPrimaryKey(18596L);
        cacheService.set(PinfoConstant.CACHE_DETECTION_LOG_TOPIC_ID + task.getTaskId(), "123", 1L, TimeUnit.HOURS);
        DynamicTaskContext data = new DynamicTaskContext();
        data.setTaskId(task.getTaskId());
        data.setCreateUserId(data.getCreateUserId());
        data.setTerminalType(data.getTerminalType());
        data.setNotificationId("123");
        sendMessage.sendTaskDynamicLogMessage("{\"test\":1}", data);
    }

    @Test
    public void testSendBroadcastMessage() {
        TTask task = taskMapper.selectByPrimaryKey(18612L);
        sendMessage.sendBroadcast("{\"deviceType\":1,\"deviceSerial\":\"172.10.23.133:5555\",\"msgType\":0,\"messageType\":15,\"cmdType\":1,\"stfToken\":\"4ea2a89d24aa414b9848841da2c987737214da2383df4178b80c29f27f784e1b\",\"dynamicType\":1,\"deviceId\":\"172.10.3.107:7649\",\"cmdData\":{\"msg\":{\"type_name\":\"\",\"action_time\":1637309362081,\"type_id\":18002,\"isPersonal\":{\"value\":0,\"name\":\"否\"},\"id\":\"1461607396139864064\"},\"progress\":0,\"type\":5,\"taskId\":\"18612\"},\"terminalType\":1,\"taskId\":18612}", task);
    }

}
