package cn.ijiami.detection.service.impl;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.io.File;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.ijiami.detection.service.api.IDynamicWechatAppletDetectionService;
import cn.ijiami.detection.VO.detection.privacy.WechatAppletAction;
import cn.ijiami.detection.analyzer.AppletBehaviorInfoAction;
import cn.ijiami.detection.analyzer.BehaviorInfoAction;
import cn.ijiami.detection.analyzer.parser.WechatAppletBehaviorInfoParser;
import cn.ijiami.detection.entity.*;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.mapper.TActionNougatMapper;
import cn.ijiami.detection.mapper.TPrivacyActionNougatMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Update;

import cn.ijiami.detection.BaseJunit;
import cn.ijiami.detection.server.client.base.enums.TerminalTypeEnum;
import cn.ijiami.detection.mapper.TAssetsMapper;
import cn.ijiami.detection.mapper.TTaskMapper;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AppletServiceImplTest.java
 * @Description
 * @createTime 2023年04月26日 18:46:00
 */
public class AppletServiceImplTest extends BaseJunit {

    @Autowired
    AppletServiceImpl appletService;

    @Autowired
    DynamicWechatAppletDetectionServiceImpl dynamicAppletDetectionService;

    @Autowired
    TTaskMapper taskMapper;


    @Autowired
    private IDynamicWechatAppletDetectionService dynamicWechatAppletDetectionService;

    @Autowired
    private TActionNougatMapper tActionNougatMapper;

    @Transactional
    @Test
    public void testActionFilter() {
        Long taskId = 1L;
        String line = "{\"deviceId\":\"FA7CL1A00963\",\"deviceType\":2,\"deviceSerial\":\"FA7CL1A00963\",\"dynamicType\":4,\"cmdType\":1,\"terminalType\":4,\"stfToken\":\"3037d2194cb54686b86ff4eb7397fe1f362d0183789c423fb6d33b9464be4be0\",\"deviceSerialNum\":\"\",\"cmdData\":{\"taskId\":\"84542\",\"msg\":{\"data\":{\"actionApi\":\"wx.getAccountInfoSync\",\"stackInfo\":\"AopWXFunc.func9 (<anonymous>:1:17144)\\n    Object.$.getAppInfoSync (https://usr/appservice.app.js:181:25579)\\n    Object.$.getAppId (https://usr/appservice.app.js:181:25739)\\n    Object.$.getOpenidNameByAppid (https://usr/appservice.app.js:181:26365)\\n    Object.toState (https://usr/appservice.app.js:181:29036)\\n    Object.init (https://usr/appservice.app.js:181:32941)\\n    Object.i.init (https://usr/appservice.app.js:181:37974)\\n    shdrInit (https://usr/appservice.app.js:177:188)\\n    https://usr/appservice.app.js:173:1068\\n    s (https://usr/appservice.app.js:156:738)\",\"returnData\":\"{\\\"miniProgram\\\":{\\\"appId\\\":\\\"wx47a7a73d243e5369\\\",\\\"envVersion\\\":\\\"release\\\",\\\"version\\\":\\\"10.6.0\\\"}}\",\"actionType\":1,\"appId\":\"wx47a7a73d243e5369\",\"appVersion\":\"10.6.0\",\"logoPicUrl\":\"http://wx.qlogo.cn/mmhead/Q3auHgzwzM7DPKcj8fp36372AXNlPPdOLRjQicUu5XJibn4VKQyXaiccw/96\",\"timeStamp\":\"*************\"},\"url\":\"https://ijmuat.zywa.com.cn/privacy-test/detection/compliance/applet/analyzeDynamic\",\"header\":{\"content-type\":\"application/x-www-form-urlencoded;charset=UTF-8\"},\"method\":\"POST\",\"timeout\":0,\"enableHttp2\":false,\"enableQuic\":false,\"enableCache\":false,\"enableProfile\":true,\"port\":443,\"forbidReuse\":false,\"host\":\"ijmuat.zywa.com.cn\",\"__skipDomainCheck__\":false,\"responseType\":\"text\"},\"type\":5,\"progress\":0},\"msgType\":0}";
        WechatAppletAction actionLog = WechatAppletBehaviorInfoParser.parserLogStr(line);
        assert actionLog != null;
        Map<Long, TActionNougat> actionNougatMap = tActionNougatMapper.findByTerminalType(TerminalTypeEnum.WECHAT_APPLET.getValue())
                .stream()
                .collect(Collectors.toMap(TActionNougat::getActionId, Function.identity(), (entity1, entity2) -> entity1));
        //获取行为
        Optional<TActionNougat> actionNougat = actionNougatMap
                .values()
                .stream()
                .filter(nougat -> org.apache.commons.lang.StringUtils.equals(nougat.getActionApi(), actionLog.getData().getActionApi()))
                .findFirst();
        assert actionNougat.isPresent();
        //入库
        TPrivacyActionNougat privacyActionNougat = AppletBehaviorInfoAction.buildBehavioral(taskId, actionLog.getData(),
                BehaviorStageEnum.BEHAVIOR_FRONT, actionNougat.get());
        List<TActionFilterGroupRegex> tActionFilterGroupRegexes = new ArrayList<>();
        TActionFilterGroupRegex regex = new TActionFilterGroupRegex();
        regex.setActionFilterRegex("getAccountInfoSync");
        tActionFilterGroupRegexes.add(regex);
        // 过滤行为
        assert BehaviorInfoAction.filterActionGroupRegex(tActionFilterGroupRegexes, privacyActionNougat.getActionId(),
                privacyActionNougat.getStackInfo(), privacyActionNougat.getDetailsData());
    }

    @Transactional
    @Test
    public void testBuild() {
        TTask query = new TTask();
        query.setTerminalType(TerminalTypeEnum.WECHAT_APPLET);
        List<TTask> taskList = taskMapper.select(query);
        for (TTask t : taskList) {
            Update update = new Update();
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("_id", t.getApkDetectionDetailId());
            update.set("applet_extra_info",
                    appletService.buildAppletInfo(new File(getClass().getResource("/test/wechatBasicInfo.xml").getFile()), Collections.emptyList()));
            dynamicAppletDetectionService.update(paramMap, update);
//            dynamicWechatAppletDetectionService.analysisDataRetry(t.getTaskId());
        }
    }

}
