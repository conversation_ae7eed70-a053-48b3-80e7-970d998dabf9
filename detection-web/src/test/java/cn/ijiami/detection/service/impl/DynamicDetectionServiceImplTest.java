package cn.ijiami.detection.service.impl;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import cn.ijiami.detection.entity.interfaces.ActionExecutor;
import jdk.nashorn.internal.ir.annotations.Ignore;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import cn.ijiami.detection.BaseJunit;
import cn.ijiami.detection.VO.SuspiciousSdkBehaviorVO;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.analyzer.bo.DetectDataBO;
import cn.ijiami.detection.analyzer.bo.SuspiciousSdkBO;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.entity.TOdpCompany;
import cn.ijiami.detection.entity.TOdpCompanyProduct;
import cn.ijiami.detection.entity.TPermission;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.entity.TSdkLibrary;
import cn.ijiami.detection.entity.TSdkLibraryPackage;
import cn.ijiami.detection.entity.TSuspiciousSdkLibrary;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.server.client.base.enums.TerminalTypeEnum;
import cn.ijiami.detection.mapper.TActionNougatMapper;
import cn.ijiami.detection.mapper.TOdpCompanyMapper;
import cn.ijiami.detection.mapper.TOdpCompanyProductMapper;
import cn.ijiami.detection.mapper.TPermissionMapper;
import cn.ijiami.detection.mapper.TPrivacyLawsBasisMapper;
import cn.ijiami.detection.mapper.TSdkLibraryMapper;
import cn.ijiami.detection.mapper.TSuspiciousSdkLibraryMapper;
import cn.ijiami.detection.mapper.TSuspiciousSdkMapper;
import cn.ijiami.detection.service.api.IDynamicIOSActionDataService;
import cn.ijiami.detection.utils.SuspiciousSdkUtils;

/**
 * <EMAIL>
 */
public class DynamicDetectionServiceImplTest extends BaseJunit {

    @Autowired
    private DynamicAndroidDetectionServiceImpl androidDetectionService;

    @Autowired
    private DynamicWechatAppletDetectionServiceImpl wechatAppletDetectionService;

    @Autowired
    private DynamicAlipayAppletDetectionServiceImpl alipayAppletDetectionService;

    @Autowired
    private DynamicHarmonyDetectionServiceImpl harmonyDetectionService;

    @Autowired
    TSdkLibraryMapper sdkLibraryMapper;

    @Autowired
    TPermissionMapper permissionMapper;

    @Autowired
    TActionNougatMapper actionNougatMapper;

    @Autowired
    TSuspiciousSdkMapper suspiciousSdkMapper;

    @Autowired
    TSuspiciousSdkLibraryMapper suspiciousSdkLibraryMapper;

    @Autowired
    IDynamicIOSActionDataService iDynamicIOSActionDataService;

    @Autowired
    TOdpCompanyProductMapper odpCompanyProductMapper;

    @Autowired
    TOdpCompanyMapper odpCompanyMapper;

    @Autowired
    TPrivacyLawsBasisMapper tPrivacyLawsBasisMapper;

    @Transactional
    @Test
    public void testFindSuspiciousSdks() {
        String suspiciousPackageName = "com.test.aaa";
        String apkName = "com.ddd.eee";
        String apkName2 = "com.ddd.eee.fff";
        String shortName = "com.baidu.c";
        String threeLevelSuspiciousPackageName = "com.bbb.ccc";
        String twoLevelSuspiciousPackageName = "com.bbb";
        String suspiciousPackageName2 = "com.bytedance.sdk";
        String suspiciousPackageName3 = "sunsun.xiaoli.jiarenbang";
        TaskDetailVO taskDetailVo = new TaskDetailVO();
        taskDetailVo.setApk_package(apkName);
        List<String> shrinkingNames = Arrays.asList("a.b.c", "ab", "a.bc.a", "c.bc");

        // 创建就公司产品数据
        List<TOdpCompanyProduct> productList = odpCompanyProductMapper.selectAll();
        Map<String, List<TOdpCompanyProduct>> productMap = productList.stream()
                .filter(product -> product.getCompanyId() != null)
                .filter(product -> StringUtils.isNotBlank(product.getProductEnglishName()))
                .collect(Collectors.groupingBy(TOdpCompanyProduct::getProductEnglishName));
        // 构造一份公司名数据
        List<TOdpCompany> companyList = odpCompanyMapper.selectAll();
        Map<String, List<TOdpCompanyProduct>> companyMap = SuspiciousSdkUtils.buildCompanyMainProduct(companyList)
                .stream()
                .filter(product -> product.getCompanyId() != null)
                .filter(product -> StringUtils.isNotBlank(product.getProductEnglishName()))
                .collect(Collectors.groupingBy(TOdpCompanyProduct::getProductEnglishName));

        List<TSdkLibrary> sdkLibraries = sdkLibraryMapper.findByTerminalType(TerminalTypeEnum.ANDROID.getValue());
        List<TSdkLibrary> firstPartyLibraries = sdkLibraryMapper.findFirstPartyByTerminalType(TerminalTypeEnum.ANDROID.getValue());

        Set<String> filterNames = new HashSet<>();
        sdkLibraries.subList(0, 100)
                .forEach(s -> {
                    filterNames.addAll(s.getPackageList().stream().map(TSdkLibraryPackage::getPackageName).collect(Collectors.toList()));
                });
        List<String> sdkNames = new ArrayList<>(filterNames);
        Pattern twoLevelNamePattern = Pattern.compile("^[a-zA-Z]+[0-9a-zA-Z_]*\\.[a-zA-Z]+[0-9a-zA-Z_]*$");
        List<String> twoLevelNameSdk = sdkNames.stream().filter(p -> twoLevelNamePattern.matcher(p).matches()).collect(Collectors.toList());
        String twoLevelName = twoLevelNameSdk.get(0);

        String sdkFull = sdkNames.get(0) + ".common.SPUtility2.readFromSPUnified";
        String stackInfo1 = "java.io.FileInputStream.<init>(FileInputStream.java:142)" +
                "<---com.taobao.wireless.security.adapter.common.SPUtility2.a(Unknown Source:42)" +
                "<---com.taobao.wireless.security.adapter.common.SPUtility2.b(Unknown Source:38)" +
                "<---com.taobao.wireless.security.adapter.common.SPUtility2.readFromSPUnified(Unknown Source:28)" +
                "<---" + sdkFull + "(Unknown Source:28)";

        String stackInfo2 = "java.io.FileInputStream.<init>(FileInputStream.java:142)" +
                "<---" + taskDetailVo.getApk_package() + ".aaa(Unknown Source:42)" +
                "<---" + suspiciousPackageName + ".ccc(Unknown Source:42)" +
                "<---android.app.SharedPreferencesImpl.writeToFile(SharedPreferencesImpl.java:760)" +
                "<---sun.xxx.dada.oo(SharedPreferencesImpl.java:760)" +
                "<---" + twoLevelName + ".oo(SharedPreferencesImpl.java:760)" +
                "<---" + apkName2 + ".oo(SharedPreferencesImpl.java:760)" +
                "<---" + shortName + "(SharedPreferencesImpl.java:760)" +
                "<---com.google.android.gms(Unknown Source:28)";
        String stackInfo3 = "<---" + threeLevelSuspiciousPackageName + ".a(SharedPreferencesImpl.java:760)" +
                "<---" + twoLevelSuspiciousPackageName + ".a(SharedPreferencesImpl.java:760)";

        String stackInfo4 =
                "<---" + suspiciousPackageName2 + ".openadsdk.a(SharedPreferencesImpl.java:760)" +
                        "<---" + suspiciousPackageName3 + ".app.App.onCreated(SharedPreferencesImpl.java:760)";

        List<TPrivacyActionNougat> privacyActionNougats = new ArrayList<>();
        TPrivacyActionNougat nougat1 = new TPrivacyActionNougat();
        nougat1.setPackageName(sdkNames.get(0));
        nougat1.setExecutorType(ExecutorTypeEnum.SDK.getValue());
        nougat1.setExecutor("java");
        nougat1.setStackInfo(stackInfo1);
        privacyActionNougats.add(nougat1);

        TPrivacyActionNougat nougat2 = new TPrivacyActionNougat();
        nougat2.setPackageName(taskDetailVo.getApk_package());
        nougat2.setExecutorType(ExecutorTypeEnum.APP.getValue());
        nougat2.setExecutor("test");
        nougat2.setStackInfo(stackInfo2);
        privacyActionNougats.add(nougat2);

        TPrivacyActionNougat nougat3 = new TPrivacyActionNougat();
        nougat3.setPackageName(taskDetailVo.getApk_package());
        nougat3.setExecutorType(ExecutorTypeEnum.APP.getValue());
        nougat3.setExecutor("test");
        nougat3.setStackInfo(stackInfo3);
        privacyActionNougats.add(nougat3);



        TPrivacyActionNougat nougat4 = new TPrivacyActionNougat();
        nougat4.setPackageName(taskDetailVo.getApk_package());
        nougat4.setExecutorType(ExecutorTypeEnum.APP.getValue());
        nougat4.setExecutor("test");
        nougat4.setStackInfo(stackInfo4);
        privacyActionNougats.add(nougat4);

        sdkNames.forEach(name -> {
            TPrivacyActionNougat address = new TPrivacyActionNougat();
            address.setExecutorType(ExecutorTypeEnum.SDK.getValue());
            address.setStackInfo(name);
            privacyActionNougats.add(address);
        });

        shrinkingNames.forEach(name -> {
            TPrivacyActionNougat address = new TPrivacyActionNougat();
            address.setExecutorType(ExecutorTypeEnum.SDK.getValue());
            address.setStackInfo(name);
            privacyActionNougats.add(address);
        });

        DetectDataBO testData = new DetectDataBO();
        testData.setPrivacyActionNougats(privacyActionNougats);

        List<TPrivacyOutsideAddress> privacyOutsideAddresses = new ArrayList<>();
        TPrivacyOutsideAddress address = new TPrivacyOutsideAddress();
        address.setExecutorType(ExecutorTypeEnum.SDK.getValue());
        address.setExecutor("test2");
        address.setStackInfo(stackInfo2);
        privacyOutsideAddresses.add(address);

        TPrivacyOutsideAddress address2 = new TPrivacyOutsideAddress();
        address2.setExecutorType(ExecutorTypeEnum.APP.getValue());
        address2.setExecutor("test3");
        address2.setStackInfo("");
        privacyOutsideAddresses.add(address2);

        TPrivacyOutsideAddress address3 = new TPrivacyOutsideAddress();
        address3.setExecutorType(ExecutorTypeEnum.SDK.getValue());
        address3.setExecutor("test3");
        address3.setStackInfo("");
        privacyOutsideAddresses.add(address3);
        testData.setPrivacyOutsideAddresses(privacyOutsideAddresses);

        Map<BehaviorStageEnum, DetectDataBO> detectDataMap = new HashMap<>();
        detectDataMap.put(BehaviorStageEnum.BEHAVIOR_FRONT, testData);
        List<SuspiciousSdkBO> suspiciousSdkBOList = androidDetectionService.findSuspiciousSdks(detectDataMap, taskDetailVo, sdkLibraries, firstPartyLibraries);
        List<String> packageNames = suspiciousSdkBOList
                .stream().map(s -> s.getSuspiciousSdk().getPackageName()).collect(Collectors.toList());

        // 有疑似包名
        assert packageNames.contains(suspiciousPackageName);
        assert packageNames.contains(suspiciousPackageName2);
        assert packageNames.contains(suspiciousPackageName3);
        // 没有android和java
        assert !packageNames.contains("com.google.android.gms");
        assert !packageNames.contains("java.io.FileInputStream");
        assert !packageNames.contains("sun.xxx.dada");
        assert !packageNames.contains(taskDetailVo.getApk_package());
        // 与应用包名相同的也要过滤掉
        assert !packageNames.contains(apkName2);

        assert packageNames.stream().noneMatch(p -> p.startsWith(twoLevelName));
        // 没有SDK库的包名
        assert packageNames.stream().noneMatch(p -> sdkNames.stream().anyMatch(p::startsWith));
        // 没有混淆后的包名
        assert packageNames.stream().noneMatch(p -> shrinkingNames.stream().anyMatch(sdkName -> sdkName.equals(p)));
        // SDK主体调用，如果包名前3级和SDK库里的相同，也要被过滤掉
        assert !packageNames.contains(sdkFull);

        // 提取的sdk名中，如果有com.bbb.ccc时，就不要把com.bbb提取出来
        assert !packageNames.contains(twoLevelSuspiciousPackageName);
        assert packageNames.contains(threeLevelSuspiciousPackageName);

        assert nougat2.getExecutor().contains(suspiciousPackageName);
        assert nougat2.getExecutorType().equals(ExecutorTypeEnum.SDK.getValue());
        assert nougat2.getPackageName().contains(suspiciousPackageName);
        // 更新后的包名和主体名个数要一致
        assert nougat1.getPackageName().split(",").length == nougat1.getExecutor().split(",").length;

        Optional<TOdpCompanyProduct> companyProduct = SuspiciousSdkUtils.findCompanyProduct(shortName, productMap, companyMap);
        String shortSdkName = companyProduct.isPresent() ? companyProduct.get().getProductName() : shortName;
        assert address.getExecutor().equals("test2," + suspiciousPackageName + "," + shortSdkName);
        assert address.getExecutorType().equals(ExecutorTypeEnum.SDK.getValue());
        // 没有疑似sdk的保持和原来一致
        assert address2.getExecutor().equals("test3");
        assert address2.getExecutorType().equals(ExecutorTypeEnum.APP.getValue());
        assert address3.getExecutor().equals("test3");
        assert address3.getExecutorType().equals(ExecutorTypeEnum.SDK.getValue());
    }

    @Test
    @Transactional
    public void testGetNewSdkPermissionCode() {
        List<TSdkLibrary> sdkLibraries = androidDetectionService.makeSdkPackageNameMap(sdkLibraryMapper.findAll()).values().stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        List<TPermission> permissions = permissionMapper.findByTerminalType(null, TerminalTypeEnum.ANDROID.getValue());
        List<TActionNougat> actionNougats = actionNougatMapper.findByTerminalType(TerminalTypeEnum.ANDROID.getValue());
        TSdkLibrary library1 = sdkLibraries.get(0);
        if (StringUtils.isBlank(library1.getPermissionCodes())) {
            library1.setPermissionCodes("AUTH_011");
        }
        TSdkLibrary library2 = sdkLibraries.get(1);
        if (StringUtils.isBlank(library2.getPermissionCodes())) {
            library2.setPermissionCodes("AUTH_011");
        }
        TSdkLibrary library3 = sdkLibraries.get(2);
        library3.setPermissionCodes("");

        List<TPrivacyActionNougat> privacyActionNougats = new ArrayList<>();
        TPrivacyActionNougat notExistNougat = new TPrivacyActionNougat();
        notExistNougat.setPackageName(library1.getPackageList().get(0).getPackageName());
        notExistNougat.setExecutorType(ExecutorTypeEnum.SDK.getValue());
        // 找出不在sdk里的权限
        List<TPermission> newPermissions = permissions.stream()
                .filter(p -> p.getPermissionCode() != null)
                .filter(p -> !library1.getPermissionCodes().contains(p.getPermissionCode()))
                .collect(Collectors.toList());
        notExistNougat.setActionId(findActionId(actionNougats, newPermissions.get(0).getName()));
        privacyActionNougats.add(notExistNougat);

        TPrivacyActionNougat existPermission = new TPrivacyActionNougat();
        existPermission.setPackageName(library1.getPackageList().get(0).getPackageName());
        existPermission.setExecutorType(ExecutorTypeEnum.SDK.getValue());
        // 找出已经在sdk里的权限
        String[] existPermissionCodes = library1.getPermissionCodes().split(",");
        String existName = permissions.stream().filter(p -> p.getPermissionCode().equals(existPermissionCodes[0])).findFirst().get().getName();
        existPermission.setActionId(findActionId(actionNougats, existName));
        privacyActionNougats.add(existPermission);

        TPrivacyActionNougat nougat3 = new TPrivacyActionNougat();
        nougat3.setPackageName(library2.getPackageList().get(0).getPackageName());
        nougat3.setExecutorType(ExecutorTypeEnum.SDK.getValue());
        // 找出不在sdk里的权限
        List<TPermission> newPermissions2 = permissions.stream().filter(p -> !library2.getPermissionCodes().contains(p.getPermissionCode()))
                .collect(Collectors.toList());
        nougat3.setActionId(findActionId(actionNougats, newPermissions2.get(0).getName()));
        privacyActionNougats.add(nougat3);

        TPrivacyActionNougat nougat4 = new TPrivacyActionNougat();
        nougat4.setPackageName(library2.getPackageList().get(0).getPackageName());
        nougat4.setExecutorType(ExecutorTypeEnum.SDK.getValue());
        privacyActionNougats.add(nougat4);

        TPrivacyActionNougat nougat5 = new TPrivacyActionNougat();
        nougat5.setPackageName(library3.getPackageList().get(0).getPackageName());
        nougat5.setExecutorType(ExecutorTypeEnum.SDK.getValue());
        privacyActionNougats.add(nougat5);


        DetectDataBO testData = new DetectDataBO();
        testData.setPrivacyActionNougats(privacyActionNougats);
        Map<BehaviorStageEnum, DetectDataBO> detectDataMap = new HashMap<>();
        detectDataMap.put(BehaviorStageEnum.BEHAVIOR_FRONT, testData);


        Map<String, TPermission> permissionMap = permissionMapper.findByTerminalType(null, TerminalTypeEnum.ANDROID.getValue())
                .stream().collect(Collectors.toMap(TPermission::getName, Function.identity()));
        Map<Long, TActionNougat> actionNougatsMap = actionNougatMapper.findByTerminalType(TerminalTypeEnum.ANDROID.getValue())
                .stream().collect(Collectors.toMap(TActionNougat::getActionId, Function.identity(), (key1, key2) -> key2));

        Map<Long, String> updatePermissionCodeMap = androidDetectionService.findUpdatePermissionCode(detectDataMap, sdkLibraries, actionNougatsMap, permissionMap);

        assert updatePermissionCodeMap.get(library1.getId()).contains(newPermissions.get(0).getPermissionCode());
        assert updatePermissionCodeMap.get(library1.getId()).contains(existPermissionCodes[0]);
        assert updatePermissionCodeMap.get(library2.getId()).contains(newPermissions2.get(0).getPermissionCode());
        assert updatePermissionCodeMap.get(library3.getId()) == null;

        String testPackageName1 = "com.test1.dfadfas";
        String testPackageName2 = "com.test2.dfadfas";

        long taskId = 123L;

        List<SuspiciousSdkBO> suspiciousSdkBOs = new ArrayList<>();
        SuspiciousSdkBehaviorVO behaviorVO = new SuspiciousSdkBehaviorVO();
        behaviorVO.setPackageName(testPackageName1);
        behaviorVO.setExecutor(behaviorVO.getPackageName());
        behaviorVO.setExecutorType(ExecutorTypeEnum.SDK.getValue());
        behaviorVO.setTaskId(taskId);
        SuspiciousSdkBO bo1 = new SuspiciousSdkBO(behaviorVO, existPermission);
        suspiciousSdkBOs.add(bo1);

        SuspiciousSdkBehaviorVO behaviorVO2 = new SuspiciousSdkBehaviorVO();
        behaviorVO2.setPackageName(testPackageName2);
        behaviorVO2.setExecutor(behaviorVO.getPackageName());
        behaviorVO2.setExecutorType(ExecutorTypeEnum.SDK.getValue());
        behaviorVO2.setTaskId(taskId);
        SuspiciousSdkBO bo2 = new SuspiciousSdkBO(behaviorVO2, existPermission);
        suspiciousSdkBOs.add(bo2);

        // 先构造一个疑似SDK库数据
        TSuspiciousSdkLibrary suspiciousSdkLibrary = new TSuspiciousSdkLibrary();
        suspiciousSdkLibrary.setName(testPackageName2);
        suspiciousSdkLibrary.setTerminalType(TerminalTypeEnum.ANDROID.getValue());
        suspiciousSdkLibrary.setPackageName(suspiciousSdkLibrary.getName());
        suspiciousSdkLibrary.setPermissionCodes("AUTH_011");
        suspiciousSdkLibraryMapper.insert(suspiciousSdkLibrary);

        androidDetectionService.updateSuspiciousSdkLibraryCodesAndInsertRecord(taskId, suspiciousSdkBOs, actionNougatsMap, permissionMap, TerminalTypeEnum.ANDROID);

        List<TSuspiciousSdkLibrary> sdkLibList = suspiciousSdkLibraryMapper.findInPackageName(Arrays.asList(testPackageName1, testPackageName2));
        Optional<TSuspiciousSdkLibrary> sdkLibrary1 = sdkLibList.stream().filter(s -> s.getPackageName().equals(testPackageName1)).findFirst();
        Optional<TSuspiciousSdkLibrary> sdkLibrary2 = sdkLibList.stream().filter(s -> s.getPackageName().equals(testPackageName2)).findFirst();

        assert sdkLibrary1.isPresent();
        String actionPermission = actionNougatsMap.get(existPermission.getActionId()).getActionPermission();
        assert sdkLibrary1.get().getPermissionCodes().equals(permissionMap.get(actionPermission).getPermissionCode());
        // 没有重复权限
        List<String> codes = Arrays.asList(sdkLibrary1.get().getPermissionCodes().split(","));
        assert codes.size() == new HashSet<>(codes).size();

        assert sdkLibrary2.isPresent();
        assert sdkLibrary2.get().getPermissionCodes().contains(permissionMap.get(actionPermission).getPermissionCode());
        assert sdkLibrary2.get().getPermissionCodes().contains(suspiciousSdkLibrary.getPermissionCodes());

        List<String> codes2 = Arrays.asList(sdkLibrary2.get().getPermissionCodes().split(","));
        assert codes2.size() == new HashSet<>(codes2).size();
    }

    private long findActionId(List<TActionNougat> actionNougats, String permissionName) {
        return actionNougats.stream().filter(a -> permissionName.equals(a.getActionPermission())).findFirst().get().getActionId();
    }

    @Transactional
    @Test
    public void testMakeSdkPackageNameMap() {
        List<TSdkLibrary> sdkLibraries = new ArrayList<>();
        sdkLibraries.add(buildSdk("test1", "com.xxx.dd", "1.2.0", new Date()));
        sdkLibraries.add(buildSdk("test1", "com.xxx.dd", "1.2.1", new Date()));
        sdkLibraries.add(buildSdk("test2", "com.bbb.dd", "1.2.1.ver", new Date(System.currentTimeMillis() - 10000000)));
        sdkLibraries.add(buildSdk("test2", "com.bbb.dd", "1.2.1.ver3", new Date(System.currentTimeMillis() - 20000000)));
        sdkLibraries.add(buildSdk("test2", "com.bbb.dd", "1.2.1.bbb", new Date()));

        TSdkLibrary normalSdk = buildSdk("test3", "com.ccc.dd", "avc", new Date());
        sdkLibraries.add(normalSdk);

        TSdkLibrary eSdk = buildSdk("test2", "com.bbb.dd", "1.2.1.bbb", new Date());
        eSdk.setPackageList(null);
        sdkLibraries.add(eSdk);

        Map<String, List<TSdkLibrary>> listMap = androidDetectionService.makeSdkPackageNameMap(sdkLibraries);
        List<TSdkLibrary> test1 = listMap.get(sdkLibraries.get(0).getPackageList().get(0).getPackageName());
        // 新旧两个版本时，只保留新的sdk，version可以判断时根据version判断
        assert test1.size() == 1;
        assert test1.get(0).getVersion().equals(sdkLibraries.get(1).getVersion());

        List<TSdkLibrary> test2 = listMap.get(sdkLibraries.get(2).getPackageList().get(0).getPackageName());
        // 新旧两个版本时，只保留新的sdk，version不可以判断时根据创建时间判断
        assert test2.size() == 1;
        assert test2.get(0).getVersion().equals(sdkLibraries.get(4).getVersion());

        assert !listMap.get(normalSdk.getPackageList().get(0).getPackageName()).isEmpty();
    }

    private TSdkLibrary buildSdk(String name, String packageName, String version, Date createTime) {
        TSdkLibrary library = new TSdkLibrary();
        List<TSdkLibraryPackage> packageList = new ArrayList<>();
        TSdkLibraryPackage TSdkLibraryPackage = new TSdkLibraryPackage();
        TSdkLibraryPackage.setPackageName(packageName);
        packageList.add(TSdkLibraryPackage);
        library.setName(name);
        library.setVersion(version);
        library.setCreatedTime(createTime);
        library.setPackageList(packageList);
        return library;
    }

    @Ignore
    @Test
    public void testUpdateAlipayAppletInfo() {
        alipayAppletDetectionService.updateAppletAssets(7544L, "", "",
                "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",
                "");
    }

    @Test
    @Transactional
    public void testWechatAppletDetection() throws Exception {
        File file = new File("E:/zywa/a79c778abd93a81c55436ca7cf9d18da_82496_AUTO.tar.gz");
        if (file.exists()) {
            Matcher m = Pattern.compile("_([0-9]+)_").matcher(file.getAbsolutePath());
            if (m.find()) {
                long taskId = Long.parseLong(m.group(1));
                MultipartFile multipartFile = new MockMultipartFile(file.getName(), new FileInputStream(file));
                wechatAppletDetectionService.analysisAutoAgain(taskId, multipartFile);
            }
        }
    }

    @Test
    @Transactional
    public void testAlipayAppletDetection() throws Exception {
        File file = new File("E:/zywa/dfd36685804d4bd96546c06ea8b934f1_81270_AUTO.tar.gz");
        if (file.exists()) {
            Matcher m = Pattern.compile("_([0-9]+)_").matcher(file.getAbsolutePath());
            if (m.find()) {
                long taskId = Long.parseLong(m.group(1));
                MultipartFile multipartFile = new MockMultipartFile(file.getName(), new FileInputStream(file));
                alipayAppletDetectionService.analysisAutoAgain(taskId, multipartFile);
            }
        }
    }


    @Test
    @Transactional
    public void testAliPayAppletDetectionRetry() throws Exception {
        alipayAppletDetectionService.analysisDataRetry(83735L);
    }

    @Test
    @Transactional
    public void testWechatAppletDetectionRetry() throws Exception {
        wechatAppletDetectionService.analysisDataRetry(82496L);
    }

    @Test
    @Transactional
    public void testAndroidDetectionRetry() throws Exception {
        androidDetectionService.analysisDataRetry(1852L);
    }


    @Test
    @Transactional
    public void testAndroidDetection() throws Exception {
        File file = new File("E:/zywa/a_2646_AUTO.zip");
        if (file.exists()) {
            Matcher m = Pattern.compile("_([0-9]+)_").matcher(file.getAbsolutePath());
            if (m.find()) {
                long taskId = Long.parseLong(m.group(1));
                MultipartFile multipartFile = new MockMultipartFile(file.getName(), new FileInputStream(file));
                androidDetectionService.analysisAutoAgain(taskId, multipartFile);
            }
        }
    }

    @Test
    @Transactional
    public void testAndroidDetectionManual() throws Exception {
        File file = new File("C:/Users/<USER>/Desktop/431546363586dfde4a63fc9f4ae7ea5b_74051_MANUAL.zip");
        if (file.exists()) {
            Matcher m = Pattern.compile("_([0-9]+)_").matcher(file.getAbsolutePath());
            if (m.find()) {
                long taskId = Long.parseLong(m.group(1));
                MultipartFile multipartFile = new MockMultipartFile(file.getName(), new FileInputStream(file));
                androidDetectionService.analysisManual(taskId, multipartFile);
            }
        }
    }

    @Test
    @Transactional
    public void testMultiAppIosDetection() throws Exception {
        List<String> com108 = Arrays.asList(
                "5b6d01fc7b6bde762617eece66bb368d_16944_AUTO.tar.gz",
                "06e3f8e52fcd1afdcbf413e5f7db80f8_16806_AUTO.tar.gz",
                "f9277f49793dab07daebc0cc05f85259_16854_AUTO.tar.gz");
        List<String> inv108 = Arrays.asList(
                "3b783e3951965525ead02c2295cdd828_16595_AUTO.tar.gz",
                "3faaf8ecb75209b70ca4ca20067a80b6_16452_AUTO.tar.gz",
                "5e0ffca99dc5794003dd19ecd80b3475_16446_AUTO.tar.gz",
                "31fc0c861f1a8487c1ad7783b412aa56_16586_AUTO.tar.gz",
                "145f435f422ef3b2f484eaac9a8cb0ed_16453_AUTO.tar.gz",
                "589b44b853dc9a87478704e18c54f2a3_16515_AUTO.tar.gz",
                "676e9fa25080899857a249747d4f83a7_16598_AUTO.tar.gz",
                "3299baf1477e2bf462528f564a373a3f_16559_AUTO.tar.gz",
                "8164f0d0daaee8dcc9fd7f3c2c0af8ff_16939_AUTO.tar.gz",
                "73898208929e9c36c4da24314968a677_16626_AUTO.tar.gz",
                "d4229d672774dbcec7f009c4fef8ad0a_16928_AUTO.tar.gz",
                "21b8ff633bbefa267380bad1a9dc7e44_16982_AUTO.tar.gz");
        for (String name: com108) {
            long taskId = Long.parseLong(name.split("_")[1]);
            File file = new File("D:/test/" + name);
            MultipartFile multipartFile = new MockMultipartFile(file.getName(), new FileInputStream(file));
            iDynamicIOSActionDataService.analysisAutoAgain(taskId, multipartFile);
        }
    }

    @Test
    @Transactional
    public void testIosDetection() throws Exception {
        String name = "8ad14a1431ba9b65726946ce54366e03_31_AUTO.tar.gz";
        File file = new File("E:/zywa/" + name);
        long taskId = Long.parseLong(name.split("_")[1]);
        MultipartFile multipartFile = new MockMultipartFile(file.getName(), new FileInputStream(file));
        iDynamicIOSActionDataService.analysisAutoAgain(taskId, multipartFile);
    }

    @Test
    @Transactional
    public void testHarmonyDetection() throws Exception {
        File file = new File("E:/zywa/d52602e63a4f416302049c0c26b55155_84154_AUTO.tar.gz");
        if (file.exists()) {
            Matcher m = Pattern.compile("_([0-9]+)_").matcher(file.getAbsolutePath());
            if (m.find()) {
                long taskId = Long.parseLong(m.group(1));
                MultipartFile multipartFile = new MockMultipartFile(file.getName(), new FileInputStream(file));
                harmonyDetectionService.analysisAutoAgain(taskId, multipartFile);
            }
        }
    }

    @Test
    @Transactional
    public void testHarmonyDetectionRetry() throws Exception {
        harmonyDetectionService.analysisDataRetry(87041L);
    }
}
