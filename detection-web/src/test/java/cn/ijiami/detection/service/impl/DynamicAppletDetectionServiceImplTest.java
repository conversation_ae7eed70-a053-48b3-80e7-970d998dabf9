package cn.ijiami.detection.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import cn.ijiami.detection.VO.RealTimeBehaviorLog;
import cn.ijiami.detection.analyzer.bo.DetectDataBO;
import cn.ijiami.detection.bean.ResultContent;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.service.api.CacheService;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Update;

import com.fasterxml.jackson.core.type.TypeReference;

import cn.ijiami.detection.BaseJunit;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.bean.AppletBaseResultContentDTO;
import cn.ijiami.detection.bean.DetectionItem;
import cn.ijiami.detection.bean.AppletPermissionResultContentDTO;
import cn.ijiami.detection.enums.DetectionItemIdEnum;
import cn.ijiami.detection.enums.DetectionItemStatusEnum;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.utils.CommonUtil;

import static cn.ijiami.detection.constant.IdbMsgFieldName.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DynamicAppletDetectionServiceImplTest.java
 * @Description 小程序解析单元测试
 * @createTime 2023年04月20日 17:49:00
 */
public class DynamicAppletDetectionServiceImplTest extends BaseJunit {

    @Autowired
    private DynamicWechatAppletDetectionServiceImpl dynamicAppletDetectionService;

    @Autowired
    private AppletIdbDetectionServiceImpl appletIdbDetectionService;

    @Autowired
    private TTaskMapper taskMapper;

    @Autowired
    private CacheService cacheService;

    @Test
    public void testAnalysisPrivacyCheck() {
        long[] taskIds = new long[]{79802, 79809, 79814, 79818, 79808, 79822, 79824};
        for (long taskId : taskIds) {
            Map<BehaviorStageEnum, DetectDataBO> detectDataBOMap = dynamicAppletDetectionService.getManualDetectionData(taskId);
            TTask task = taskMapper.selectByPrimaryKey(taskId);
            dynamicAppletDetectionService.analysisPrivacyCheck(task, detectDataBOMap, null);
        }
    }

    @Test
    public void testBuildMessage() {
        RealTimeBehaviorLog behaviorLog = new RealTimeBehaviorLog();
        behaviorLog.setActionTime(System.currentTimeMillis());
        behaviorLog.setDetailsData("1");
        behaviorLog.setStackInfo("2");
        behaviorLog.setIsPersonal(PrivacyStatusEnum.YES);
        behaviorLog.setTypeId(2L);
        behaviorLog.setTypeName("测试");
        behaviorLog.setId("10");
        long taskId = 1L;
        JSONObject message = appletIdbDetectionService.buildDynamicLogMessage(behaviorLog, taskId);
        JSONObject cmdData = message.getJSONObject(CMD_DATA);
        JSONObject log = cmdData.getJSONObject(CMD_DATA_ANDROID_OR_APPLET_LOG);
        assert taskId == cmdData.getLong(TASK_ID);
        assert behaviorLog.getTypeId() == log.getLong(CMD_DATA_ANDROID_OR_APPLET_LOG_ACTION_ID);
        assert behaviorLog.getTypeName().equals(log.getString(CMD_DATA_ANDROID_OR_APPLET_LOG_ACTION_NAME));
        assert behaviorLog.getActionTime() == log.getLong(CMD_DATA_ANDROID_OR_APPLET_ACTION_TIME);
        assert CommonUtil.beanToJson(behaviorLog.getIsPersonal()).equals(log.getString(CMD_DATA_ANDROID_OR_APPLET_LOG_PERSONAL));
        assert behaviorLog.getId().equals(log.getString(CMD_DATA_ANDROID_OR_APPLET_LOG_ID));
        assert StringUtils.isBlank(log.optString(CMD_DATA_ANDROID_JAVA_STACK, ""));
        assert StringUtils.isBlank(log.optString(CMD_DATA_ANDROID_LOG_DETAILS_DATA, ""));
    }

    @Test
    public void testUpdateVersionName() throws Exception {
        TaskDetailVO taskDetailVO = new TaskDetailVO();
        try {
            dynamicAppletDetectionService.save(taskDetailVO);
            Map<String, Object> paramMap = new HashMap<>();
            List<DetectionItem<?>> itemList = new ArrayList<>();
            DetectionItem<AppletBaseResultContentDTO> base = new DetectionItem<>();
            base.setDetectionItemId(DetectionItemIdEnum.BASE.getValue());
            base.setDetectionItemName(DetectionItemIdEnum.BASE.getName());
            base.setStatus(DetectionItemStatusEnum.SAFE.getValue());
            AppletBaseResultContentDTO content1 = new AppletBaseResultContentDTO();
            content1.setAppName("app");
            content1.setVersionName("1");
            base.setResultContent(content1);
            itemList.add(base);

            DetectionItem<AppletPermissionResultContentDTO> permission = new DetectionItem<>();
            permission.setDetectionItemId(DetectionItemIdEnum.PERMISSION.getValue());
            permission.setDetectionItemName(DetectionItemIdEnum.PERMISSION.getName());
            permission.setStatus(DetectionItemStatusEnum.SAFE.getValue());
            AppletPermissionResultContentDTO content2 = new AppletPermissionResultContentDTO();
            List<String> stringList = new ArrayList<>();
            stringList.add("test");
            content2.setPermissionList(stringList);
            permission.setResultContent(content2);
            itemList.add(permission);

            Update update = new Update();
            paramMap.put("_id", taskDetailVO.getId());
            update.set("detection_result", itemList);
            dynamicAppletDetectionService.update(paramMap, update);

            TaskDetailVO query = dynamicAppletDetectionService.findById(taskDetailVO.getId());
            List<DetectionItem<ResultContent>> itemList1 = CommonUtil.jsonToBean(query.getDetection_result().toString(), new TypeReference<List<DetectionItem<ResultContent>>>() {
            });
            assert itemList1
                    .stream()
                    .anyMatch(item -> item.getDetectionItemId().equals(DetectionItemIdEnum.BASE.getValue())
                            && ((AppletBaseResultContentDTO) item.getResultContent()).getVersionName().equals("1"));
            dynamicAppletDetectionService.updateTaskVersion(taskDetailVO.getId(), "", "2");

            TaskDetailVO query2 = dynamicAppletDetectionService.findById(taskDetailVO.getId());
            List<DetectionItem<ResultContent>> itemList2 = CommonUtil.jsonToBean(query2.getDetection_result().toString(), new TypeReference<List<DetectionItem<ResultContent>>>() {
            });
            assert itemList2
                    .stream()
                    .anyMatch(item -> item.getDetectionItemId().equals(DetectionItemIdEnum.BASE.getValue())
                            && ((AppletBaseResultContentDTO) item.getResultContent()).getVersionName().equals("2"));
        } finally {
            if (StringUtils.isNotBlank(taskDetailVO.getId())) {
                Map<String, Object> param = new HashMap<>();
                param.put("_id", taskDetailVO.getId());
                dynamicAppletDetectionService.remove(param);
            }
        }
    }

    @Test
    public void testSendError() throws Exception {
        String nowAppId = "2";
        Long taskId = 1L;
        cacheService.set(PinfoConstant.CACHE_MINI_PROGRAM_APP_ID + taskId, "default", 2L, TimeUnit.HOURS);
        String pre = appletIdbDetectionService.preActionApi(taskId);
        if (!nowAppId.equals(pre) && cacheService.compareAndSet(appletIdbDetectionService.actionApiKey(taskId), pre, nowAppId, TimeUnit.HOURS.toMillis(2))) {
            assert true;
        } else {
            assert false;
        }
        assert appletIdbDetectionService.preActionApi(taskId).equals(nowAppId);
    }

}
