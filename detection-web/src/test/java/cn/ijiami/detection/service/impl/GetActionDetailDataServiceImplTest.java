package cn.ijiami.detection.service.impl;

import java.time.format.DateTimeFormatter;
import java.util.Collections;

import cn.ijiami.detection.bean.DynamicTaskContext;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.helper.AndroidActionLogConvertHelper;
import cn.ijiami.detection.mapper.TActionNougatMapper;
import cn.ijiami.detection.service.DetectionDataService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.github.pagehelper.PageInfo;

import cn.ijiami.detection.BaseJunit;
import cn.ijiami.detection.VO.RealTimeBehaviorLog;
import cn.ijiami.detection.VO.detection.privacy.IOSRealTimeLog;
import cn.ijiami.detection.query.task.TaskLogQuery;
import cn.ijiami.detection.service.api.CacheService;
import cn.ijiami.detection.service.api.IdGeneratorService;
import net.sf.json.JSONObject;

import static cn.ijiami.detection.constant.IdbMsgFieldName.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName GetActionDetailDataServiceImplTest.java
 * @Description 测试类
 * @createTime 2021年12月23日 15:52:00
 */
public class GetActionDetailDataServiceImplTest extends BaseJunit {


    @Autowired
    private IdGeneratorService idGeneratorService;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private DetectionDataService detectionDataService;

    @Autowired
    private GetActionDetailDataServiceImpl getActionDetailDataService;

    @Autowired
    private TActionNougatMapper actionNougatMapper;

    @Autowired
    private AndroidActionLogConvertHelper convertHelper;

    @Test
    public void testGetDynamicDetectionDetailListByPage() throws Exception {
        Long taskId = 1L;
        detectionDataService.cleanDynamicAction(taskId);
        try {
            int total = 13;
            for (int i=0; i<total; i++) {
                JSONObject msgJson = new JSONObject();
                msgJson.put("id", idGeneratorService.nextId());
                msgJson.put("stack_info", "com.abc." + i);
                msgJson.put("details_data", "123");
                msgJson.put("action_time", System.currentTimeMillis() + i);
                msgJson.put("type_name", "");
                msgJson.put("type_id", 10002);
                msgJson.put(CMD_DATA_ANDROID_OR_APPLET_LOG_BEHAVIOR_STAGE, BehaviorStageEnum.BEHAVIOR_FRONT.getValue());
                DynamicTaskContext taskData = new DynamicTaskContext();
                taskData.setSdkLibraries(Collections.emptyList());
                taskData.setAppName("test");
                taskData.setPackageName("com.abc." + i);
                detectionDataService.insertDynamicAction(convertHelper.buildPrivacyActionNougat(taskId, msgJson, taskData, PrivacyStatusEnum.YES), taskData);
            }

            TaskLogQuery query = new TaskLogQuery();
            query.setTaskId(taskId);
            query.setPage(1);
            query.setRows(10);
            PageInfo<RealTimeBehaviorLog> info = getActionDetailDataService.getDynamicLogListByPage(query);

            assert info.getList().size() == query.getRows();
            assert Long.parseLong(info.getList().get(0).getId()) > Long.parseLong(info.getList().get(1).getId());
            assert info.getSize() == query.getRows();
            assert info.getPages() == 2;
            assert info.getTotal() == total;
            assert info.getPageSize() == query.getRows();
            assert info.getPageNum() == query.getPage();


            TaskLogQuery query2 = new TaskLogQuery();
            query2.setTaskId(taskId);
            query2.setPage(2);
            query2.setRows(10);
            PageInfo<RealTimeBehaviorLog> info2 = getActionDetailDataService.getDynamicLogListByPage(query2);

            assert info2.getList().size() == 3;
            assert Long.parseLong(info2.getList().get(0).getId()) > Long.parseLong(info2.getList().get(1).getId());
            assert info2.getSize() == 3;
            assert info2.getPages() == 2;
            assert info2.getTotal() == total;
            assert info2.getPageSize() == query2.getRows();
            assert info2.getPageNum() == query2.getPage();
        } finally {
            detectionDataService.cleanDynamicAction(taskId);
        }
    }

    @Test
    public void testIosTimeFormatter() {
        DateTimeFormatter millisFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
        DateTimeFormatter secondFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        IOSRealTimeLog log1 = new IOSRealTimeLog();
        log1.setStrTime("2021-12-29 16:58:49.246");
        long time = GetActionDetailDataServiceImpl.parseLogTime(log1, millisFormatter, secondFormatter);
        assert time > 0;

        IOSRealTimeLog log2 = new IOSRealTimeLog();
        log2.setStrTime("2021-12-29 16:58:49.24");
        long time2 = GetActionDetailDataServiceImpl.parseLogTime(log2, millisFormatter, secondFormatter);
        assert time2 > 0;
        assert time > time2;
    }
}
