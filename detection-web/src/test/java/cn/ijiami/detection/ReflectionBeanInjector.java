package cn.ijiami.detection;

import org.springframework.aop.framework.Advised;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ReflectionBeanInjector.java
 * @Description TODO
 * @createTime 2025年03月27日 19:51:00
 */
@Component
public class ReflectionBeanInjector {

    @Autowired
    private ApplicationContext applicationContext;

    @PostConstruct
    public void injectBeans() throws Exception {
        // 获取目标 Bean（例如 UserDetailsServiceImpl）
        Object userDetailsServiceImpl = getTargetObject(applicationContext.getBean("ijiamiUserDetailsServiceImpl"));

        // 获取 tokenStore Bean
        Object tokenStore = applicationContext.getBean("tokenStore");

        // 使用反射为 UserDetailsServiceImpl 的 tokenStore 字段赋值
        Field tokenStoreField = userDetailsServiceImpl.getClass().getDeclaredField("tokenStore");
        tokenStoreField.setAccessible(true);
        tokenStoreField.set(userDetailsServiceImpl, tokenStore);
    }

    public static Object getTargetObject(Object proxy) throws Exception {
        if (AopUtils.isAopProxy(proxy)) {
            return ((Advised) proxy).getTargetSource().getTarget();
        }
        return proxy;
    }
}