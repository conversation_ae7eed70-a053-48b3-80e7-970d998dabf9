package cn.ijiami.detection.miit.point.ios.law164;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import cn.ijiami.detection.BaseJunit;
import cn.ijiami.detection.analyzer.bo.DetectDataBO;
import cn.ijiami.detection.config.IjiamiCommonProperties;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacyLawsBasis;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.PrivacyLawId;
import cn.ijiami.detection.enums.PrivacyLawsBasisCategoryEnum;
import cn.ijiami.detection.server.client.base.enums.TerminalTypeEnum;
import cn.ijiami.detection.mapper.TActionNougatMapper;
import cn.ijiami.detection.mapper.TPrivacyLawsBasisMapper;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.service.impl.DynamicAndroidDetectionServiceImpl;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName Detection163Test.java
 * @Description
 * @createTime 2022年01月24日 16:09:00
 */
public class Detection163Test extends BaseJunit {

    @Autowired
    private TPrivacyLawsBasisMapper privacyLawsBasisMapper;
    @Autowired
    private IjiamiCommonProperties commonProperties;
    @Autowired
    private TActionNougatMapper actionNougatMapper;
    @Autowired
    private DynamicAndroidDetectionServiceImpl androidDetectionService;

    @Test
    public void test() {
        DetectPoint1020203 detection = new DetectPoint1020203();
        CommonDetectInfo commonDetectInfo = new CommonDetectInfo();
        CustomDetectInfo customDetectInfo = new CustomDetectInfo();

        List<TPrivacyActionNougat> privacyActionNougats = new ArrayList<>();


        privacyActionNougats.add(makeNougat(110048L, System.currentTimeMillis()));
        privacyActionNougats.add(makeNougat(110048L, System.currentTimeMillis() + 1000));

        privacyActionNougats.add(makeNougat(110049L, System.currentTimeMillis()));
        privacyActionNougats.add(makeNougat(110049L, System.currentTimeMillis() + 1000));

        Map<BehaviorStageEnum, DetectDataBO> detectDataMap = new HashMap<>();
        DetectDataBO detectDataBO = new DetectDataBO();
        detectDataBO.setPrivacyActionNougats(privacyActionNougats);
        detectDataMap.put(BehaviorStageEnum.BEHAVIOR_FRONT, detectDataBO);
        commonDetectInfo.setDetectDataMap(detectDataMap);


        String item = "1020203";
        List<TPrivacyLawsBasis> regulations = privacyLawsBasisMapper.selectItemNoInfoByLawId(PrivacyLawId.IOS_LAW_164.id);
        Map<String, List<TPrivacyLawsBasis>> itemNoWithActionAndKeys = regulations.stream().collect(Collectors.groupingBy(TPrivacyLawsBasis::getItemNo));
        Set<Long> actionIds = new HashSet<>();
        Set<String> keysWords = new HashSet<>();
        Map<Long, String> actionKey = new HashMap<>(16);
        List<TPrivacyLawsBasis> lawsBases = itemNoWithActionAndKeys.get(item);
        lawsBases.forEach(basis -> {
            // 有可能actionId在关键词表没有数据，需要先加入到行为id集合中，避免漏加
            if (basis.getCategory() == PrivacyLawsBasisCategoryEnum.ACTION_ID) {
                actionIds.add(basis.getActionId());
            }
            androidDetectionService.getLawsBaseKeyWords(basis).forEach(words -> {
                if (Objects.nonNull(words.getActionId())) {
                    actionIds.add(words.getActionId());
                }
                if (Objects.nonNull(words.getKeyWords())) {
                    keysWords.add(words.getKeyWords());
                }
                if (Objects.nonNull(words.getActionId()) && Objects.nonNull(words.getKeyWords())) {
                    actionKey.put(words.getActionId(), words.getKeyWords());
                }
            });
        });
        Map<String, String> itemCheckWord = new HashMap<>();
        String point_10107_agree = commonProperties.getProperty("point_10107_agree");
        String point_10107_refuse = commonProperties.getProperty("point_10107_refuse");
        try {
            point_10107_agree = new String(point_10107_agree.getBytes("iso-8859-1"), "utf-8");
            point_10107_refuse = new String(point_10107_refuse.getBytes("iso-8859-1"), "utf-8");
        } catch (Exception e1) {
            e1.getMessage();
        }

        itemCheckWord.put("point_10107_agree", point_10107_agree);
        itemCheckWord.put("point_10107_refuse", point_10107_refuse);
        itemCheckWord.put("self_starting_white_list", commonProperties.getProperty("self_starting_white_list"));

        customDetectInfo.setItemNo(item);
        customDetectInfo.setDecideRuleActionIds(actionIds);
        customDetectInfo.setDecideRuleKeys(keysWords);
        customDetectInfo.setActionWithKeyRegex(actionKey);
        customDetectInfo.setKeyWordRegex(itemCheckWord);
        customDetectInfo.setCloudPhoneAddressBook(Collections.emptyMap());

        // 查询行为数据
        List<TActionNougat> actionNougats = actionNougatMapper.findByTerminalType(TerminalTypeEnum.ANDROID.getValue());
        Map<Long, TActionNougat> actionNougatMap = actionNougats.stream().collect(Collectors.toMap(TActionNougat::getActionId, Function.identity(),(entity1, entity2) -> entity1));
        commonDetectInfo.setTaskId(1L);
        commonDetectInfo.setApkPackageName("com.app.test");
        commonDetectInfo.setApkName("");
        commonDetectInfo.setApkTargetSdkVersion(18);
        commonDetectInfo.setFilePath("");
        commonDetectInfo.setPrivacyPolicyMinTextLineSpacing(16);
        commonDetectInfo.setPrivacyPolicyMinTextSize(16);
        commonDetectInfo.setActionNougatMap(actionNougatMap);
        commonDetectInfo.setDetectDataMap(detectDataMap);
        commonDetectInfo.setSdkTypePushIds(Collections.emptySet());
        commonDetectInfo.setTerminalTypeEnum(TerminalTypeEnum.IOS);
        commonDetectInfo.setApplyPermissions(Collections.emptyList());

        DetectResult result = detection.doDetect(commonDetectInfo, customDetectInfo);

        assert result.getComplianceStatus() == MiitDetectStatusEnum.NON_COMPLIANCE;
    }

    private TPrivacyActionNougat makeNougat(Long id, Long time) {
        TPrivacyActionNougat actionNougat1 = new TPrivacyActionNougat();
        actionNougat1.setActionId(id);
        actionNougat1.setDetailsData("");
        actionNougat1.setStackInfo("");
        actionNougat1.setPackageName("com.app.test");
        actionNougat1.setActionTimeStamp(time);
        return actionNougat1;
    }

}
