package cn.ijiami.detection.miit.example;

import cn.ijiami.detection.analyzer.bo.DetectDataBO;
import cn.ijiami.detection.analyzer.helper.DynamicFileReaderHelper;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.miit.callback.DefaultDetectCallback;
import cn.ijiami.detection.miit.callback.IDetectCallback;
import cn.ijiami.detection.miit.domain.*;
import cn.ijiami.detection.miit.kit.MiitLogKit;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 检测示例
 *
 * <AUTHOR>
 * @date 2020-12-22 14:35
 */

public class DetectExample {
    private static final String PATH =
            "E:\\E0-workspace\\E0-1-privacy-detection\\E0-1-08 privacy-detection-web-2.3.2\\pinfo-detection\\detection-service-impl\\src\\test\\resources\\miit\\";

    /**
     * 补充数据源
     *
     * @return
     */
    protected static Map<Long, TActionNougat> getActionNougat() {
        String str = DynamicFileReaderHelper.readFileToString(PATH + "actionNougat.json");
        List<TActionNougat> actions = JSONArray.parseArray(str, TActionNougat.class);
        return actions.stream().collect(Collectors.toMap(TActionNougat::getActionId, Function.identity()));
    }

    /**
     * 检测行为数据源
     *
     * @return
     */
    protected static Map<BehaviorStageEnum, DetectDataBO> getDetectDataMap() {
        Map<BehaviorStageEnum, DetectDataBO> detectDataBOMap = new HashMap<>(8);
        String str = DynamicFileReaderHelper.readFileToString(PATH + "privacyActionNougat.json");
        List<TPrivacyActionNougat> actions = JSONArray.parseArray(str, TPrivacyActionNougat.class);
        actions.stream().collect(Collectors.groupingBy(TPrivacyActionNougat::getBehaviorStage)).forEach((k, v) -> {
            System.err.println(k.getName() + ":" + v.size());
            DetectDataBO detectDataBO = new DetectDataBO();
            detectDataBO.setPrivacyActionNougats(v);
            detectDataBOMap.put(k, detectDataBO);
        });
        return detectDataBOMap;
    }

    /**
     * 检测行为数据源
     *
     * @return
     */
    protected static List<ResultDataLogBO> getResultDataLogs() {
        return MiitLogKit.readLogToBeanByAbsolutePath(PATH + "resultDataLog");
    }

    protected static CommonDetectInfo commonDetectInfo() {
        CommonDetectInfo commonDetectInfo = new CommonDetectInfo();
        commonDetectInfo.setTaskId(5974L);
        commonDetectInfo.setApkPackageName("com.fangdd.mobile.fddhouseownersell");
        //        commonDetectInfo.setFilePath();
        //        commonDetectInfo.setPrivacyPolicyImg();
        //        commonDetectInfo.setPrivacyPolicyContent();
        commonDetectInfo.setActionNougatMap(getActionNougat());
        commonDetectInfo.setDetectDataMap(getDetectDataMap());
        commonDetectInfo.setResultDataLogs(getResultDataLogs());
        commonDetectInfo.setFilePath(PATH);
        return commonDetectInfo;
    }

    protected static DetectResult detectPoint(AbstractDetectPoint detectPoint, CustomDetectInfo customDetectInfo, boolean hasPrivacyPolicy) {
        // 公共参数
        CommonDetectInfo commonDetectInfo = commonDetectInfo();
        commonDetectInfo.setHasPrivacyPolicy(hasPrivacyPolicy);
        // 默认回调
        IDetectCallback callback = new DefaultDetectCallback();
        // 开始执行
        detectPoint.startDetect(commonDetectInfo, customDetectInfo, callback);
        // 打印检测结果
        DetectResult result = callback.getResult();
        System.err.println("任务ID：" + result.getTaskId() + "，检测点:" + result.getItemNo() + "，合规状态:" + result.getComplianceStatus().getName());
        System.err.println("总次数:" + result.getCount());
        List<ActionAnalyse> analysisResult = Optional.ofNullable(result.getAnalysisResult()).orElse(new ArrayList<>());
        for (ActionAnalyse actionAnalyse : analysisResult) {
            System.err.println(JSONObject.toJSONString(actionAnalyse));
        }
        return result;
    }

}
