<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TActionNougatMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TActionNougat">
        <id column="id" property="id"/>
        <result column="action_id" property="actionId"/>
        <result column="action_name" property="actionName"/>
        <result column="action_permission" property="actionPermission"/>
        <result column="action_permission_alias" property="actionPermissionAlias"/>
        <result column="sensitive" property="sensitive"/>
        <result column="is_personal" property="personal"/>
        <result column="action_api" property="actionApi"/>
    </resultMap>

    <select id="findByTerminalTypeAndActionId" resultMap="BaseResultMap">
        select id,action_id,action_name,action_permission,action_permission_alias,`sensitive`,is_personal,action_api from t_action_nougat
        where action_id = #{actionId} and terminal_type = #{TerminalType} limit 1
    </select>

    <select id="findByTerminalType" resultMap="BaseResultMap">
        select id,action_id,action_name,action_permission,action_permission_alias,`sensitive`,is_personal,action_api from t_action_nougat
        where terminal_type = #{TerminalType}
    </select>

    <select id="findInTerminalTypeAndActionId" resultMap="BaseResultMap">
        select id,action_id,action_name,action_permission,action_permission_alias,`sensitive`,is_personal,action_api from t_action_nougat
        where action_id in
        <foreach collection="actionIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and terminal_type = #{terminalType}
    </select>


    <select id="findInActionId" resultMap="BaseResultMap">
        select id,action_id,action_name,action_permission,action_permission_alias,`sensitive`,is_personal,action_api from t_action_nougat
        where action_id in
        <foreach collection="actionIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="findActionIdByNames" resultType="java.lang.Long">
        SELECT action_id FROM t_action_nougat
        WHERE action_name IN
        <foreach collection="actionNameList" item="action_name" open="(" separator="," close=")">
            #{action_name}
        </foreach>
        AND terminal_type = #{terminalType}
        GROUP BY action_id
    </select>

    <select id="selectPersonalAction" resultType="cn.ijiami.detection.VO.PersonalActionVO">
        select action_id as actionId,action_name as actionName from t_action_nougat where terminal_type = #{terminalType} and is_personal =1
    </select>

    <select id="queryActionNougat" resultType="cn.ijiami.detection.entity.TActionNougat">
        SELECT
            DISTINCT
            t2.id,
            t2.action_id AS actionId,
            t2.action_name AS actionName,
            t2.is_personal AS personal
        FROM
            t_privacy_action_nougat t1 join t_action_nougat t2 on t1.action_id = t2.action_id
        WHERE
            t2.terminal_type = #{terminalType}
          AND t1.task_id = #{taskId}
    </select>

    <select id="findActionTypeName" resultType="java.lang.String">
        select DISTINCT action_name from t_action_nougat where 1=1
        <if test="ids !=null and ids.size()>0 ">
            and id in
            <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
