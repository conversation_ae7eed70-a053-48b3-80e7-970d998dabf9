<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.CustomDepartmentMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.organ.department.entity.Department">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="department_id" jdbcType="BIGINT" property="departmentId" />
        <result column="parent_department_id" jdbcType="BIGINT" property="parentDepartmentId" />
        <result column="department_name" jdbcType="VARCHAR" property="departmentName" />
        <result column="department_master" jdbcType="BIGINT" property="departmentMaster" />
        <result column="department_code" jdbcType="VARCHAR" property="departmentCode" />
        <result column="company_id" jdbcType="BIGINT" property="companyId" />
        <result column="created_user_id" jdbcType="BIGINT" property="createdUserId" />
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
        <result column="updated_user_id" jdbcType="BIGINT" property="updatedUserId" />
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
    </resultMap>

    <select id="isSubDepartmentUserId" resultType="java.lang.Long">
        SELECT tou.userId FROM torgan_user_info AS tou INNER JOIN torgan_department tod ON tod.department_id = tou.department_id
        WHERE tod.department_code LIKE CONCAT((SELECT department_code FROM torgan_department WHERE department_id = #{departmentId}),'%%')
        AND tou.user_info_id=#{userId};
    </select>
</mapper>