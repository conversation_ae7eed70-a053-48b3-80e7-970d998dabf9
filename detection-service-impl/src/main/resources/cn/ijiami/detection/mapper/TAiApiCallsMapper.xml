<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.ijiami.detection.mapper.TAiApiCallsMapper">

    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TAiApiCalls">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="auth_code_id" property="authCodeId" jdbcType="VARCHAR"/>
        <result column="api_endpoint" property="apiEndpoint" jdbcType="VARCHAR"/>
        <result column="call_time" property="callTime" jdbcType="TIMESTAMP"/>
        <result column="ip_address" property="ipAddress" jdbcType="VARCHAR"/>
        <result column="success" property="success" jdbcType="INTEGER"/>
        <result column="error_message" property="errorMessage" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="BaseColumn">
        id, auth_code_id, api_endpoint, call_time, ip_address, success, error_message
    </sql>

    <select id="findByAuthCodeId" resultMap="BaseResultMap">
        SELECT <include refid="BaseColumn"/>
        FROM t_ai_api_calls
        WHERE auth_code_id = #{authCodeId,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByAuthCodeId">
        DELETE FROM t_ai_api_calls
        WHERE auth_code_id = #{authCodeId,jdbcType=VARCHAR}
    </delete>

</mapper>