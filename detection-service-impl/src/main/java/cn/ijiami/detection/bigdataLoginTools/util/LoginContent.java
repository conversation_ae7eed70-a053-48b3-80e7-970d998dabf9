package cn.ijiami.detection.bigdataLoginTools.util;

public class LoginContent {
    public static final String CLIENT_ID = "client_id";
    public static final String CLIENTID = "clientid";
    public static final String CLIENT_ID_VALUE = "client_1";
    public static final String CLIENT_SECRET = "client_secret";
    public static final String CLIENT_SECRET_VALUE = "123";
    public static final String GRANT_TYPE = "grant_type";
    public static final String REFRESH_TOKEN = "refresh_token";
    public static final String URL_OAUTH_TOKEN = "https://abd.ijiami.cn/armp-gateway/armp-auth/oauth/token";
    public static final String USERNAME = "username";
    public static final String PASSWORD = "password";
    public static final String BASIC_ = "Basic ";
    public static final String BEARER_ = "Bearer ";
    public static final String AUTHORIZATION = "Authorization";
    public static final String DATA = "data";
    public static final String GETTOKENDATASTR = "getTokenDataStr";
    public static final String SCOPE = "scope";
    public static final String READ = "read";
    public static final String ACCESS_TOKEN = "access_token";
    public static final String SIGN = "sign";
    public static final String TIMESTAMP = "timestamp";

    public LoginContent() {
    }
}
