package cn.ijiami.detection.client;

import java.io.IOException;
import java.util.Date;

import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.PostMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSONObject;

import cn.ijiami.framework.web.controller.BaseController;

/**
 * 统一认证接口客户端
 */
@Component
public class AuthTokenClient extends BaseController {

    private static final Logger logger= LoggerFactory.getLogger(AuthTokenClient.class);

    @Value("${ijiami.auth.client_id:}")
    private String clientId;

    @Value("${ijiami.auth.client_secret:}")
    private String clientSecret;

    @Value("${ijiami.auth.authorize.url:}")
    private String authorizeUrl;

    @Value("${server.port:}")
    private String port;

    @Value("${server.contextPath:}")
    private String path;

    @Value("${ijiami.auth.host:}")
    private String host;

    @Value("${ijiami.auth.homePath:}")
    private String homePath;

    @Autowired
    private RestTemplate restTemplate;


    /**
     * 根据code获取用户accessToken
     *
     * @param code
     * @return
     */
    public JSONObject token(String code) throws IOException {
        StringBuilder sb = new StringBuilder();
        sb.append(authorizeUrl);
        sb.append("/openapi/oauth/token");
        PostMethod postMethod = null;
        postMethod = new PostMethod(sb.toString());
        postMethod.setRequestHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
//        String type=homePath.split(":")[0];
        String authorizeRedirectUrl=host;
//        if ("https".equals(type)) {
//            authorizeRedirectUrl = type+"://" + host;
//        } else {
//            authorizeRedirectUrl = type+"://" + host + ":" + port;
//        }

        //安全加固不拼服务名
        /*if (!"safe-encrypt".equals(client_id)) {
            if (StringUtils.isNotEmpty(path)) {
                authorizeRedirectUrl = authorizeRedirectUrl + path;
            }
        }*/
        long authTime = System.currentTimeMillis();
        authorizeRedirectUrl = authorizeRedirectUrl + "/detection/manager/authentication/in/receiveCode";
        NameValuePair[] data = {
                new NameValuePair("grant_type", "authorization_code"),
                //new NameValuePair("scope", scope),
                new NameValuePair("client_id", clientId),
                new NameValuePair("client_secret", clientSecret),
                new NameValuePair("redirect_uri", authorizeRedirectUrl),
                new NameValuePair("oauth_timestamp",String.valueOf(authTime)),
                new NameValuePair("code", code),
        };
        logger.info("根据code换取access_token,{},参数{}", sb.toString(), data);
        postMethod.setRequestBody(data);
        org.apache.commons.httpclient.HttpClient httpClient = new org.apache.commons.httpclient.HttpClient();
        int response = httpClient.executeMethod(postMethod); // 执行POST方法
        String result = postMethod.getResponseBodyAsString();
        return JSONObject.parseObject(result);
    }


    /**
     * 根据accessToken获取用户信息
     *
     * @param token
     * @return
     */
    public JSONObject getUser(String token) {
        StringBuilder sb = new StringBuilder();
        sb.append(authorizeUrl);
        sb.append("/openapi/oauth/userinfo?access_token=").append(token);
        logger.info("获取用户信息,{}", sb.toString());
        return httpGet(sb.toString());
    }

    private JSONObject httpGet(String getUrl) throws RestClientException {
        ResponseEntity<String> response = restTemplate.getForEntity(getUrl, String.class);
        return JSONObject.parseObject(response.getBody());
    }
}
