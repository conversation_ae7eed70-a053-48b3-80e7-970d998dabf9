package cn.ijiami.detection.job;

import cn.ijiami.detection.VO.AppletExtraInfoVO;
import cn.ijiami.detection.bean.*;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.entity.TComplianceAppletPlugins;
import cn.ijiami.detection.enums.DetectionItemIdEnum;
import cn.ijiami.detection.enums.DetectionItemStatusEnum;
import cn.ijiami.detection.enums.PrivacyDetectionResultEnum;
import cn.ijiami.detection.server.client.base.enums.TerminalTypeEnum;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * xxl检测结果相关数据解析
 * <p>原只是用于ios检测结果的解析，先需求需要，Android也用于此体系，通过终端类型区分，后期可能还会添加<b>小程序、公众号、SDK</b>等</p>
 *
 * <AUTHOR>
 * @date 2020-08-13 11:00
 */
@Slf4j
public class XxlDetectAnalysisHelper {

    private XxlDetectAnalysisHelper() {
    }

    /**
     * 解析检测结果
     *
     * @param terminal        终端类型
     * @param detectionEndTime 检测时间
     * @param results 检测结果
     * @return 符合个人信息要求的检测项数据格式
     */
    public static List<Map<String, Object>> analysis(TerminalTypeEnum terminal, String detectionEndTime, Map<String, String> results) {
        List<Map<String, Object>> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(results)) {
            return list;
        }
        for (Map.Entry<String, String> result : results.entrySet()) {
            String key = result.getKey();
            if ("apk_detection_starttime".equals(key) || "apk_detection_endtime".equals(key)) {
                continue;
            }
            String value = (String) result.getValue();
            JSONObject detectionItemResult = JSONObject.parseObject(value);
            switch (terminal) {
                case IOS:
                    list.add(iosDetectionResultFormat(detectionItemResult, detectionEndTime));
                    break;
                case ANDROID:
                case HARMONY:
                    list.add(androidDetectionResultFormat(detectionItemResult));
                    break;
                default:
                    break;
            }
        }
        return list;
    }

    /**
     * 安卓检测结果格式化
     * <pre>
     *   android - 检测项结果
     *   {"type_id":"7","status":1,"detection_item_id":"0205","detection_item_name":"","extra":"","grade":"中","describe":"","over_time":"","result_content":""}
     * </pre>
     *
     * @param detectionItemResult 检测项检测结果
     * @return 清洗安卓检测项检测结果
     */
    private static Map<String, Object> androidDetectionResultFormat(JSONObject detectionItemResult) {
        Map<String, Object> resultMap = new HashMap<>(16);
        resultMap.put("status", detectionItemResult.getInteger("status"));
        resultMap.put("type_id", detectionItemResult.getString("type_id"));
        resultMap.put("detection_item_id", detectionItemResult.getString("detection_item_id"));
        resultMap.put("extra", detectionItemResult.getString("extra"));
        resultMap.put("grade", detectionItemResult.getString("grade"));
        resultMap.put("describe", detectionItemResult.getString("describe"));
        resultMap.put("over_time", detectionItemResult.get("over_time"));
        resultMap.put("detection_item_name", detectionItemResult.getString("detection_item_name"));
        resultMap.put("result_content", detectionItemResult.get("result_content"));
        return resultMap;
    }

    public static String harmonyAppVersion(Map<String, String> results) {
        if (CollectionUtils.isEmpty(results)) {
            return "";
        }
        try {
            for (Map.Entry<String, String> result : results.entrySet()) {
                String key = result.getKey();
                if ("apk_detection_starttime".equals(key) || "apk_detection_endtime".equals(key)) {
                    continue;
                }
                String value = result.getValue();
                JSONObject detectionItemResult = JSONObject.parseObject(value);
                if (DetectionItemIdEnum.BASE.getValue().equals(detectionItemResult.getString("detection_item_id"))) {
                    JSONObject childJson = detectionItemResult.getJSONObject("result_content");
                    String version = childJson.getString("versionName");
                    return Objects.isNull(version) ? StringUtils.EMPTY : version;
                }
            }
        } catch (Exception e) {
            log.error("解析鸿蒙信息失败", e);
        }
        return "";
    }

    /**
     * ios检测结果格式化
     * <pre>
     *     ios - 检测项结果
     *     {"type_id":"21","grade":"低","id":"0401","name":"ARC自动内存管理","details":"ARC自动内存管理","result":11}
     * </pre>
     *
     * @param detectionItemResult 检测结果
     * @param detectionEndTime    检测结束时间
     * @return 清洗IOS检测项检测结果
     */
    private static Map<String, Object> iosDetectionResultFormat(JSONObject detectionItemResult, String detectionEndTime) {
        Map<String, Object> resultMap = new HashMap<>(16);
        //11安全 10不安全 9受保护
        resultMap.put("status", detectionItemResult.getInteger("result") == 11
                ? DetectionItemStatusEnum.SAFE.getValue() : DetectionItemStatusEnum.UNSAFE.getValue());
        resultMap.put("type_id", detectionItemResult.getString("type_id"));
        resultMap.put("detection_item_id", detectionItemResult.getString("id"));
        resultMap.put("extra", null);
        resultMap.put("grade", detectionItemResult.getString("grade"));
        resultMap.put("describe", null);
        resultMap.put("over_time", detectionEndTime);
        resultMap.put("detection_item_name", detectionItemResult.getString("name"));
        resultMap.put("result_content", detectionItemResult.get("details"));
        return resultMap;
    }


    /**
     * 支付宝小程序检测结果格式化
     * <pre>
     *   android - 检测项结果
     *   {"type_id":"7","status":1,"detection_item_id":"0205","detection_item_name":"","extra":"","grade":"中","describe":"","over_time":"","result_content":""}
     * </pre>
     *
     * @param detectionItemResult 检测项检测结果
     * @return 清洗安卓检测项检测结果
     */
    public static List<DetectionItem<? extends ResultContent>> alipayAppletDetectionResultFormat(TAssets assets, JSONObject detectionItemResult) {
        String createTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(System.currentTimeMillis());
        List<DetectionItem<? extends ResultContent>> detectionItems = new ArrayList<>();
        detectionItems.add(buildAppletBaseInfoItem(assets.getName(), assets.getAppId(), assets.getVersion(), createTime));
        List<String> permissionList = new ArrayList<>();
        JSONObject permission = detectionItemResult.getJSONObject("permission");
        if (permission != null) {
            for (Map.Entry<String, Object> entry:permission.entrySet()) {
                permissionList.add(entry.getKey());
            }
        }
        detectionItems.add(buildAppletPermissionItem(permissionList, createTime));
        return detectionItems;
    }

    /**
     * 微信小程序检测结果格式化
     * <pre>
     *   android - 检测项结果
     *   {"type_id":"7","status":1,"detection_item_id":"0205","detection_item_name":"","extra":"","grade":"中","describe":"","over_time":"","result_content":""}
     * </pre>
     *
     * @param detectionItemResult 检测项检测结果
     * @return 转换为安卓格式的检测项检测结果
     */
    public static List<DetectionItem<? extends ResultContent>> wechatAppletDetectionResultFormat(TAssets assets, WechatAppletStaticDetectionResult detectionItemResult) {
        String createTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(System.currentTimeMillis());
        List<DetectionItem<? extends ResultContent>> detectionItems = new ArrayList<>();
        detectionItems.add(buildAppletBaseInfoItem(assets.getName(), assets.getAppId(), assets.getVersion(), createTime));
        List<String> permissionList = new ArrayList<>();
        Map<String, String> permissionMap = detectionItemResult.getPermission();
        if (!CollectionUtils.isEmpty(permissionMap)) {
            for (Map.Entry<String, String> entry:permissionMap.entrySet()) {
                permissionList.add(entry.getKey());
            }
        }
        detectionItems.add(buildAppletPermissionItem(permissionList, createTime));
        detectionItems.addAll(buildAppletPersonalInfoRiskItem(detectionItemResult.getSafety(), createTime));
        return detectionItems;
    }

    public static DetectionItem<AppletBaseResultContentDTO> buildAppletBaseInfoItem(String appName, String appId, String versionName, String createTime) {
        DetectionItem<AppletBaseResultContentDTO> baseInfoItem = new DetectionItem<>();
        baseInfoItem.setOverTime(createTime);
        baseInfoItem.setDetectionItemId(DetectionItemIdEnum.BASE.getValue());
        baseInfoItem.setDetectionItemName(DetectionItemIdEnum.BASE.getName());
        baseInfoItem.setStatus(DetectionItemStatusEnum.SAFE.getValue());
        AppletBaseResultContentDTO resultContent = new AppletBaseResultContentDTO();
        resultContent.setAppName(appName);
        resultContent.setPackageName(appId);
        resultContent.setVersionName(versionName);
        baseInfoItem.setResultContent(resultContent);
        return baseInfoItem;
    }

    public static DetectionItem<AppletPermissionResultContentDTO> buildAppletPermissionItem(List<String> permissionList, String createTime) {
        DetectionItem<AppletPermissionResultContentDTO> baseInfoItem = new DetectionItem<>();
        baseInfoItem.setOverTime(createTime);
        baseInfoItem.setDetectionItemId(DetectionItemIdEnum.PERMISSION.getValue());
        baseInfoItem.setDetectionItemName(DetectionItemIdEnum.PERMISSION.getName());
        baseInfoItem.setStatus(DetectionItemStatusEnum.SAFE.getValue());
        AppletPermissionResultContentDTO resultContent = new AppletPermissionResultContentDTO();
        resultContent.setPermissionList(permissionList);
        baseInfoItem.setResultContent(resultContent);
        return baseInfoItem;
    }

    public static List<DetectionItem<AppletPersonalInfoRiskResultContentDTO>> buildAppletPersonalInfoRiskItem(String safety, String createTime) {
        List<DetectionItem<AppletPersonalInfoRiskResultContentDTO>> itemList = new ArrayList<>();
        JSONObject jsonObject = JSONObject.parseObject(safety);
        itemList.add(buildPersonalInfoRiskItem(DetectionItemIdEnum.EMAIL, jsonObject, createTime));
        itemList.add(buildPersonalInfoRiskItem(DetectionItemIdEnum.IP, jsonObject, createTime));
        itemList.add(buildPersonalInfoRiskItem(DetectionItemIdEnum.WEBSITE, jsonObject, createTime));
        itemList.add(buildPersonalInfoRiskItem(DetectionItemIdEnum.PHONE, jsonObject, createTime));
        return itemList;
    }

    private static List<String> dataList(JSONObject jsonObject, DetectionItemIdEnum itemId) {
        try {
            if (jsonObject != null) {
                JSONArray item = jsonObject.getJSONArray(itemId.getName());
                if (item != null) {
                    return item.toJavaList(String.class);
                }
            }
        } catch (Exception e) {
            log.error("解析小程序风险漏洞检测数据异常", e);
        }
        return Collections.emptyList();
    }

    private static DetectionItem<AppletPersonalInfoRiskResultContentDTO> buildPersonalInfoRiskItem(DetectionItemIdEnum itemIdEnum, JSONObject jsonObject, String createTime) {
        List<String> dataList = dataList(jsonObject, itemIdEnum);
        DetectionItem<AppletPersonalInfoRiskResultContentDTO> item = new DetectionItem<>();
        item.setOverTime(createTime);
        item.setDetectionItemId(itemIdEnum.getValue());
        item.setDetectionItemName(itemIdEnum.getName());
        item.setTypeId(itemIdEnum.getTypeId());
        item.setGrade(itemIdEnum.getGrade());
        item.setDescribe("该应用" + (dataList.isEmpty() ? "不存在" : "存在") + itemIdEnum.getName() + "风险");
        item.setStatus(dataList.isEmpty() ? PrivacyDetectionResultEnum.SAFE.getStatus() : PrivacyDetectionResultEnum.RISK.getStatus());
        AppletPersonalInfoRiskResultContentDTO resultContent = new AppletPersonalInfoRiskResultContentDTO();
        resultContent.setRiskList(dataList);
        item.setResultContent(resultContent);
        return item;
    }

    public static AppletExtraInfoVO alipayAppletExtraInfoFormat(TAssets assets, JSONObject detectionItemResult) {
        AppletExtraInfoVO extraInfoVO = new AppletExtraInfoVO();
        if (Objects.nonNull(detectionItemResult)){
            extraInfoVO.setAppId(assets.getAppId());
            //小程序名称
            extraInfoVO.setNickname(assets.getName());
            //账号主体
            extraInfoVO.setAccountSubject(optString(detectionItemResult, "账号主体", PinfoConstant.DETAILS_EMPTY));
            //原始ID
            extraInfoVO.setOriginalId(PinfoConstant.DETAILS_EMPTY);
            //服务隐私及数据提示
            extraInfoVO.setPrivacyData(optString(detectionItemResult, "用户隐私及数据保护", PinfoConstant.DETAILS_EMPTY));
            //服务声明
            extraInfoVO.setStatement(optString(detectionItemResult, "服务声明", PinfoConstant.DETAILS_EMPTY));
            //更新时间
            extraInfoVO.setUpdateTime(optString(detectionItemResult, "更新时间", PinfoConstant.DETAILS_EMPTY));
            //服务类目
            extraInfoVO.setServiceCategory(optString(detectionItemResult, "服务类目", PinfoConstant.DETAILS_EMPTY));
            //引用插件
            extraInfoVO.setPluginAppIds(Collections.emptyList());
            //引用插件
            extraInfoVO.setPlugins(PinfoConstant.DETAILS_EMPTY);
            //授权服务商
            extraInfoVO.setServiceProvider(PinfoConstant.DETAILS_EMPTY);
        }
        return extraInfoVO;
    }

    public static AppletExtraInfoVO wechatAppletInfoFormat(WechatAppletStaticDetectionResult result,
                                                           List<TComplianceAppletPlugins> newPluginsList) {
        AppletExtraInfoVO extraInfoVO = new AppletExtraInfoVO();
        if (Objects.nonNull(result)) {
            extraInfoVO.setAppId(result.getAppId());
            //小程序名称
            extraInfoVO.setNickname(result.getNickname());
            //账号主体
            extraInfoVO.setAccountSubject(result.getName());
            //原始ID
            extraInfoVO.setOriginalId(result.getUsername());
            //服务隐私及数据提示
            extraInfoVO.setPrivacyData(result.getServiceAndData());
            //服务声明
            extraInfoVO.setStatement(result.getServiceStatement());
            //更新时间
            extraInfoVO.setUpdateTime(result.getUpdateTime());
            //服务类目
            extraInfoVO.setServiceCategory(result.getCategoryList());
            //引用插件
            extraInfoVO.setPluginAppIds(newPluginsList.stream().map(TComplianceAppletPlugins::getPluginAppid).collect(Collectors.toList()));
            //引用插件
            extraInfoVO.setPlugins(newPluginsList.stream().map(TComplianceAppletPlugins::getPluginName).collect(Collectors.joining("、")));
            //授权服务商
            extraInfoVO.setServiceProvider(result.getAuth3reList());
            //小程序sdk列表
            if (Objects.nonNull(result.getStaticSdks()) && Objects.nonNull(result.getStaticSdks().getSdk())) {
                Set<String> sdkList = new HashSet<>();
                if (!CollectionUtils.isEmpty(result.getStaticSdks().getSdk())) {
                    sdkList.addAll(result.getStaticSdks().getSdk());
                }
                if (!CollectionUtils.isEmpty(result.getStaticSdks().getPlugin())) {
                    sdkList.addAll(result.getStaticSdks().getPlugin());
                }
                extraInfoVO.setSdkList(new ArrayList<>(sdkList));
            }
        }
        return extraInfoVO;
    }

    private static String optString(JSONObject obj, String key, String defaultValue) {
        String value = obj.getString(key);
        return StringUtils.isBlank(value) ? defaultValue : value;
    }

}
