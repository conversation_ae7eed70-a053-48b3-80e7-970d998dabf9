package cn.ijiami.detection.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.StringJoiner;
import java.util.TreeSet;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import cn.ijiami.detection.VO.compliance.OutsideAddressDetailsVO;
import cn.ijiami.detection.mapper.*;
import cn.ijiami.detection.utils.*;
import cn.ijiami.detection.utils.icp.IcpUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.ijiami.detection.VO.CountOutsideTypeVO;
import cn.ijiami.detection.VO.IpAddressCheckResultVO;
import cn.ijiami.detection.VO.PrivacyDetectionResultVO;
import cn.ijiami.detection.VO.TPrivacyOutsideAddressVO;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.VO.detection.statistical.ChildItemCommonCountVO;
import cn.ijiami.detection.VO.networkcheck.DialDetectParams;
import cn.ijiami.detection.VO.networkcheck.DialDetectResult;
import cn.ijiami.detection.VO.networkcheck.NetworkCheckResponse;
import cn.ijiami.detection.analyzer.bo.ActionStackBO;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.entity.TIpAddressCheck;
import cn.ijiami.detection.entity.TIpAddressCheckRecord;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.IpAddressCheckStatusEnum;
import cn.ijiami.detection.enums.OutsideEnum;
import cn.ijiami.detection.server.client.base.enums.TerminalTypeEnum;
import cn.ijiami.detection.exception.IpAddressCheckFailureException;
import cn.ijiami.detection.helper.InsertListHelper;
import cn.ijiami.detection.query.BehaviorQuery;
import cn.ijiami.detection.query.IpAddressCheckQuery;
import cn.ijiami.detection.service.api.CacheService;
import cn.ijiami.detection.service.api.IPrivacyDetectionTransferRiskService;
import cn.ijiami.detection.service.api.IPrivacyOutsideAddressService;
import cn.ijiami.detection.service.api.ISendMessageService;
import cn.ijiami.detection.service.spi.NetworkCheckFeign;
import cn.ijiami.framework.kit.utils.UuidUtil;
import cn.ijiami.manager.user.entity.User;
import cn.ijiami.manager.user.service.api.IUserService;

/**
 * <AUTHOR>
 * @date 2019/12/3 15:19
 */
@Service
public class PrivacyOutsideAddressServiceImpl extends BaseDetectionMongoDBDAOImpl<TaskDetailVO> implements IPrivacyOutsideAddressService {

    private static final Logger logger = LoggerFactory.getLogger(PrivacyOutsideAddressServiceImpl.class);

    private final TPrivacyOutsideAddressMapper privacyOutsideAddressMapper;
    private final IPrivacyDetectionTransferRiskService transferRiskService;
    private final TTaskMapper tTaskMapper;
    private final TAssetsMapper tAssetsMapper;
    private final TIpAddressCheckMapper tIpAddressCheckMapper;
    private final TIpAddressCheckRecordMapper tIpAddressCheckRecordMapper;
    private final NetworkCheckFeign networkCheckFeign;
    private final ISendMessageService iSendMessageService;
    private final IUserService userService;
    private final CacheService cacheService;
    private final TAssetsMapper assetsMapper;
    private final TPrivacySensitiveWordMapper sensitiveWordMapper;

    @Value("${ijiami.networkCheck.callback:}")
    private String callbackUrl;

    public PrivacyOutsideAddressServiceImpl(TPrivacyOutsideAddressMapper privacyOutsideAddressMapper,
                                            IPrivacyDetectionTransferRiskService transferRiskService, TTaskMapper tTaskMapper,
                                            TAssetsMapper tAssetsMapper, TIpAddressCheckMapper tIpAddressCheckMapper,
                                            TIpAddressCheckRecordMapper tIpAddressCheckRecordMapper, NetworkCheckFeign networkCheckFeign,
                                            ISendMessageService iSendMessageService, IUserService userService, CacheService cacheService,
                                            TAssetsMapper assetsMapper,TPrivacySensitiveWordMapper sensitiveWordMapper) {
        this.privacyOutsideAddressMapper = privacyOutsideAddressMapper;
        this.transferRiskService = transferRiskService;
        this.tTaskMapper = tTaskMapper;
        this.tAssetsMapper = tAssetsMapper;
        this.tIpAddressCheckMapper = tIpAddressCheckMapper;
        this.tIpAddressCheckRecordMapper = tIpAddressCheckRecordMapper;
        this.networkCheckFeign = networkCheckFeign;
        this.iSendMessageService = iSendMessageService;
        this.userService = userService;
        this.cacheService = cacheService;
        this.assetsMapper = assetsMapper;
        this.sensitiveWordMapper = sensitiveWordMapper;
    }

    @Override
    public List<TPrivacyOutsideAddress> getOutSideAddress(Long taskId, Integer behaviorStage) {
        return privacyOutsideAddressMapper.findByTaskId(taskId, behaviorStage);
    }

    @Override
    public List<TPrivacyOutsideAddress> findByTaskIdAndOutside(Long taskId, Integer outside, Integer behaviorStage) {
        TTask tTask = tTaskMapper.selectByPrimaryKey(taskId);
        if (outside == null) {
            outside = 0;
        }
        if (tTask.getTerminalType() == TerminalTypeEnum.IOS) {
            return privacyOutsideAddressMapper.findByTaskIdAndOutsideIos(taskId, outside, behaviorStage);
        } else {
            List<TPrivacyOutsideAddress> result = privacyOutsideAddressMapper.findByTaskIdAndOutside(taskId, outside, behaviorStage);
            // 兼容堆栈数据处理方式
            if (CollectionUtils.isNotEmpty(result) && DataHandleUtil.isJSONValid(result.get(0).getStackInfo())) {
                result.stream().forEach(item -> {
                    item.setCounter(0);
                    if (StringUtils.isNotBlank(item.getStackInfo())) {
                        try {
                            item.setCounter(JSON.parseArray(item.getStackInfo()).size());
                            if (item.getExecutorType() == 1) {
                                item.setAppCount(item.getCounter());
                            }
                            if (item.getExecutorType() == 2) {
                                item.setSdkCount(item.getCounter());
                            }
                        } catch (Exception e) {
                        }
                    }
                    item.setCounter(item.getAppCount() + item.getSdkCount());
                });
            }
            return result;
        }
    }

    @Override
    public List<CountOutsideTypeVO> countByOutside(Long taskId, Integer behaviorStage) {
        return privacyOutsideAddressMapper.countByOutside(taskId, behaviorStage);
    }

    @Override
    public void updateOutSideWhenStaticComplete(Long taskId, String documentId) {
        // 查询应用信息
        TAssets asset = assetsMapper.selectAssetByTaskId(taskId);
        if (asset == null) {
            logger.error("安卓静态检测境内外IP更新失败，找不到应用信息，任务ID：{}", taskId);
            return;
        }
        List<TPrivacyOutsideAddress> outsideAddresses = privacyOutsideAddressMapper.findByTaskId(taskId, 0);
        if (outsideAddresses == null) {
            outsideAddresses = new ArrayList<>();
        }
        // 获取mongo中存的静态检测项检测结果
        PrivacyDetectionResultVO resultFor0806 = transferRiskService.getResultFor(documentId, "0806", 1);
        if (resultFor0806 != null && resultFor0806.getDetails() != null) {
            for (ChildItemCommonCountVO detail : resultFor0806.getDetails()) {
                TPrivacyOutsideAddress outSideAddress = new TPrivacyOutsideAddress();
                outSideAddress.setTaskId(taskId);
                outSideAddress.setIp(detail.getColunm1());
                outSideAddress.setAddress(detail.getColunm2());
                outSideAddress.setCounter(0);
                outSideAddress.setBehaviorStage(BehaviorStageEnum.BEHAVIOR_FRONT);
                if (!StringUtils.isBlank(detail.getColunm2()) && !detail.getColunm2().startsWith("中国")) {
                    outSideAddress.setOutside(1);
                } else {
                    outSideAddress.setOutside(0);
                }
                outSideAddress.setExecutorType(1);
                outSideAddress.setExecutor(asset.getName());
                outsideAddresses.add(outSideAddress);
            }
        }
        // 去重处理
        updateOutSide(taskId, outsideAddresses, false);
        logger.debug("静态检测 - 境内外ip数据更新，任务ID：{}，更新内容：{}", taskId, outsideAddresses);
    }

    @Override
    public void updateOutSide(Long taskId, List<TPrivacyOutsideAddress> outsideAddresses, boolean clean) {
        // 重置数据
        if (clean) {
            privacyOutsideAddressMapper.deleteByTaskId(taskId);
        }
        outsideAddresses.forEach(i -> i.setId(null));
        outsideAddresses = outsideAddresses.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>((o1, o2) -> {
            StringBuilder builder1 = new StringBuilder();
            builder1.append(o1.getIp());
            if (!org.apache.commons.lang3.StringUtils.isBlank(builder1)) {
                builder1.append(o1.getHost());
            }
            StringBuilder builder2 = new StringBuilder();
            builder2.append(o2.getIp());
            if (!org.apache.commons.lang3.StringUtils.isBlank(builder2)) {
                builder2.append(o2.getHost());
            }
            return builder1.toString().compareTo(builder2.toString());
        })), ArrayList::new));
        // 批量插入数据
        if (outsideAddresses.size() > 0) {
            privacyOutsideAddressMapper.insertList(outsideAddresses);
        }
    }


    @Override
    public List<TPrivacyOutsideAddress> findByTaskIdAndOutsideStackInfo(Long taskId, String ip, String host, Integer behaviorStage) {
        TTask task = tTaskMapper.selectByPrimaryKey(taskId);
        List<TPrivacyOutsideAddress> result = privacyOutsideAddressMapper.findByTaskIdAndOutsideStackInfo(taskId, ip, host, behaviorStage);
        // 兼容堆栈数据处理方式
        try {
            if (task.getTerminalType() == TerminalTypeEnum.ANDROID && result.size() > 0 && DataHandleUtil.isJSONValid(result.get(0).getStackInfo())) {
                TPrivacyOutsideAddress root = result.get(0);
                List<TPrivacyOutsideAddress> finalResult = new ArrayList<>();
                for (ActionStackBO stackBO : JSON.parseArray(root.getStackInfo(), ActionStackBO.class)) {
                    TPrivacyOutsideAddress outsideAddress = new TPrivacyOutsideAddress();
                    BeanUtil.copyPropertiesIgnoreNull(root, outsideAddress);
                    outsideAddress.setStackInfo(stackBO.getStackInfo());
                    outsideAddress.setDetailsData(stackBO.getDetailsData());
                    outsideAddress.setStrTime(stackBO.getActionTime());
                    finalResult.add(outsideAddress);
                }
                return finalResult;
            } else {
                return result;
            }
        } catch (Exception e) {
        }
        return result;
    }
    
    
    /**
     * 通信行为分析通用业务，包含各个筛选条件
     * @param behaviorQuery
     * @return
     */
    @Override
    public TPrivacyOutsideAddressVO findOutsideDataByTaskId(BehaviorQuery behaviorQuery) {
        //返回的数据带分页信息
        TPrivacyOutsideAddressVO tPrivacyOutsideAddressVO = new TPrivacyOutsideAddressVO();
        List<TPrivacyOutsideAddress> result=null;
        PageInfo<TPrivacyOutsideAddress> pageInfo = null;

        if(behaviorQuery !=null && (!"".equals(behaviorQuery.getTaskId()) && behaviorQuery.getTaskId()!=null)
                && (behaviorQuery.getTaskId()!=null && !"".equals(behaviorQuery.getTaskId()))){

            Long taskId = behaviorQuery.getTaskId();
            String executors = behaviorQuery.getExecutors();
            String executorTypeString = behaviorQuery.getExecutorType();
            String outsideString = behaviorQuery.getOutside();
            String protocol = behaviorQuery.getProtocol();
            Integer behaviorStage = behaviorQuery.getBehaviorStage();
            Integer sortType = behaviorQuery.getSortType();
            Integer sortOrder = behaviorQuery.getSortOrder();

            TTask task = tTaskMapper.selectByPrimaryKey(taskId);
            if (behaviorQuery.getPage() != null && behaviorQuery.getRows() != null) {
                PageHelper.startPage(behaviorQuery.getPage(), behaviorQuery.getRows());
            }
            result = privacyOutsideAddressMapper.findOutsideDataByTaskId(taskId,
                    behaviorStage, executors, executorTypeString, outsideString, protocol, sortType, sortOrder,
                    behaviorQuery.getCookieMark(), task.getTerminalType().getValue());
            // 移除乱码
            result.forEach(data -> data.setDetailsData(data.getDetailsData().equals("") ? PinfoConstant.DETAILS_EMPTY : data.getDetailsData()));

            // 兼容堆栈数据处理方式
            if (task.getTerminalType() == TerminalTypeEnum.ANDROID && result.size() > 0) {
                for (TPrivacyOutsideAddress root : result) {
                    if (root.getStackInfo() != null && !PinfoConstant.DETAILS_EMPTY.equals(root.getStackInfo()) && DataHandleUtil.isJSONValid(root.getStackInfo())) {
                        ActionStackBO stackBO = JSON.parseArray(root.getStackInfo(), ActionStackBO.class).get(0);
                        root.setStackInfo(StringUtils.isEmpty(root.getStackInfo()) ? stackBO.getStackInfo() : root.getStackInfo());
                        root.setDetailsData(StringUtils.isEmpty(root.getDetailsData()) ? stackBO.getDetailsData() : root.getDetailsData());
                        root.setStrTime(root.getStrTime()==null?stackBO.getActionTime():root.getStrTime());
                        root.setActionTime(root.getActionTime()==null?stackBO.getActionTime():root.getActionTime());
                    }
                }
            }
        }

        if(result!=null){
            pageInfo = new PageInfo<>(result);
            pageInfo.setList(result);
            tPrivacyOutsideAddressVO.setPageInfo(pageInfo);
        }

        return tPrivacyOutsideAddressVO;
    }

    /**
     * 获取通信行为的筛选条件（主体类型，协议类型）
     * @param documentId
     * @param behaviorStage
     * @return
     */
    @Override
    public Map<String,List<String>> getBehaviorNoun(String documentId, Integer behaviorStage) {
        List<String> executorList = new ArrayList<>();
        List<String> protocolList = new ArrayList<>();
        Map<String,List<String>> map = new HashMap<>();
        TTask task = tTaskMapper.findByDocumentId(documentId);
        if(task !=null && task.getTaskId()!=null && task.getTaskId()!=0L){
            List<String> list = privacyOutsideAddressMapper.getBehaviorNoun(task.getTaskId(), behaviorStage);
            protocolList = privacyOutsideAddressMapper.getBehaviorNounOfProtocol(task.getTaskId(),behaviorStage);
            TAssets tAssets = tAssetsMapper.selectAssetByTaskId(task.getTaskId());
            for (String lineInfo : list) {
                for (String sdkInfo : lineInfo.split(",")) {
                    if (executorList.contains(sdkInfo)) {
                        continue;
                    }
                    executorList.add(sdkInfo.replace("\n",""));
                }
            }
            //过滤掉与app名称相同的sdk主体
            executorList=executorList.stream().filter(executor -> !executor.equals(tAssets.getName())).collect(Collectors.toList());
        }
        map.put("executor",executorList);
        map.put("protocol",protocolList);
        return map;
    }

    @Override
    public void ipAddressCheck(IpAddressCheckQuery query, Long userId) {
        TPrivacyOutsideAddress address = privacyOutsideAddressMapper.selectByPrimaryKey(query.getOutsideId());
        if (Objects.isNull(address)) {
            throw new IpAddressCheckFailureException("通讯传输行为id错误");
        }
        TIpAddressCheckRecord queryRecord = new TIpAddressCheckRecord();
        queryRecord.setIp(address.getIp());
        queryRecord.setStatus(IpAddressCheckStatusEnum.SUCCESS.getValue());
        TIpAddressCheckRecord record = tIpAddressCheckRecordMapper.selectOne(queryRecord);
        if (Objects.nonNull(record)) {
            TIpAddressCheck queryCheckData = new TIpAddressCheck();
            queryCheckData.setRecordId(record.getId());
            List<TIpAddressCheck> checkList = tIpAddressCheckMapper.select(queryCheckData);
            // 这里的用户Id不能使用record里的用户id
            // 因为record里记录的是执行检测的用户id，有可能是A用户对这个ip进行了拨测，B用户也请求这个接口刚好有数据就不用再拨测了。
            sendIpAddressCheckSuccessData(record, address, checkList, userId);
        } else {
            DialDetectParams params = new DialDetectParams();
            params.setNetAddress(address.getIp());
            params.setBusinessKey(UuidUtil.uuid());
            params.setNetDialDetectId(String.valueOf(address.getId()));
            params.setCallbackUrl(callbackUrl);
            TransactionSynchronizationUtils.afterCommit(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    NetworkCheckResponse<String> response = networkCheckFeign.dialDetect(params);
                    if (response.getSuccess()) {
                        TIpAddressCheckRecord saveRecord = new TIpAddressCheckRecord();
                        saveRecord.setUserId(userId);
                        saveRecord.setOutsideId(address.getId());
                        saveRecord.setIp(address.getIp());
                        saveRecord.setHttpCode(response.getCode());
                        saveRecord.setTaskId(address.getTaskId());
                        saveRecord.setRequestId(params.getBusinessKey());
                        saveRecord.setStatus(IpAddressCheckStatusEnum.PROCESSING.getValue());
                        saveRecord.setHttpCode(response.getCode());
                        saveRecord.setHttpResult(response.getMessage());
                        saveRecord.setResultJson(JSON.toJSONString(response));
                        saveRecord.setRequestParam(JSON.toJSONString(params));
                        saveRecord.setShellCount(0);
                        saveRecord.setUpdateTime(new Date());
                        saveRecord.setCreateTime(new Date());
                        tIpAddressCheckRecordMapper.insert(saveRecord);
                        cacheService.setNotificationId(PinfoConstant.CACHE_IP_ADDRESS_CHECK_TOPIC_ID + saveRecord.getRequestId(), query.getNotificationId());
                    } else {
                        logger.error("ip拨测服务调用失败 message={} code={}", response.getMessage(), response.getCode());
                        throw new IpAddressCheckFailureException("ip拨测服务调用失败");
                    }
                }
            });
        }
    }

    @Transactional
    @Override
    public void saveIpAddressCheckData(DialDetectResult result) {
        if (Objects.isNull(result)) {
            logger.info("ip拨测结果为空");
            return;
        }
        TIpAddressCheckRecord queryRecord = new TIpAddressCheckRecord();
        queryRecord.setRequestId(result.getBusinessKey());
        queryRecord.setStatus(IpAddressCheckStatusEnum.PROCESSING.getValue());
        TIpAddressCheckRecord record = tIpAddressCheckRecordMapper.selectOne(queryRecord);
        if (Objects.isNull(record)) {
            logger.info("ip拨测记录不存在 businessKey={}", result.getBusinessKey());
            return;
        }
        if (result.getStatus() == IpAddressCheckStatusEnum.SUCCESS.getValue()) {
            TPrivacyOutsideAddress outsideAddress = privacyOutsideAddressMapper.selectByPrimaryKey(record.getOutsideId());
            if (Objects.isNull(outsideAddress)) {
                logger.info("通讯传输记录不存在 outsideId={}", record.getOutsideId());
                return;
            }
            List<TIpAddressCheck> checkList = Optional.ofNullable(result.getRouteList()).orElse(Collections.emptyList()).stream()
                    .map(route-> {
                TIpAddressCheck check = new TIpAddressCheck();
                check.setRecordId(record.getId());
                check.setIp(route.getIp());
                check.setHostName(route.getHostName());
                check.setSeq(route.getSeq());
                check.setCountry(route.getCountry());
                check.setRegion(route.getRegion());
                check.setCity(route.getCity());
                check.setArea(route.getArea());
                return check;
            }).collect(Collectors.toList());
            InsertListHelper.insertList(checkList, tIpAddressCheckMapper::insertList);
            TIpAddressCheckRecord updateRecord = new TIpAddressCheckRecord();
            updateRecord.setId(record.getId());
            updateRecord.setStatus(IpAddressCheckStatusEnum.SUCCESS.getValue());
            tIpAddressCheckRecordMapper.updateByPrimaryKeySelective(updateRecord);

            sendIpAddressCheckSuccessData(record, outsideAddress, checkList, record.getUserId());
            logger.info("ip拨测成功");
        } else if(result.getStatus() == IpAddressCheckStatusEnum.FAILURE.getValue()) {
            ipAddressCheckFailure(record);
            logger.info("ip拨测失败");
        }
    }

    @Override
    public DialDetectResult queryIpAddressCheckDataByRecord(TIpAddressCheckRecord record) {
        NetworkCheckResponse<DialDetectResult> response = networkCheckFeign.findByBusinessKey(record.getRequestId());
        return response.getResult();
    }

    @Override
    public void ipAddressCheckFailure(TIpAddressCheckRecord record) {
        TIpAddressCheckRecord updateRecord = new TIpAddressCheckRecord();
        updateRecord.setId(record.getId());
        updateRecord.setStatus(IpAddressCheckStatusEnum.FAILURE.getValue());
        tIpAddressCheckRecordMapper.updateByPrimaryKeySelective(updateRecord);

        sendIpAddressCheckFailureData(record, record.getUserId());
    }

    private void sendIpAddressCheckSuccessData(TIpAddressCheckRecord record, TPrivacyOutsideAddress outsideAddress, List<TIpAddressCheck> checkList, Long toUserId) {
        List<IpAddressCheckResultVO.IpAddressCheckRoute> resultRouteList = checkList.stream()
                .sorted(Comparator.comparingInt(TIpAddressCheck::getSeq))
                .map(route-> {
            IpAddressCheckResultVO.IpAddressCheckRoute check = new IpAddressCheckResultVO.IpAddressCheckRoute();
            check.setIp(route.getIp());
            check.setAddress(buildAddress(route));
            check.setOutside(OutsideEnum.getItem(IpUtil.isOutSide(check.getAddress())));
            return check;
        }).collect(Collectors.toList());
        IpAddressCheckResultVO resultVO = new IpAddressCheckResultVO();
        resultVO.setIp(outsideAddress.getIp());
        resultVO.setHost(outsideAddress.getHost());
        resultVO.setPort(outsideAddress.getPort());
        resultVO.setStatus(IpAddressCheckStatusEnum.SUCCESS.getValue());
        resultVO.setRouteList(resultRouteList);
        User user = userService.findUserById(toUserId);
        String notificationId = cacheService.getNotificationId(PinfoConstant.CACHE_IP_ADDRESS_CHECK_TOPIC_ID + record.getRequestId());
        if (Objects.nonNull(user)) {
            iSendMessageService.sendIpAddressCheckMessage(CommonUtil.beanToJson(resultVO), user, notificationId);
        } else {
            logger.info("用户不存在 userId={}", record.getUserId());
        }
    }

    private void sendIpAddressCheckFailureData(TIpAddressCheckRecord record, Long toUserId) {
        IpAddressCheckResultVO resultVO = new IpAddressCheckResultVO();
        resultVO.setOutsideId(record.getOutsideId());
        resultVO.setStatus(IpAddressCheckStatusEnum.FAILURE.getValue());
        User user = userService.findUserById(toUserId);
        String notificationId = cacheService.getNotificationId(PinfoConstant.CACHE_IP_ADDRESS_CHECK_TOPIC_ID + record.getRequestId());
        if (Objects.nonNull(user)) {
            iSendMessageService.sendIpAddressCheckMessage(JSON.toJSONString(resultVO), user, notificationId);
        } else {
            logger.info("用户不存在 userId={}", record.getUserId());
        }
    }

    private String buildAddress(TIpAddressCheck route) {
        StringJoiner joiner = new StringJoiner(" ");
        if (StringUtils.isNotBlank(route.getCountry())) {
            joiner.add(route.getCountry());
        }
        if (StringUtils.isNotBlank(route.getRegion())) {
            joiner.add(route.getRegion());
        }
        if (StringUtils.isNotBlank(route.getCity())) {
            joiner.add(route.getCity());
        }
        if (StringUtils.isNotBlank(route.getArea())) {
            joiner.add(route.getArea());
        }
        return joiner.toString();
    }

    /**
     * 查询传输信息数据详情与域名备案信息
     * @param id
     * @return
     */
    @Override
    public OutsideAddressDetailsVO queryAnalysisNetwork(Long id) {
        OutsideAddressDetailsVO outsideAddressDetailsVo = new OutsideAddressDetailsVO();
        TPrivacyOutsideAddress outsideAddress = privacyOutsideAddressMapper.selectByPrimaryKey(id);
        if(outsideAddress ==null){
            return null;
        }
        BeanUtils.copyProperties(outsideAddress,outsideAddressDetailsVo);
        //敏感词标红
        List<String> list = sensitiveWordMapper.queryKeywordByTaskId(outsideAddress.getTaskId());
        for (String word : list) {
            outsideAddressDetailsVo.setDetailsData(WordStringUtil.replaceCodeToRedCode(outsideAddressDetailsVo.getDetailsData(),word));
        }
        //生成备案信息
        Map<String, String> icpDataMaps = new HashMap<>();
        Map<String, String> whoIsMaps = new HashMap<>();
        long startTime = System.currentTimeMillis();
        if(StringUtils.isNotEmpty(outsideAddress.getHost())){
            icpDataMaps = IcpUtils.query_ICPResult(outsideAddress.getHost());
            whoIsMaps = IcpUtils.query_WhoIsInfo(outsideAddress.getHost());
        }
        if(icpDataMaps !=null){
            outsideAddressDetailsVo.setSiteName(icpDataMaps.getOrDefault("名称",""));
            outsideAddressDetailsVo.setCompanyName(icpDataMaps.getOrDefault("主办单位名称",""));
            outsideAddressDetailsVo.setUnitProperties(icpDataMaps.getOrDefault("主办单位性质",""));
            outsideAddressDetailsVo.setDirector("");
            outsideAddressDetailsVo.setAuditTime(icpDataMaps.getOrDefault("审核时间",""));
            outsideAddressDetailsVo.setFilingLicenseNo(icpDataMaps.getOrDefault("备案/许可证号",""));
        }

        if(whoIsMaps !=null){
            outsideAddressDetailsVo.setPrimaryDomain(whoIsMaps.getOrDefault("域名服务器",""));
            outsideAddressDetailsVo.setCreateTime(whoIsMaps.getOrDefault("创建时间",""));
            outsideAddressDetailsVo.setExpiryTime(whoIsMaps.getOrDefault("过期时间",""));
            outsideAddressDetailsVo.setRegistrars(whoIsMaps.getOrDefault("注册商",""));
        }

        //结束时间
        long milliseconds = System.currentTimeMillis() - startTime;
        logger.info("域名查询耗时"+ TimeUnit.MILLISECONDS.toSeconds(milliseconds) +"秒");
        return outsideAddressDetailsVo;
    }
}
