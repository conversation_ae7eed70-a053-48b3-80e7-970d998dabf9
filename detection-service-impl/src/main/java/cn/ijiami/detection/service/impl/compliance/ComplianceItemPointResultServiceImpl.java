package cn.ijiami.detection.service.impl.compliance;


import cn.ijiami.detection.VO.compliance.ComplianceConfirmVO;
import cn.ijiami.detection.VO.compliance.ComplianceResultVO;
import cn.ijiami.detection.android.client.api.AndroidComplianceInstanceServiceApi;
import cn.ijiami.detection.server.client.base.dto.compliance.ComplianceResultDTO;
import cn.ijiami.detection.server.client.base.param.ComplianceBaseParam;
import cn.ijiami.detection.service.api.compliance.IComplianceItemPointResultService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.util.*;

@Service
@Slf4j
public class ComplianceItemPointResultServiceImpl implements IComplianceItemPointResultService {
    @Autowired
    private AndroidComplianceInstanceServiceApi androidComplianceInstanceServiceApi;


    @Override
    public List<ComplianceResultVO> batchSaveComplianceItemPointResult(ComplianceConfirmVO complianceConfirmVo) {
        log.info("通过远程调用保存或修改评估点结果,请求参数{}", JSON.toJSONString(complianceConfirmVo));
        ComplianceBaseParam complianceBaseParam = new ComplianceBaseParam();
        BeanUtils.copyProperties(complianceConfirmVo, complianceBaseParam);
        return converToComplianceItemPointResultVOList(androidComplianceInstanceServiceApi.batchSaveComplianceItemPointResult(complianceBaseParam));
    }

    /**
     * 转成VO输出
     */
    private List<ComplianceResultVO> converToComplianceItemPointResultVOList(List<ComplianceResultDTO> complianceResultDTOList) {
        if (CollectionUtils.isEmpty(complianceResultDTOList)) {
            return Collections.emptyList();
        }
        List<ComplianceResultVO> complianceItemPointResultVOList = new ArrayList<>();
        complianceResultDTOList.forEach(resultDTO ->{
            ComplianceResultVO complianceItemPointResultVO = new ComplianceResultVO();
            BeanUtils.copyProperties(resultDTO, complianceItemPointResultVO);
            complianceItemPointResultVOList.add(complianceItemPointResultVO);
        });
        return complianceItemPointResultVOList;
    }
}
