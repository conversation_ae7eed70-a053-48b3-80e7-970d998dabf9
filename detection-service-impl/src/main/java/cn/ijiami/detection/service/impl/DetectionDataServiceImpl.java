package cn.ijiami.detection.service.impl;

import static cn.ijiami.detection.utils.SuspiciousSdkUtils.packageNamePrefix;
import static cn.ijiami.detection.utils.SuspiciousSdkUtils.setSuspiciousSdkExecutorAndPackageName;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.ijiami.base.common.user.IUser;
import cn.ijiami.detection.VO.RealTimeBehaviorLog;
import cn.ijiami.detection.VO.RealTimeLog;
import cn.ijiami.detection.VO.RealTimeNetLog;
import cn.ijiami.detection.VO.RealtimeSdkItem;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.VO.detection.privacy.IOSRealTimeLog;
import cn.ijiami.detection.VO.detection.privacy.dto.AppletActionBO;
import cn.ijiami.detection.analyzer.AppletCaptureAction;
import cn.ijiami.detection.analyzer.CaptureHostIpAction;
import cn.ijiami.detection.analyzer.CaptureInfoAction;
import cn.ijiami.detection.analyzer.bo.SuspiciousSdkBO;
import cn.ijiami.detection.bean.DynamicTaskContext;
import cn.ijiami.detection.config.IjiamiCommonProperties;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.entity.TPermission;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacyActionNougatExtend;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import cn.ijiami.detection.entity.TSuspiciousSdk;
import cn.ijiami.detection.entity.TSuspiciousSdkLibrary;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.entity.interfaces.ActionExecutor;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.server.client.base.enums.TerminalTypeEnum;
import cn.ijiami.detection.helper.AndroidActionLogConvertHelper;
import cn.ijiami.detection.helper.InsertListHelper;
import cn.ijiami.detection.helper.IosActionLogConvertHelper;
import cn.ijiami.detection.mapper.TAssetsMapper;
import cn.ijiami.detection.mapper.TPrivacyActionNougatExtendMapper;
import cn.ijiami.detection.mapper.TPrivacyActionNougatMapper;
import cn.ijiami.detection.mapper.TPrivacyOutsideAddressMapper;
import cn.ijiami.detection.mapper.TPrivacySensitiveWordMapper;
import cn.ijiami.detection.mapper.TPrivacySharedPrefsMapper;
import cn.ijiami.detection.mapper.TSuspiciousSdkLibraryMapper;
import cn.ijiami.detection.mapper.TSuspiciousSdkMapper;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.result.AppDetailsResult;
import cn.ijiami.detection.service.DetectionDataService;
import cn.ijiami.detection.service.api.CacheService;
import cn.ijiami.detection.service.api.IAssetsService;
import cn.ijiami.detection.service.api.ITaskService;
import cn.ijiami.detection.service.api.SDKWhitelistRuleService;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.SuspiciousSdkUtils;
import cn.ijiami.detection.utils.WriteExcel;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * @作者 zhengxingtian
 * @版本 1.0.0
 * @类名 DetectionDataServiceImpl.java
 * @描述 检测数据服务实现类
 * @创建时间 2024年05月29日 18:25:00
 */
@Slf4j
@Service
public class DetectionDataServiceImpl implements DetectionDataService {

    @Autowired
    private IjiamiCommonProperties commonProperties;

    @Autowired
    private TTaskMapper taskMapper;

    @Autowired
    private ITaskService taskService;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private TAssetsMapper assetsMapper;

    @Autowired
    private IAssetsService assetsService;

    @Autowired
    private TPrivacyOutsideAddressMapper privacyOutsideAddressMapper;

    @Autowired
    private TPrivacyActionNougatMapper privacyActionNougatMapper;

    @Autowired
    private TPrivacySensitiveWordMapper privacySensitiveWordMapper;

    @Autowired
    private TPrivacySharedPrefsMapper privacySharedPrefsMapper;

    @Autowired
    private TPrivacyActionNougatExtendMapper privacyActionNougatExtendMapper;

    @Autowired
    private IosActionLogConvertHelper iosActionLogConvertHelper;

    @Autowired
    private AndroidActionLogConvertHelper androidActionLogConvertHelper;

    @Autowired
    private CaptureInfoAction androidCaptureInfoAction;

    @Autowired
    private CaptureHostIpAction androidCaptureHostIpAction;

    @Autowired
    private AppletCaptureAction appletCaptureAction;

    @Value("${fastDFS.intranet.ip:}")
    private String fastIntranetDFSIp;

    @Value("${ijiami.framework.rootPath}")
    private String iosFrameworkRootPath;

    @Autowired
    private IjiamiCommonProperties ijiamiCommonProperties;

    @Autowired
    private SDKWhitelistRuleService sdkWhitelistRuleService;

    @Autowired
    private TSuspiciousSdkLibraryMapper suspiciousSdkLibraryMapper;

    @Autowired
    private TSuspiciousSdkMapper suspiciousSdkMapper;

    /**
     * 导出实时SDK行为数据
     * @param user 用户信息
     * @param taskId 任务ID
     * @return 导出的文件
     */
    @Override
    public File exportRealtimeSdk(IUser user, Long taskId) {
        TTask task = validateTask(taskId);
        TaskDetailVO detailVO = taskService.getTaskDetail(task.getApkDetectionDetailId());
        Map<String, List<RealtimeSdkItem>> sdkMap = collectRealtimeSdkItems(taskId, detailVO);
        List<RealtimeSdkItem> data = sdkMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        File file = createEmptyExcelFile(taskId + "_SDK行为调用");
        try {
            if (file.createNewFile()) {
                WriteExcel.writeExcelDeepSdkAction(data, file.getAbsolutePath());
            } else {
                log.error("文件创建失败 {}", file.getAbsolutePath());
                throw new IjiamiRuntimeException("文件创建失败");
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return file;
    }

    /**
     * 校验任务是否存在
     * @param taskId 任务ID
     */
    private TTask validateTask(Long taskId) {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (task == null) {
            throw new IjiamiRuntimeException("任务不存在");
        }
        return task;
    }

    /**
     * 收集实时SDK项
     * @param taskId 任务ID
     * @param detailVO 任务详情
     * @return SDK项映射
     */
    private Map<String, List<RealtimeSdkItem>> collectRealtimeSdkItems(Long taskId, TaskDetailVO detailVO) {
        List<? extends RealTimeLog> logList = getRealTimeLogs(taskId);
        Map<String, List<RealtimeSdkItem>> sdkMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(logList)) {
            logList.stream()
                    .filter(log -> Objects.equals(log.getExecutorType(), ExecutorTypeEnum.SDK.getValue()))
                    .forEach(log -> {
                        String[] executors = log.getExecutor().split(",");
                        for (String executor : executors) {
                            List<RealtimeSdkItem> sdkItems = sdkMap.computeIfAbsent(executor, k -> new ArrayList<>());
                            Optional<RealtimeSdkItem> sdkItem = sdkItems.stream().filter(sdk -> sdk.getActionName().equals(log.getActionName())).findFirst();
                            if (sdkItem.isPresent()) {
                                sdkItem.get().setCount(sdkItem.get().getCount() + 1);
                            } else {
                                RealtimeSdkItem item = new RealtimeSdkItem();
                                item.setExecutor(executor);
                                item.setActionName(log.getActionName());
                                item.setAppName(detailVO.getApk_name());
                                item.setCount(1);
                                sdkItems.add(item);
                            }
                        }
                    });
        }
        return sdkMap;
    }

    /**
     * 获取实时日志
     * @param taskId 任务ID
     * @return 实时日志列表
     */
    private List<? extends RealTimeLog> getRealTimeLogs(Long taskId) {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (task.getTerminalType() == TerminalTypeEnum.IOS) {
            return getAllIosActionList(taskId);
        } else {
            return getAllActionList(taskId);
        }
    }


    /**
     * 创建一个空的Excel文件
     * @param fileName 文件名
     * @return File对象
     */
    private File createEmptyExcelFile(String fileName) {
        String reportRootPath = commonProperties.getProperty("ijiami.report.root.path");
        String suffix = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        return new File(reportRootPath + "out" + File.separator + fileName + suffix);
    }

    /**
     * 导出实时行为数据
     * @param user 用户信息
     * @param taskId 任务ID
     * @return 导出的文件
     */
    @Override
    public File exportRealtimeBehavior(IUser user, Long taskId) {
        TTask task = validateTask(taskId);
        TaskDetailVO detailVO = taskService.getTaskDetail(task.getApkDetectionDetailId());
        if (detailVO == null) {
            throw new IjiamiRuntimeException("任务不存在");
        }
        List<? extends RealTimeLog> logList = getRealTimeLogs(taskId);
        File file = createEmptyExcelFile(taskId + "_本地行为堆栈信息");
        try {
            if (file.createNewFile()) {
                WriteExcel.writeExcelDeepDetectionAction(logList, task.getDynamicStarttime(), detailVO.getApk_name(), file.getAbsolutePath());
            } else {
                log.error("文件创建失败 {}", file.getAbsolutePath());
                throw new IjiamiRuntimeException("文件创建失败");
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return file;
    }

    /**
     * 删除行为日志
     * @param taskId 任务ID
     * @param actionLogIds 行为日志ID列表
     */
    @Override
    public void deleteActionLogs(Long taskId, List<String> actionLogIds) {
        validateTask(taskId);
        deleteActionList(taskId, actionLogIds);
    }

    /**
     * 获取应用详细信息
     * @param taskId 任务ID
     * @return 应用详细信息结果
     * @throws IjiamiApplicationException 异常信息
     */
    @Override
    public AppDetailsResult getAppDetails(Long taskId) throws IjiamiApplicationException {
        TTask task = validateTask(taskId);
        TAssets assets = assetsMapper.selectByPrimaryKey(task.getAssetsId());
        if (assets == null) {
            throw new IjiamiRuntimeException("资产不存在");
        }
        return assetsService.retrieveAppBasicInfo(taskMapper.selectByPrimaryKey(taskId), assets);
    }


    @Override
    public void insertDynamicAction(TPrivacyActionNougat nougat, DynamicTaskContext taskContext) {
        privacyActionNougatMapper.insert(nougat);
        // 解析疑似SDK
        analysisSuspiciousSdk(nougat, taskContext);
        if (StringUtils.isNotBlank(nougat.getJniStackInfo()) || StringUtils.isNotBlank(nougat.getApiName())) {
            privacyActionNougatExtendMapper.insert(TPrivacyActionNougatExtend.make(nougat));
        }
        if (nougat.getActionId() == 18002L) {
            List<TPrivacySensitiveWord> sensitiveWordList = androidCaptureInfoAction.getPrivacySensitiveWordResult(Collections.singletonList(nougat),
                    taskContext.getSensitiveWords(), new HashMap<>());
            if (!sensitiveWordList.isEmpty()) {
                privacySensitiveWordMapper.insertList(sensitiveWordList);
            }
        }
    }

    private  void analysisSuspiciousSdk(ActionExecutor executor, DynamicTaskContext taskContext) {
        // 从包名中解析出疑似SDK
        List<SuspiciousSdkBO> sdkBOList = SuspiciousSdkUtils.extractPackageName(
                taskContext.getTaskDetailVo(), executor, taskContext.getSdkNameSet(),
                taskContext.getFirstPartyNameSet(), taskContext.getTwoLevelSdkNameSet(), sdkWhitelistRuleService);
        sdkBOList.forEach(sdk -> taskContext.getAllSuspiciousSdkNames().add(sdk.getSuspiciousSdk().getPackageName()));
        Set<String> allSuspiciousSdkNames = sdkBOList.parallelStream().map(sdk -> sdk.getSuspiciousSdk().getPackageName()).collect(Collectors.toSet());
        List<SuspiciousSdkBO> suspiciousSdkBOList =  sdkBOList.parallelStream().filter(
                        // 有com.abc.xxx时，要把com.abc的记录排除掉
                        sdk -> packageNamePrefix(allSuspiciousSdkNames, sdk.getSuspiciousSdk().getPackageName()))
                .collect(Collectors.toList());
        // 更改行为的主体和包名为疑似SDK类型
        setSuspiciousSdkExecutorAndPackageName(executor, suspiciousSdkBOList, taskContext.getProductMap(), taskContext.getCompanyMap());
        // 疑似SDK入库
        insertSuspiciousSdk(suspiciousSdkBOList, taskContext);
    }

    private void insertSuspiciousSdk(List<SuspiciousSdkBO> suspiciousSdkBOList, DynamicTaskContext taskContext) {
        if (suspiciousSdkBOList.isEmpty()) {
            return;
        }
        List<TSuspiciousSdk> suspiciousSdks = new ArrayList<>(suspiciousSdkBOList.size());
        List<String> packageNameList = suspiciousSdkBOList.stream()
                .map(sdkBO -> sdkBO.getSuspiciousSdk().getPackageName())
                .distinct()
                .collect(Collectors.toList());
        Map<String, TSuspiciousSdkLibrary> libraryMap = suspiciousSdkLibraryMapper.findInPackageName(packageNameList)
                .stream().collect(Collectors.toMap(TSuspiciousSdkLibrary::getPackageName, Function.identity(), (key1, key2) -> key2));
        suspiciousSdkBOList.forEach(sdkBO -> {
            TSuspiciousSdk sdk = new TSuspiciousSdk();
            sdk.setTaskId(sdkBO.getSuspiciousSdk().getTaskId());
            sdk.setExecutor(sdkBO.getSuspiciousSdk().getExecutor());
            sdk.setExecutorType(sdkBO.getSuspiciousSdk().getExecutorType());
            // 更新疑似SDK库信息
            updateSuspiciousSdkLibrary(sdk, sdkBO.getSuspiciousSdk().getPackageName(), sdkBO.getPrivacyActionNougat(),
                    taskContext.getTerminalType(), taskContext.getPermissionMap(), taskContext.getActionNougatMap(), libraryMap);
            suspiciousSdks.add(sdk);
        });
        if (!suspiciousSdks.isEmpty()) {
            InsertListHelper.insertList(suspiciousSdks, suspiciousSdkMapper::insertList);
        }
    }

    private void updateSuspiciousSdkLibrary(TSuspiciousSdk sdk, String packageName, ActionExecutor actionExecutor,
                                            TerminalTypeEnum terminalType, Map<String, TPermission> permissionMap,
                                            Map<Long, TActionNougat> actionNougats, Map<String, TSuspiciousSdkLibrary> libraryMap) {
        TSuspiciousSdkLibrary library = libraryMap.get(packageName);
        if (library == null) {
            // 疑似SDK库里面没有，创建一条记录
            library = new TSuspiciousSdkLibrary();
            library.setName(packageName);
            library.setPackageName(packageName);
            library.setTerminalType(terminalType.getValue());
            library.setPermissionCodes(getSuspiciousSdkPermissionCode("", permissionMap,
                    actionNougats, actionExecutor));
            suspiciousSdkLibraryMapper.insert(library);
        } else {
            String oldPermissionCode = Objects.isNull(library.getPermissionCodes()) ? "" : library.getPermissionCodes();
            String updateCodes = getSuspiciousSdkPermissionCode(oldPermissionCode, permissionMap,
                    actionNougats, actionExecutor);
            library.setTerminalType(terminalType.getValue());
            // 如果SDK库里有，判断是否有新增权限
            if (StringUtils.isNotBlank(updateCodes)
                    && !updateCodes.equals(library.getPermissionCodes())) {
                library.setPermissionCodes(updateCodes);
                library.setUpdateTime(new Date());
                suspiciousSdkLibraryMapper.updateByPrimaryKey(library);
            }
        }
        sdk.setSuspiciousSdkLibraryId(library.getId());
    }

    protected String getSuspiciousSdkPermissionCode(String oldPermissionCode, Map<String, TPermission> permissionMap,
                                                    Map<Long, TActionNougat> actionNougats, ActionExecutor actionExecutor) {
        if (actionExecutor instanceof TPrivacyActionNougat) {
            TPrivacyActionNougat nougat = (TPrivacyActionNougat) actionExecutor;
            TActionNougat actionNougat = actionNougats.get(nougat.getActionId());
            if (Objects.isNull(actionNougat)) {
                return "";
            }
            TPermission permission = permissionMap.get(actionNougat.getActionPermission());
            if (Objects.isNull(permission)) {
                return "";
            }
            List<String> permissionCodes = new ArrayList<>();
            if (!oldPermissionCode.contains(permission.getPermissionCode())) {
                permissionCodes.add(permission.getPermissionCode());
            }
            if (permissionCodes.isEmpty()) {
                return oldPermissionCode;
            } else {
                if (StringUtils.isNotBlank(oldPermissionCode)) {
                    permissionCodes.add(0, oldPermissionCode);
                }
                StringJoiner joiner = new StringJoiner(",");
                permissionCodes.forEach(joiner::add);
                return joiner.toString();
            }
        } else {
            return "";
        }
    }

    @Override
    public void cleanDynamicAction(Long taskId) {
        privacyActionNougatMapper.deleteByTaskId(taskId);
        privacyActionNougatExtendMapper.deleteByTaskId(taskId);
        cleanNetAction(taskId);
        cacheService.cleanSensorAction(taskId);
    }

    @Override
    public List<RealTimeBehaviorLog> getAllActionList(Long taskId) {
        List<TPrivacyActionNougat> nougatList = privacyActionNougatMapper.findByTaskId(taskId,
                Collections.emptyList(),
                Collections.emptyList(), Collections.emptyList());
        return nougatList.stream().map(this::buildBehaviorLog).collect(Collectors.toList());
    }

    @Override
    public List<IOSRealTimeLog> getAllIosActionList(Long taskId) {
        TAssets assets = assetsMapper.getAssetByTaskId(taskId);
        List<TPrivacyActionNougat> nougatList = privacyActionNougatMapper.findByTaskId(taskId, Collections.emptyList(),
                Collections.singletonList(PrivacyStatusEnum.YES),
                Collections.emptyList());
        return nougatList.stream().map(nougat -> iosActionLogConvertHelper.buildRealTimeLog(nougat, assets.getName())).collect(Collectors.toList());
    }

    @Override
    public RealTimeBehaviorLog getActionById(Long id) {
        return buildBehaviorLog(privacyActionNougatMapper.findById(id));
    }

    public PageInfo<RealTimeBehaviorLog> getActionPage(Long taskId, List<Long> actionIdList,
                                                       List<PrivacyStatusEnum> isPersonalList,
                                                       List<BehaviorStageEnum> behaviorStageList,
                                                       int page, int rows) {
        PageHelper.startPage(page, rows);
        List<TPrivacyActionNougat> nougatList = privacyActionNougatMapper.findByTaskId(taskId, actionIdList, isPersonalList, behaviorStageList);
        PageInfo<TPrivacyActionNougat> pageInfo = new PageInfo<>(nougatList);
        PageInfo<RealTimeBehaviorLog> resultPage = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, resultPage);
        List<RealTimeBehaviorLog> logList = nougatList.stream()
                .map(this::buildBehaviorLog)
                .collect(Collectors.toList());
        resultPage.setList(logList);
        return resultPage;
    }

    public RealTimeBehaviorLog buildBehaviorLog(TPrivacyActionNougat nougat) {
        if (nougat == null) {
            throw new IjiamiRuntimeException("行为不存在");
        }
        RealTimeBehaviorLog logVO = new RealTimeBehaviorLog();
        logVO.setId(nougat.getId().toString());
        logVO.setJniStackInfo(nougat.getJniStackInfo());
        logVO.setStackInfo(nougat.getStackInfo());
        logVO.setDetailsData(nougat.getDetailsData());
        logVO.setActionTime(nougat.getActionTimeStamp());
        logVO.setTypeName(nougat.getActionName());
        logVO.setTypeId(nougat.getActionId());
        logVO.setIsPersonal(PrivacyStatusEnum.getItem(Integer.parseInt(nougat.getIsPersonal())));
        logVO.setExecutor(nougat.getExecutor());
        logVO.setExecutorType(nougat.getExecutorType());
        logVO.setPackageName(nougat.getPackageName());
        logVO.setBehaviorStage(nougat.getBehaviorStage().getValue());
        return logVO;
    }

    @Override
    public List<String> getActionTypeList(Long taskId) {
        return privacyActionNougatMapper.findByTaskId(taskId, Collections.emptyList(), Collections.emptyList(), Collections.emptyList())
                .stream()
                .map(TPrivacyActionNougat::getActionId)
                .map(Object::toString)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public IOSRealTimeLog getIosActionById(Long id) {
        TPrivacyActionNougat nougat = privacyActionNougatMapper.findById(id);
        TAssets assets = assetsMapper.getAssetByTaskId(nougat.getTaskId());
        return iosActionLogConvertHelper.buildRealTimeLog(nougat, assets.getName());
    }

    @Override
    public PageInfo<IOSRealTimeLog> getIosActionPage(Long taskId, List<Long> actionIdList,
                                                     List<PrivacyStatusEnum> isPersonalList,
                                                     List<BehaviorStageEnum> behaviorStageList,
                                                     int page, int rows) {
        TAssets assets = assetsMapper.getAssetByTaskId(taskId);
        PageHelper.startPage(page, rows);
        List<TPrivacyActionNougat> nougatList = privacyActionNougatMapper.findByTaskId(taskId, actionIdList, isPersonalList, behaviorStageList);
        PageInfo<TPrivacyActionNougat> pageInfo = new PageInfo<>(nougatList);
        PageInfo<IOSRealTimeLog> resultPage = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, resultPage);
        List<IOSRealTimeLog> logList = nougatList.stream()
                .map(log -> iosActionLogConvertHelper.buildRealTimeLog(log, assets.getName()))
                .collect(Collectors.toList());
        resultPage.setList(logList);
        return resultPage;
    }

    @Override
    public void deleteActionList(Long taskId, List<String> logIds) {
        logIds.forEach(id -> {
            privacyActionNougatMapper.deleteByPrimaryKey(id);
        });
    }

    @Override
    public void insertAndroidNetAction(TPrivacyOutsideAddress address, DynamicTaskContext taskContext) {
        privacyOutsideAddressMapper.insert(address);
        // 解析疑似SDK
        analysisSuspiciousSdk(address, taskContext);
        List<TPrivacySensitiveWord> sensitiveWordList;
        if (CommonUtil.isEmptyDetails(address.getDetailsData())) {
            List<TPrivacyActionNougat> item18002List = privacyActionNougatMapper.findByTaskId(address.getTaskId(),
                    Collections.singletonList(18002L), Collections.emptyList(), Collections.emptyList());
            sensitiveWordList = androidCaptureInfoAction.analyzeEmptyDetailsWord(address, item18002List, taskContext.getSensitiveWords());
        } else {
            // 分析传输个人信息数据
            sensitiveWordList = androidCaptureHostIpAction.getPrivacySensitiveWordResult(address, taskContext.getSensitiveWords());
        }
        if (!sensitiveWordList.isEmpty()) {
            sensitiveWordList.forEach(sensitiveWord -> {
                // 解析疑似SDK
                analysisSuspiciousSdk(address, taskContext);
            });
            privacySensitiveWordMapper.insertList(sensitiveWordList);
        }
    }

    @Override
    public void insertHarmonyNetAction(TPrivacyOutsideAddress address, DynamicTaskContext taskContext) {
        privacyOutsideAddressMapper.insert(address);
        // 分析传输个人信息数据
        List<TPrivacySensitiveWord> sensitiveWordList = androidCaptureHostIpAction.getPrivacySensitiveWordResult(address, taskContext.getSensitiveWords());
        if (!sensitiveWordList.isEmpty()) {
            privacySensitiveWordMapper.insertList(sensitiveWordList);
        }
    }

    @Override
    public void insertIosNetAction(TPrivacyOutsideAddress address, DynamicTaskContext taskData) {
        privacyOutsideAddressMapper.insert(address);
    }

    @Override
    public void insertAppletNetAction(TPrivacyOutsideAddress address, DynamicTaskContext taskData, AppletActionBO data) {
        privacyOutsideAddressMapper.insert(address);
        List<TPrivacySensitiveWord> sensitiveWordList = appletCaptureAction.analyzeCaptureInfo(taskData.getTaskId(),
                address.getBehaviorStage(), data, taskData.getSensitiveWords());
        if (!sensitiveWordList.isEmpty()) {
            privacySensitiveWordMapper.insertList(sensitiveWordList);
        }
    }

    @Override
    public void cleanNetAction(Long taskId) {
        privacyOutsideAddressMapper.deleteByTaskId(taskId);
        log.info("清除缓存 {}", taskId);
    }


    @Override
    public PageInfo<RealTimeNetLog> getNetActionPage(Long taskId, int page, int rows) {
        PageHelper.startPage(page, rows);
        Example query = new Example(TPrivacyOutsideAddress.class);
        query.createCriteria().andEqualTo("taskId", taskId);
        query.orderBy("actionTime").desc();
        List<TPrivacyOutsideAddress> outsideAddressesList = privacyOutsideAddressMapper.selectByExample(query);
        PageInfo<TPrivacyOutsideAddress> pageInfo = new PageInfo<>(outsideAddressesList);
        PageInfo<RealTimeNetLog> resultPage = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, resultPage);
        resultPage.setList(outsideAddressesList.stream().map(a -> iosActionLogConvertHelper.buildNetLog(a)).collect(Collectors.toList()));
        return resultPage;
    }

    @Override
    public RealTimeNetLog getNetActionById(Long id) {
        return iosActionLogConvertHelper.buildNetLog(privacyOutsideAddressMapper.selectByPrimaryKey(id));
    }

    @Override
    public List<String> getIosActionTypeList(Long taskId) {
        List<TPrivacyActionNougat> nougatList = privacyActionNougatMapper.findByTaskId(taskId,
                Collections.emptyList(),
                Collections.singletonList(PrivacyStatusEnum.YES),
                Collections.emptyList());
        return nougatList.stream().map(TPrivacyActionNougat::getActionName).distinct().collect(Collectors.toList());
    }



}
