package cn.ijiami.detection.service.impl;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.io.FileUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import cn.ijiami.detection.VO.PermissionVO;
import cn.ijiami.detection.VO.SensitiveWordImgVO;
import cn.ijiami.detection.VO.detection.BaseMessageVO;
import cn.ijiami.detection.VO.gongxinbu.GongxinbuVO;
import cn.ijiami.detection.config.IjiamiCommonProperties;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.server.client.base.enums.DetectionStatusEnum;
import cn.ijiami.detection.mapper.TPrivacyActionNougatMapper;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.service.api.GongxinbuService;
import cn.ijiami.detection.service.api.IPrivacyDetectionService;
import cn.ijiami.detection.service.api.IPrivacySensitiveWordService;
import cn.ijiami.detection.service.api.ITaskService;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.report.context.ReportContext;
import cn.ijiami.report.service.api.IReportService;
import cn.ijiami.report.service.api.ReportManager;
import cn.ijiami.report.service.impl.DefaultReportManagerImpl;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @date 2019/11/15 14:50
 */
@Service
public class GongxinbuServiceImpl implements GongxinbuService {

    private final TTaskMapper taskMapper;
    private final ITaskService taskService;
    private final IjiamiCommonProperties commonProperties;
    private final IPrivacySensitiveWordService privacySensitiveWordService;
    private final IPrivacyDetectionService privacyDetectionService;
    private final TPrivacyActionNougatMapper privacyActionNougatMapper;
    private final IReportService reportService;

    public GongxinbuServiceImpl(TTaskMapper taskMapper,
                                ITaskService taskService,
                                IjiamiCommonProperties commonProperties,
                                IPrivacySensitiveWordService privacySensitiveWordService,
                                IPrivacyDetectionService privacyDetectionService,
                                TPrivacyActionNougatMapper privacyActionNougatMapper,
                                IReportService reportService) {
        this.taskMapper = taskMapper;
        this.taskService = taskService;
        this.commonProperties = commonProperties;
        this.privacySensitiveWordService = privacySensitiveWordService;
        this.privacyDetectionService = privacyDetectionService;
        this.privacyActionNougatMapper = privacyActionNougatMapper;
        this.reportService = reportService;
    }

    @Override
    public File exportExcel(Long userId) {
        List<GongxinbuVO> data = new ArrayList<>();

        Example example = new Example(TTask.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("taskTatus", DetectionStatusEnum.DETECTION_OVER);
        criteria.andEqualTo("createUserId", userId);
        List<TTask> tasks = taskMapper.selectByExample(example);

        for (TTask task : tasks) {
            GongxinbuVO vo = new GongxinbuVO();

            //获取基本信息
            BaseMessageVO baseMessage = taskService.getBaseMessage(task.getApkDetectionDetailId());
            if (baseMessage == null) continue;
            BeanUtils.copyProperties(baseMessage, vo);

            //统计个人信息
            List<TPrivacySensitiveWord> sensitiveWords = privacySensitiveWordService.findByTaskId(task.getTaskId(), 0);
            if (!CollectionUtils.isEmpty(sensitiveWords)) {
                vo.setCol1(true);
                vo.setCol2(true);
            }

            List<TPrivacySensitiveWord> sdkSensitiveWords = sensitiveWords.stream().filter(p -> p.getSdkId() != null).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(sdkSensitiveWords)) {
                vo.setCol3(true);
            }

            //统计权限
            List<PermissionVO> xmlPermission = privacyDetectionService.getXMLPermission(task.getApkDetectionDetailId(), null);
            if (!CollectionUtils.isEmpty(xmlPermission)) {
                vo.setCol5(true);
                vo.setCol6(true);
                vo.setCol7(true);
            }

            data.add(vo);
        }

        String templateName = "report_template.xlsx";
        String template = commonProperties.getProperty("ijiami.report.root.path") + "reportTemplate/" + templateName;
        String out = commonProperties.getProperty("ijiami.report.root.path") + "out/" + "工信部信管涵【2019】337号_1000个应用检测结果.xlsx";
        GongxinbuExcelUtil.exportReport(template, out, data);

        File file = new File(out);
        return file.exists() ? file : null;
    }

    @Override
    public File exportWord(Long userId) throws IjiamiApplicationException {
        Example example = new Example(TTask.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("taskTatus", DetectionStatusEnum.DETECTION_OVER);
        criteria.andEqualTo("createUserId", userId);
        List<TTask> tasks = taskMapper.selectByExample(example);

        String templateName = "工信部信管涵【2019】337号报告模板.ftl";
        String reportRootPath = commonProperties.getProperty("ijiami.report.root.path");

        try {
            File zipDir = new File(reportRootPath + "out" + File.separator + "gxb");
            if (zipDir.exists()) {
                FileUtils.deleteDirectory(zipDir);
            }

            File zipFile = new File(reportRootPath + "out" + File.separator + "工信部信管涵【2019】337号报告.zip");
            if (zipFile.exists()) {
                FileUtils.forceDelete(zipFile);
            }

            for (TTask task : tasks) {
                //获取基本信息
                BaseMessageVO baseMessage = taskService.getBaseMessage(task.getApkDetectionDetailId());
                if (baseMessage == null) continue;

                Map<String, Object> map = buildData(task);
                String outFileName = baseMessage.getAppName() + "_工信部信管涵【2019】337号_" + new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date());
                ReportContext reportContext = reportService.generateReportContext(map, templateName, outFileName, 1);
                ReportManager reportManager = new DefaultReportManagerImpl(reportContext);
                String reportName = reportManager.generateReport();
                File file = new File(reportRootPath + "out" + File.separator + reportName);
                if (!file.exists()) continue;
                FileUtils.moveFileToDirectory(file, zipDir, true);
            }

            CommonUtil.compress(zipDir.getAbsolutePath(), zipFile.getAbsolutePath());
            return zipFile;
        } catch (IOException e) {
            e.getMessage();
            throw new IjiamiApplicationException("导出报告发生错误");
        }
    }

    private Map<String, Object> buildData(TTask task) {
        Map<String, Object> map = new HashMap<>();
        BaseMessageVO baseMessage = taskService.getBaseMessage(task.getApkDetectionDetailId());
        List<TPrivacySensitiveWord> privacySensitiveWords = privacySensitiveWordService.findByTaskId(task.getTaskId(), 0);
        List<TPrivacySensitiveWord> sdkSensitiveWords = privacySensitiveWords.stream().filter(p -> p.getSdkId() != null).collect(Collectors.toList());
        List<SensitiveWordImgVO> sensitiveWordImgs = privacySensitiveWordService.findImgByTaskId(task.getTaskId(), 0);
        List<TPrivacyActionNougat> privacyActionNougats = privacyActionNougatMapper.countByTaskId(task.getTaskId());
        List<PermissionVO> xmlPermission = privacyDetectionService.getXMLPermission(task.getApkDetectionDetailId(), null);
        for (PermissionVO permissionVO : xmlPermission) {
            for (TPrivacyActionNougat privacyActionNougat : privacyActionNougats) {
                if (permissionVO.getName().equals(privacyActionNougat.getActionPermission())) {
                    permissionVO.setUsed(true);
                    permissionVO.setUseCount(privacyActionNougat.getCounter());
                    break;
                }
            }
        }

        map.put("baseMessage", baseMessage);
        map.put("privacySensitiveWords", privacySensitiveWords);
        map.put("sdkSensitiveWords", sdkSensitiveWords);
        map.put("sensitiveWordImgs", sensitiveWordImgs);
        map.put("xmlPermission", xmlPermission);
        map.put("startTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(task.getTaskStarttime()));
        return map;
    }
}
