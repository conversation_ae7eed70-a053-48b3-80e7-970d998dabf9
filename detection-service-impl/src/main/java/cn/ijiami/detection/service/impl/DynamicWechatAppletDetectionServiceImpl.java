package cn.ijiami.detection.service.impl;

import static cn.ijiami.detection.constant.DistributedLockConstant.KEY_ANALYSIS_TASK_DATA_PREFIX;
import static cn.ijiami.detection.server.client.base.enums.DynamicAutoStatusEnum.DETECTION_AUTO_FAILED;
import static cn.ijiami.detection.server.client.base.enums.DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED;
import static cn.ijiami.detection.server.client.base.enums.DynamicAutoStatusEnum.DETECTION_AUTO_WAITING;
import static cn.ijiami.detection.job.XxlDetectAnalysisHelper.buildAppletBaseInfoItem;
import static cn.ijiami.detection.job.XxlDetectAnalysisHelper.buildAppletPermissionItem;
import static cn.ijiami.detection.utils.CommonUtil.analysisErrorMsg;
import static cn.ijiami.detection.utils.CommonUtil.isSamePlugins;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.ijiami.detection.enums.BooleanEnum;
import cn.ijiami.detection.utils.SensitiveUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import cn.ijiami.detection.VO.PrivacyPolicyCheck;
import cn.ijiami.detection.VO.TTaskExtendVO;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.VO.detection.ScreenshotImage;
import cn.ijiami.detection.VO.detection.SdkVO;
import cn.ijiami.detection.analyzer.AppletBehaviorInfoAction;
import cn.ijiami.detection.analyzer.bo.DetectDataBO;
import cn.ijiami.detection.bean.DetectionItem;
import cn.ijiami.detection.bean.ResultContent;
import cn.ijiami.detection.bean.WechatAppletStaticDetectionResult;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.dao.TaskDAO;
import cn.ijiami.detection.dao.UserUseDeviceDAO;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.entity.TComplianceAppletPlugins;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.entity.TPrivacyPolicyItem;
import cn.ijiami.detection.entity.TPrivacyPolicyResult;
import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import cn.ijiami.detection.entity.TPrivacySharedPrefs;
import cn.ijiami.detection.entity.TSensitiveWord;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.entity.TTaskData;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.BroadcastMessageTypeEnum;
import cn.ijiami.detection.enums.CookieMarkEnum;
import cn.ijiami.detection.server.client.base.enums.DetectionStatusEnum;
import cn.ijiami.detection.enums.DetectionTypeEnum;
import cn.ijiami.detection.server.client.base.enums.DynamicAutoStatusEnum;
import cn.ijiami.detection.server.client.base.enums.DynamicAutoSubStatusEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.enums.IdbStagedDataEnum;
import cn.ijiami.detection.enums.PrivacyLawId;
import cn.ijiami.detection.enums.PrivacyPolicyItemNoEnum;
import cn.ijiami.detection.enums.PrivacyPolicyResultCategoryEnum;
import cn.ijiami.detection.enums.PrivacyPolicyResultStatusEnum;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.enums.TaskDetectionTypeEnum;
import cn.ijiami.detection.server.client.base.enums.TerminalTypeEnum;
import cn.ijiami.detection.exception.DownloadZipFailException;
import cn.ijiami.detection.exception.TaskDataErrorException;
import cn.ijiami.detection.exception.UncompressFailException;
import cn.ijiami.detection.helper.bean.PrivacyPolicyTextInfo;
import cn.ijiami.detection.job.XxlDetectAnalysisHelper;
import cn.ijiami.detection.mapper.TAppletScopeApiMapper;
import cn.ijiami.detection.mapper.TComplianceAppletPluginsMapper;
import cn.ijiami.detection.mapper.TPrivacyActionNougatMapper;
import cn.ijiami.detection.mapper.TPrivacyLawsDetailMapper;
import cn.ijiami.detection.mapper.TPrivacyOutsideAddressMapper;
import cn.ijiami.detection.mapper.TPrivacySensitiveWordMapper;
import cn.ijiami.detection.mapper.TPrivacySharedPrefsMapper;
import cn.ijiami.detection.mapper.TSensitiveWordMapper;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.enums.MiitDataTypeEnum;
import cn.ijiami.detection.miit.enums.MiitUITypeEnum;
import cn.ijiami.detection.miit.parser.AppletResultDataLogParser;
import cn.ijiami.detection.query.AppletStaticDetectionInfo;
import cn.ijiami.detection.service.IAppletService;
import cn.ijiami.detection.service.api.IAssetsService;
import cn.ijiami.detection.service.api.IDynamicTaskContextService;
import cn.ijiami.detection.service.api.IDynamicWechatAppletDetectionService;
import cn.ijiami.detection.service.api.IScreenshotImageService;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.ConstantsUtils;
import cn.ijiami.detection.utils.FileVOUtils;
import cn.ijiami.detection.utils.HttpUtils;
import cn.ijiami.detection.utils.IpUtil;
import cn.ijiami.detection.utils.UriUtils;
import cn.ijiami.detection.websocket.idb.ExecutorServiceHelper;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.common.exception.IjiamiCommandException;
import cn.ijiami.framework.file.vo.FileVO;
import cn.ijiami.framework.kit.utils.FileUtil;
import cn.ijiami.framework.kit.utils.UuidUtil;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DynamicWechatAppletDetectionServiceImpl.java
 * @Description 微信小程序检测类
 * @createTime 2023年03月31日 17:18:00
 */
@Slf4j
@Service
public class DynamicWechatAppletDetectionServiceImpl extends AbstractAppletDynamicDetectionService<TaskDetailVO> implements IDynamicWechatAppletDetectionService {

    /**
     * 动态检测需要解析的文件组
     */
    private static final Map<BehaviorStageEnum, String> ANALYZE_FILE_MAP = new HashMap<>();

    static {
        ANALYZE_FILE_MAP.put(BehaviorStageEnum.BEHAVIOR_GRANT, "behavior_info_01.json");
        ANALYZE_FILE_MAP.put(BehaviorStageEnum.BEHAVIOR_FRONT, "behavior_info_02.json");
        ANALYZE_FILE_MAP.put(BehaviorStageEnum.BEHAVIOR_GROUND, "behavior_info_03.json");
        ANALYZE_FILE_MAP.put(BehaviorStageEnum.BEHAVIOR_EXIT, "behavior_info_04.json");
    }

//    @Autowired
//    private IpUtil ipUtil;

    @Autowired
    private TPrivacyOutsideAddressMapper privacyOutsideAddressMapper;

    @Autowired
    private TPrivacyActionNougatMapper privacyActionNougatMapper;

    @Autowired
    private TPrivacySensitiveWordMapper privacySensitiveWordMapper;

    @Autowired
    private TPrivacySharedPrefsMapper privacySharedPrefsMapper;

//    @Autowired
//    private AppletCaptureAction captureAction;
//
//    @Autowired
//    private IdGeneratorService idGeneratorService;
//
//    @Autowired
//    private TActionNougatMapper tActionNougatMapper;

    @Autowired
    private TSensitiveWordMapper tSensitiveWordMapper;

    @Autowired
    private IDynamicTaskContextService dynamicTaskDataService;

//    @Autowired
//    private IPrivacyCheckService iPrivacyCheckService;

    @Autowired
    private TPrivacyLawsDetailMapper privacyLawsDetailMapper;

    @Autowired
    private AppletResultDataLogParser miniProgramResultDataLogParser;

//    @Autowired
//    private DetectPointManager detectPointManager;

//    @Autowired
//    private TPermissionMapper permissionMapper;

    @Autowired
    private ExecutorServiceHelper executorServiceHelper;

    @Autowired
    private UserUseDeviceDAO userUseDeviceDAO;

    @Autowired
    private AppletBehaviorInfoAction appletBehaviorInfoAction;

    @Autowired
    private IScreenshotImageService screenshotImageService;

//    @Autowired
//    private TPrivacyPolicyTypeMapper tPrivacyPolicyTypeMapper;

    @Autowired
    private TAppletScopeApiMapper appletScopeApiMapper;

//    @Autowired
//    private TManualScreenshotImageMapper manualScreenshotImageMapper;

    @Autowired
    private TaskDAO taskDAO;

    @Value("${ijiami.applet.remote.tool.status:false}")
    private boolean isRemote;

//    @Autowired
//    private IjiamiCommonProperties ijiamiCommonProperties;
//
//    @Autowired
//    private IBaseFileService fileService;

    @Autowired
    private IAssetsService assetsService;
    
//    @Autowired
//    private DeviceManagerService deviceManagerService;

    @Autowired
    private IAppletService appletService;

    @Autowired
    private TComplianceAppletPluginsMapper pluginsMapper;

    @Override
    public void analysisAutoFromUpload(TTask task, IdbStagedDataEnum stage, MultipartFile data) {
        // 校验任务存在
        if (!checkDocumentValid(task)) {
            log.info("TaskId:{} 任务不存在", task.getTaskId());
            return;
        }
        if (task.getDynamicStatus() == DETECTION_AUTO_SUCCEED) {
            log.info("TaskId:{} 动态检测任务阶段数据完成", task.getTaskId());
            return;
        }
        if (task.getDynamicStatus() == DETECTION_AUTO_FAILED) {
            log.info("TaskId:{} 动态检测已经中断", task.getTaskId());
            return;
        }
        if (task.getDynamicStatus() == DETECTION_AUTO_WAITING) {
            log.info("TaskId:{} 动态检测等待状态 进行状态转变", task.getTaskId());
            taskDAO.updateDynamicAutoWaiting(task, "", false);
        }
        try {
            File file = saveFile(task.getMd5(), task.getTaskId(), "AUTO_" + stage.getValue(), data);
            String dfsPath = uploadDataFileToFastDfs(file.getAbsolutePath());
            TTaskData taskData = taskDataService.saveTaskData(task, DynamicAutoSubStatusEnum.getItem(stage.getValue()), dfsPath);
            // 异步线程解析需要放到保存文件后面，如果放到文件保存完成前，请求已经结束文件上传的缓存被删掉会导致无法保存文件
            executorServiceHelper.executeInWithDataAnalysisExecutor(() -> analysisAutoStagedFile(task, taskData, stage, file.getAbsolutePath()));
        } catch (Exception e) {
            log.error("TaskId:{} 检测数据处理 - {}，数据保存异常:{}", task.getTaskId(), DetectionTypeEnum.DYNAMIC.getName(), e.getMessage(), e);
            updateTaskInfoWhenException(task, DetectionTypeEnum.DYNAMIC, "数据包保存失败");
            sendAppletFailBroadcast(task, "微信小程序动态检测失败");
        }
    }

    @Override
    public void analysisAutoFromXXLData(Long taskId, IdbStagedDataEnum stage, String fastDfsDataPath) throws IOException, IjiamiApplicationException {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (!checkDocumentValid(task)) {
            log.info("TaskId:{} 任务不存在", task.getTaskId());
            return;
        }
        try {
            File file = downloadFile(fastDfsDataPath);
            TTaskData taskData = taskDataService.saveTaskData(task, DynamicAutoSubStatusEnum.getItem(stage.getValue()), fastDfsDataPath);
            analysisAutoStagedFile(task, taskData, stage, file.getAbsolutePath());
        } catch (Exception e) {
            log.error("TaskId:{} 检测数据处理 - {}，数据保存异常:{}", task.getTaskId(), DetectionTypeEnum.DYNAMIC.getName(), e.getMessage(), e);
            updateTaskInfoWhenException(task, DetectionTypeEnum.DYNAMIC, "数据包保存失败");
            sendAppletFailBroadcast(task, "微信小程序动态检测失败");
        }
    }

    @Override
    protected void analysisAutoOldFile(TTask task, String dataPath) {
        String lockKey = KEY_ANALYSIS_TASK_DATA_PREFIX + task.getTaskId();
        if (!distributedLockService.tryLock(lockKey, TimeUnit.MINUTES.toMillis(10))) {
            log.info("TaskId:{} key={} 检测数据处理已经在解析中", task.getTaskId(), lockKey);
            return;
        }
        long startTime = System.currentTimeMillis();
        String dynamicPath = commonProperties.getProperty("detection.tools.dynamic_path");
        String uncompress = null;
        try {
            uncompress = CommonUtil.uncompress(dataPath, dynamicPath + UuidUtil.uuid());
            TTask updateTask = new TTask();
            updateTask.setTaskId(task.getTaskId());
            updateTask.setDynamicAutoDataProcess();
            updateTask.setDataPath(uploadDataFileToFastDfs(dataPath));
            taskMapper.updateByPrimaryKeySelective(updateTask);
            // 释放设备
            userUseDeviceDAO.userReleaseDevice(task);
            // 把保存的隐私文件和行为文件移到压缩包中
            moveDetectionFileToUncompress(task.getTaskId(), uncompress);
            getUrlPrivacyContent(uncompress);
            // 整合数据
            TaskDetailVO taskDetailVo = findById(task.getApkDetectionDetailId());
            taskDetailVo.setTaskId(task.getTaskId());
            Map<BehaviorStageEnum, DetectDataBO> detectDataMap = analysisUncompress(taskDetailVo, uncompress);
            deleteOldData(task.getTaskId());
            Map<Long, String> screenshotMap = getScreenShotMap(IdbStagedDataEnum.NONE, uncompress);
            // 录入检测结果
            insertTaskAnalyzeResult(detectDataMap, task.getTaskId(), screenshotMap);
            // 146号文数据解析
            CommonDetectInfo commonDetectInfo = analysisMiitDetectResult(task, uncompress, detectDataMap);
            // 合规风险检测
            analysisPrivacyCheck(task, detectDataMap, commonDetectInfo);
            // 微信小程序额外的信息
            savePluginsAndBasicInfo(task, uncompress);
            // 解析完成更新检测信息
            updateTaskInfoWhenAnalyzeFinish(task, DetectionTypeEnum.DYNAMIC);
            sendAppletSuccessBroadcast(task, "微信小程序动态检测完成");
            log.info("微信小程序 动态检测完成 taskId={} 时间={}s", task.getTaskId(), (System.currentTimeMillis() - startTime) / 1000);
        } catch (UncompressFailException | TaskDataErrorException | DownloadZipFailException e) {
            updateTaskInfoWhenException(task, DetectionTypeEnum.DYNAMIC, "数据包异常");
            log.error("TaskId:{} 检测数据处理 - {}，数据解析异常:{}", task.getTaskId(), DetectionTypeEnum.DYNAMIC.getName(), e.getMessage(), e);
        } catch (Throwable e) {
            log.error("TaskId:{} 检测数据处理 - {}，数据解析异常:{}", task.getTaskId(), DetectionTypeEnum.DYNAMIC.getName(), e.getMessage(), e);
            updateTaskInfoWhenException(task, DetectionTypeEnum.DYNAMIC, analysisErrorMsg(e));
            sendAppletFailBroadcast(task, "微信小程序动态检测失败");
        } finally {
            distributedLockService.unlock(lockKey);
            CommonUtil.deleteFile(uncompress);
            CommonUtil.deleteFile(dataPath);
            IpUtil.ipMap.clear();
            taskSortThread.checkTaskSortListAsSoonASPossible();
        }
    }

    @Override
    public void analysisDataRetry(Long taskId) {
        // 校验任务存在
        TTask task = checkTaskValid(taskId);
        if (Objects.isNull(task)) {
            return;
        }
        taskDAO.updateDynamicAutoDataProcess(task.getTaskId());
        String zipSuffix;
        if (TaskDetectionTypeEnum.isAuto(task.getDetectionType())) {
            zipSuffix = "_AUTO";
        } else {
            zipSuffix = "_MANUAL";
        }
        String dynamicPath = commonProperties.getProperty("detection.tools.dynamic_path");
        if (StringUtils.isNotBlank(task.getDataPath())) {
            // 整包的数据
            try {
                File file = new File(dynamicPath + task.getMd5() + "_" + task.getTaskId() + zipSuffix + ".zip");
                if (!file.exists()) {
                    try {
                        if (task.getDataPath() != null && task.getDataPath().startsWith("http")) {
                            FileUtils.copyURLToFile(new URL(task.getDataPath()), file);
                        } else {
                            FileUtils.copyURLToFile(new URL(UriUtils.getHttpUrl(task.getDataPath(), fastDFSIntranetIp)), file);
                        }
                    } catch (IOException e) {
                        log.error(String.format("TaskId:%d 解析失败", taskId), e);
                    }
                }
                if (task.getDetectionType() == TaskDetectionTypeEnum.FAST.getValue()) {
                    String uncompress = dynamicPath + UuidUtil.uuid();
                    CommonUtil.uncompress(file.getAbsolutePath(), uncompress);
                    analysisAutoOldFile(task, uncompress);
                } else {
                    analysisManualInternal(task, file.getAbsolutePath(), task.getDataPath());
                }
            } catch (Exception e) {
                log.error("检测数据处理 - {}，数据下载异常:{}", "检测失败", e.getMessage());
            }
        } else {
            try {
                // 分段的数据
                deleteOldData(task.getTaskId());
                obtainAndVerifyTaskData(task, taskDataList -> {
                    for (TTaskData taskData:taskDataList) {
                        File stagedZip = new File(saveFilePath(task.getMd5(), task.getTaskId(), "AUTO_" + taskData.getDynamicSubStatus().getValue()));
                        if (!stagedZip.exists()) {
                            FileUtils.copyURLToFile(new URL(UriUtils.getHttpUrl(taskData.getDataPath(), fastDFSIntranetIp)), stagedZip);
                        }
                        analysisStageData(task, taskData, IdbStagedDataEnum.getItem(taskData.getDynamicSubStatus().getValue()), stagedZip.getAbsolutePath());
                    }
                    analysisFinalStage(task, taskDataList);
                });
            } catch (Exception e) {
                log.error(String.format("TaskId:%d 解析失败", taskId), e);
            }
        }
    }

    @Override
    public void saveTaskPrivacyPolicy(TTask task, MultipartFile data) throws IOException {
        saveDetectionFile(task.getTaskId(), data);
    }

    @Override
    public void saveTaskActionNougat(TTask task, MultipartFile data) throws IOException {
        saveDetectionFile(task.getTaskId(), data);
    }

    private void saveDetectionFile(Long taskId, MultipartFile data) throws IOException {
        if (data == null) {
            throw new FileNotFoundException("上传文件不存在");
        }
        String dynamicPath = commonProperties.getProperty("detection.tools.dynamic_path");
        File file = new File(dynamicPath + data.getOriginalFilename());
        if (!file.getParentFile().exists()) {
            boolean mkdirs = file.getParentFile().mkdirs();
            if (!mkdirs) {
                throw new FileNotFoundException("无法创建文件夹");
            }
        }
        data.transferTo(file);
        cacheService.opsForStringList().rightPush(PinfoConstant.CACHE_MINI_PROGRAM_FILE + taskId, file.getAbsolutePath());
    }

    @Override
    protected void analysisFinalStage(TTask task, List<TTaskData> taskDataList) throws Exception {
        String dynamicPath = commonProperties.getProperty("detection.tools.dynamic_path");
        long startTime = System.currentTimeMillis();
        List<File> stagedZipList = new ArrayList<>();
        String uncompress = null;
        try {
            TTask updateTask = new TTask();
            updateTask.setTaskId(task.getTaskId());
            updateTask.setDynamicAutoDataProcess();
            taskMapper.updateByPrimaryKeySelective(updateTask);
            // 释放设备
            userUseDeviceDAO.userReleaseDevice(task);
            uncompress = dynamicPath + UuidUtil.uuid();
            // 解压阶段数据
            log.info("解压分阶段的数据 开始");
            for (TTaskData taskData : taskDataList) {
                File stagedZip = new File(saveFilePath(task.getMd5(), task.getTaskId(), "AUTO_" + taskData.getDynamicSubStatus().getValue()));
                if (!stagedZip.exists()) {
                    try {
                        FileUtils.copyURLToFile(new URL(UriUtils.getHttpUrl(taskData.getDataPath(), fastDFSIntranetIp)), stagedZip);
                        stagedZipList.add(stagedZip);
                    } catch (Exception e) {
                        taskDataRollback(task, taskData.getDynamicSubStatus());
                        throw new TaskDataErrorException(e);
                    }
                }
                try {
                    CommonUtil.uncompressOverwrite(stagedZip.getAbsolutePath(), uncompress);
                } catch (UncompressFailException e) {
                    log.info("压缩包损坏 删除={}", stagedZip.delete());
                    throw new TaskDataErrorException(e);
                }
            }
            log.info("解压分阶段的数据 完成");
            // 把保存的隐私文件和行为文件移到压缩包中
            moveDetectionFileToUncompress(task.getTaskId(), uncompress);
            getUrlPrivacyContent(uncompress);
            // 整合数据
            TaskDetailVO taskDetailVo = findById(task.getApkDetectionDetailId());
            taskDetailVo.setTaskId(task.getTaskId());
            Map<BehaviorStageEnum, DetectDataBO> detectDataMap = findDetectData(task.getTaskId());
            // 146号文数据解析
            CommonDetectInfo commonDetectInfo = analysisMiitDetectResult(task, uncompress, detectDataMap);
            // 合规风险检测
            analysisPrivacyCheck(task, detectDataMap, commonDetectInfo);
            // 微信小程序额外的信息
            savePluginsAndBasicInfo(task, uncompress);
            // 解析完成更新检测信息
            long analysisTakesTime = System.currentTimeMillis() - startTime;
            updateTaskInfoWhenAnalyzeFinish(task, DetectionTypeEnum.DYNAMIC, analysisTakesTime);
            sendAppletSuccessBroadcast(task, "微信小程序动态检测完成");
            log.info("微信小程序 动态检测最终数据解析完成 taskId={} 时间={}s", task.getTaskId(), analysisTakesTime / 1000);
        } finally {
            CommonUtil.deleteFile(uncompress);
            for (File stagedZip:stagedZipList) {
                CommonUtil.deleteFile(stagedZip.getAbsolutePath());
            }
        }
    }

    @Override
    protected void analysisStageData(TTask task, TTaskData taskData, IdbStagedDataEnum stageEnum, String dataPath) {
        long startTime = System.currentTimeMillis();
        String uncompress = null;
        try {
            String dynamicPath = commonProperties.getProperty("detection.tools.dynamic_path");
            uncompress = CommonUtil.uncompressTaskData(dataPath, dynamicPath + UuidUtil.uuid());
            // 整合数据
            TaskDetailVO taskDetailVo = findById(task.getApkDetectionDetailId());
            taskDetailVo.setTaskId(task.getTaskId());
            List<TActionNougat> actionNougatList = actionNougatMapper.findByTerminalType(TerminalTypeEnum.WECHAT_APPLET.getValue());
            Map<BehaviorStageEnum, DetectDataBO> detectDataMap = analysisIdbDataStageUncompress(taskDetailVo, uncompress, actionNougatList, stageEnum);
            // 录入检测结果
            Map<Long, String> screenshotMap = getScreenShotMap(stageEnum, uncompress);
            saveStageData(task.getTaskId(), taskData.getId(), stageEnum, detectDataMap, screenshotMap);
            log.info("微信小程序 动态检测阶段数据解析完成 taskId={} dataId={} 时间={}s", task.getTaskId(), taskData.getId(), (System.currentTimeMillis() - startTime) / 1000);
        } finally {
            CommonUtil.deleteFile(uncompress);
        }
    }

    /**
     * 分析解压后的文件
     *
     * @param taskDetailVo
     * @param uncompress
     * @return
     */
    private Map<BehaviorStageEnum, DetectDataBO> analysisIdbDataStageUncompress(TaskDetailVO taskDetailVo, String uncompress, List<TActionNougat> actionNougatList, IdbStagedDataEnum idbUploadDataStage) {
        List<TSensitiveWord> sensitiveWords = tSensitiveWordMapper.findByTerminalType(TerminalTypeEnum.WECHAT_APPLET.getValue());
        Map<BehaviorStageEnum, DetectDataBO> detectDataMap = new HashMap<>(8);
        DetectDataBO detectData = analyzeZipAction(idbUploadDataStage.getBehaviorStage(), idbUploadDataStage.getFileNames(), uncompress, taskDetailVo, actionNougatList, sensitiveWords);
        if (Objects.nonNull(detectData)) {
            detectDataMap.put(idbUploadDataStage.getBehaviorStage(), detectData);
        }
        return detectDataMap;
    }


    private Map<BehaviorStageEnum, DetectDataBO> findDetectData(Long taskId) {
        Map<BehaviorStageEnum, DetectDataBO> detectDataMap = new HashMap<>(8);
        for (BehaviorStageEnum behaviorStageEnum:ANALYZE_FILE_MAP.keySet()) {
            DetectDataBO detectDataBO = new DetectDataBO();
            detectDataBO.setPrivacyActionNougats(privacyActionNougatMapper.findByTaskId(taskId, Collections.emptyList(),
                    Collections.emptyList(), Collections.singletonList(behaviorStageEnum)));
            detectDataBO.setPrivacySensitiveWords(privacySensitiveWordMapper.findByTaskId(taskId, behaviorStageEnum.getValue()));
            detectDataBO.setPrivacyOutsideAddresses(privacyOutsideAddressMapper.findByTaskIdAndOutsideStackInfo(taskId,
                    org.apache.commons.lang3.StringUtils.EMPTY, org.apache.commons.lang3.StringUtils.EMPTY, behaviorStageEnum.getValue()));
            detectDataBO.setPrivacySharedPrefs(privacySharedPrefsMapper.findByTaskId(taskId, behaviorStageEnum.getValue()));
            detectDataMap.put(behaviorStageEnum, detectDataBO);
        }
        return detectDataMap;
    }

    private void savePluginsAndBasicInfo(TTask task, String uncompress) throws Exception {
        // 保存小程序插件信息
        File dirs = new File(uncompress + File.separator + "policy" + File.separator + "staticXML");
        File[] files = dirs.listFiles();
        if (Objects.nonNull(files) && files.length > 0) {
            File infoFile = new File(dirs, "基本信息.xml");
            List<File> pluginsFiles = Arrays.stream(files).filter(file -> !file.getName().equals(infoFile.getName())).collect(Collectors.toList());
            appletService.savePluginsAndBasicInfo(task, pluginsFiles, infoFile);
        } else {
            log.info("找不到微信小程序的插件信息和基本信息文件");
        }
    }

    // 异步线程去处理，避免卡住上传流程，导致idb那边上传超时
    @Override
    public void analysisManual(Long taskId, MultipartFile data) {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (!checkDocumentValid(task)) {
            log.info("TaskId:{} 任务不存在", task.getTaskId());
            return;
        }
        try {
            File file = saveFile(task.getMd5(), task.getTaskId(), "MANUAL", data);
            // 异步线程解析需要放到保存文件后面，如果放到文件保存完成前，请求已经结束文件上传的缓存被删掉会导致无法保存文件
            executorServiceHelper.executeInWithDataAnalysisExecutor(() -> analysisManualInternal(task, file.getAbsolutePath(), org.apache.commons.lang3.StringUtils.EMPTY));
        } catch (Exception e) {
            log.error("TaskId:{} 检测数据处理 - {}，数据保存异常:{}", taskId, DetectionTypeEnum.MANUAL.getName(), e.getMessage(), e);
            updateTaskInfoWhenException(task, DetectionTypeEnum.MANUAL, "数据包保存失败");
            sendAppletFailBroadcast(task, "微信小程序深度检测失败");
        }
    }

    @Override
    public void handleAppletManualFinish(TTask task, int dynamicType) {
        String lockKey = KEY_ANALYSIS_TASK_DATA_PREFIX + task.getTaskId() + ":" + dynamicType;
        if (!distributedLockService.tryLock(lockKey, TimeUnit.MINUTES.toMillis(10))) {
            log.info("TaskId:{} key={} 检测数据处理已经在解析中", task.getTaskId(), lockKey);
            return;
        }
        try {
            // 深度/快速检测
            if (task.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED) {
                log.info("任务已经完成");
                return;
            }
            TTask updateTask = new TTask();
            updateTask.setTaskId(task.getTaskId());
            updateTask.setDynamicAutoDataProcess();
            taskMapper.updateByPrimaryKeySelective(updateTask);
            // 释放设备
            userUseDeviceDAO.userReleaseDevice(task);
            Map<BehaviorStageEnum, DetectDataBO> detectDataBOMap = getManualDetectionData(task.getTaskId());
            // 手动截图数据关联
            insertManualScreenshotImageByTasId(task.getTaskId(), detectDataBOMap);
            // 合规风险检测
            analysisPrivacyCheck(task, detectDataBOMap, null);
            taskDAO.updateManualSuccess(task.getTaskId());
            // 更新任务状态后发送完成检测的消息，消息要在上传数据包之前发送，避免网络原因导致迟迟不发完成检测的消息
            sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_DYNAMIC_FINISH, "微信小程序深度检测完成", task);
        } catch (Throwable e) {
            log.error("TaskId:{} 检测数据处理 - {}，数据解析异常:{}", task.getTaskId(), DetectionTypeEnum.MANUAL.getName(), e.getMessage(), e);
            updateTaskInfoWhenException(task, DetectionTypeEnum.MANUAL, analysisErrorMsg(e));
            sendAppletFailBroadcast(task, "动态检测失败");
        } finally {
            distributedLockService.unlock(lockKey);
            removeTaskData(task);
        }
    }

    @Transactional
    @Override
    public void analysisStatic(TTask task, String data) {
        WechatAppletStaticDetectionResult result = CommonUtil.jsonToBean(data, WechatAppletStaticDetectionResult.class);

        List<TComplianceAppletPlugins> pluginsList;
        if (CollectionUtils.isEmpty(result.getPlugin())) {
            pluginsList = pluginsMapper.selectAllPluginsByTaskId(task.getTaskId());
        } else {
            List<TComplianceAppletPlugins> oldPluginsList = pluginsMapper.selectAllPluginsByTaskId(task.getTaskId());
            pluginsList = getPluginsByResult(result, task.getTaskId());
            pluginsList.stream()
                    .filter(plugins -> oldPluginsList.stream().noneMatch(p -> isSamePlugins(p, plugins)))
                    .forEach(plugins -> {
                        pluginsMapper.insert(plugins);
                        log.info("保存小程序引用插件成功");
                    });
            oldPluginsList.stream()
                    .filter(plugins -> pluginsList.stream().noneMatch(p -> isSamePlugins(p, plugins)))
                    .forEach(plugins -> {
                        pluginsMapper.deleteByPrimaryKey(plugins.getId());
                        log.info("删除小程序引用插件成功");
                    });
        }

        // 查询最新的资产信息后再进行更新
        TAssets assets = assetsMapper.getAssetByTaskId(task.getTaskId());
        taskDAO.updateStaticSuccess(task.getTaskId(),
                XxlDetectAnalysisHelper.wechatAppletDetectionResultFormat(assets, result),
                XxlDetectAnalysisHelper.wechatAppletInfoFormat(result, pluginsList));
    }

    public static List<TComplianceAppletPlugins> getPluginsByResult(WechatAppletStaticDetectionResult result, Long taskId) {
        if (Objects.isNull(result.getPlugin())) {
            return Collections.emptyList();
        }
        return result.getPlugin()
                .stream()
                .filter(Objects::nonNull)
                .map(p -> {
                    TComplianceAppletPlugins plugins = new TComplianceAppletPlugins();
                    plugins.setTaskId(taskId);
                    plugins.setPluginAppid(p.getAppid());
                    plugins.setPluginName(p.getPluginName());
                    plugins.setServiceCategory(p.getServiceCategory());
                    plugins.setDeveloper(p.getDeveloper());
                    return plugins;
                }).collect(Collectors.toList());
    }

    @Override
    protected void analysisManualInternal(TTask task, String dataPath, String fastDfsDataPath) {
        String lockKey = KEY_ANALYSIS_TASK_DATA_PREFIX + task.getTaskId();
        if (!distributedLockService.tryLock(lockKey, TimeUnit.MINUTES.toMillis(10))) {
            log.info("TaskId:{} key={} 检测数据处理已经在解析中", task.getTaskId(), lockKey);
            return;
        }
        String dynamicPath = commonProperties.getProperty("detection.tools.dynamic_path");
        String uncompress = null;
        try {
            uncompress = CommonUtil.uncompress(dataPath, dynamicPath + UuidUtil.uuid());
            TTask updateTask = new TTask();
            updateTask.setTaskId(task.getTaskId());
            updateTask.setDynamicAutoDataProcess();
            updateTask.setDataPath(StringUtils.isEmpty(fastDfsDataPath) ? uploadDataFileToFastDfs(dataPath) : fastDfsDataPath);
            taskMapper.updateByPrimaryKeySelective(updateTask);
            // 释放设备
            userUseDeviceDAO.userReleaseDevice(task);
            // 整合数据
            TaskDetailVO taskDetailVo = findById(task.getApkDetectionDetailId());
            taskDetailVo.setTaskId(task.getTaskId());
            Map<BehaviorStageEnum, DetectDataBO> detectDataMap = analysisManual(taskDetailVo, uncompress);
            // 合规风险检测
            analysisPrivacyCheck(task, detectDataMap, null);
            // 微信小程序额外的信息
            savePluginsAndBasicInfo(task, uncompress);
            // 解析完成更新检测信息
            updateTaskInfoWhenAnalyzeFinish(task, DetectionTypeEnum.MANUAL);
            sendAppletSuccessBroadcast(task, "微信小程序深度检测完成");
            dynamicTaskDataService.removeTaskContext(task);
        } catch (Exception e) {
            log.error("TaskId:{} 检测数据处理 - {}，数据解析异常:{}", task.getTaskId(), DetectionTypeEnum.MANUAL.getName(), e.getMessage(), e);
            updateTaskInfoWhenException(task, DetectionTypeEnum.MANUAL, analysisErrorMsg(e));
            sendAppletFailBroadcast(task, "微信小程序深度检测失败");
        } finally {
            distributedLockService.unlock(lockKey);
            CommonUtil.deleteFile(uncompress);
            taskSortThread.checkTaskSortListAsSoonASPossible();
        }
    }


    protected void analysisPrivacyCheck(TTask task, Map<BehaviorStageEnum, DetectDataBO> detectDataMap, CommonDetectInfo commonDetectInfo) {
        TaskDetailVO taskDetail = findById(task.getApkDetectionDetailId());
        // 数据更新，需要taskId
        taskDetail.setTaskId(task.getTaskId());
        // 清除历史记录
        privacyPolicyResultMapper.deleteByTaskId(taskDetail.getTaskId());

        Map<String, TPrivacyPolicyItem> itemMap = new HashMap<>();
        for (TPrivacyPolicyItem privacyPolicyItem : privacyPolicyItemMapper.findByTerminalType(TerminalTypeEnum.WECHAT_APPLET.getValue())) {
            itemMap.put(privacyPolicyItem.getItem_no(), privacyPolicyItem);
        }

        List<TPrivacyPolicyResult> policyResultList = new ArrayList<>();

        PrivacyPolicyCheck isTransferResult = new PrivacyPolicyCheck();
        PrivacyPolicyCheck isOutSide = new PrivacyPolicyCheck();
        PrivacyPolicyCheck isApp = new PrivacyPolicyCheck();
        PrivacyPolicyCheck isCookie = new PrivacyPolicyCheck();

        for (Map.Entry<BehaviorStageEnum, DetectDataBO> entry : detectDataMap.entrySet()) {
            DetectDataBO detectData = entry.getValue();
            List<TPrivacySensitiveWord> sensitiveWords = detectData.getPrivacySensitiveWords();
            if (SensitiveUtils.havePlaintextTransmission(sensitiveWords)) {
                isTransferResult.setNonCompliance(true);
                isTransferResult.getBehaviorStageEnums().add(entry.getKey());
            }

            List<TPrivacyOutsideAddress> outsideAddresses = detectData.getPrivacyOutsideAddresses();
            if (CollectionUtils.isNotEmpty(outsideAddresses)) {
                for (TPrivacyOutsideAddress outsideAddress : outsideAddresses) {
                    if (outsideAddress.getOutside() == PrivacyStatusEnum.YES.getValue()) {
                        isOutSide.setNonCompliance(true);
                        isOutSide.getBehaviorStageEnums().add(entry.getKey());
                        break;
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(outsideAddresses)) {
                for (TPrivacyOutsideAddress outsideAddress : outsideAddresses) {
                    if (CookieMarkEnum.HAVE.getValue().equals(outsideAddress.getCookieMark())) {
                        isCookie.setNonCompliance(true);
                        isCookie.getBehaviorStageEnums().add(entry.getKey());
                    }
                }
            }
            if (entry.getKey() == BehaviorStageEnum.BEHAVIOR_GRANT) {
                List<TPrivacyActionNougat> actionNougats = detectData.getPrivacyActionNougats();
                for (TPrivacyActionNougat actionNougat : actionNougats) {
                    if (ExecutorTypeEnum.APP.getValue().equals(actionNougat.getExecutorType())
                            && org.apache.commons.lang3.StringUtils.equals(actionNougat.getIsPersonal(), String.valueOf(PrivacyStatusEnum.YES.getValue()))) {
                        isApp.setNonCompliance(true);
                        isApp.getBehaviorStageEnums().add(entry.getKey());
                    }
                }
            }
        }
        // 通信明文传输-传输个人信息
        TPrivacyPolicyResult transferResult = new TPrivacyPolicyResult();
        transferResult.setTaskId(taskDetail.getTaskId());
        transferResult.setPolicyItemId(itemMap.get(PrivacyPolicyItemNoEnum.CLEAR_TEXT_DATA.itemNo).getId());
        transferResult.setCategory(PrivacyPolicyResultCategoryEnum.NORMAL.value);
        if (isTransferResult.isNonCompliance()) {
            transferResult.setStatus(PrivacyPolicyResultStatusEnum.RISK.status);
            transferResult.setBehaviorStage(isTransferResult.getNonComplianceBehaviorStage());
        } else {
            transferResult.setStatus(PrivacyPolicyResultStatusEnum.SAFE.status);
        }
        policyResultList.add(transferResult);

        // 境外通信访问
        TPrivacyPolicyResult outSideResult = new TPrivacyPolicyResult();
        outSideResult.setTaskId(taskDetail.getTaskId());
        outSideResult.setPolicyItemId(itemMap.get(PrivacyPolicyItemNoEnum.OUTSIDE_ADDRESS_DATA.itemNo).getId());
        outSideResult.setCategory(PrivacyPolicyResultCategoryEnum.NORMAL.value);
        if (isOutSide.isNonCompliance()) {
            outSideResult.setStatus(PrivacyPolicyResultStatusEnum.RISK.status);
            outSideResult.setBehaviorStage(isOutSide.getNonComplianceBehaviorStage());
        } else {
            outSideResult.setStatus(PrivacyPolicyResultStatusEnum.SAFE.status);
        }
        policyResultList.add(outSideResult);

        // 使用cookie及其同类技术
        TPrivacyPolicyResult cookieResult = new TPrivacyPolicyResult();
        cookieResult.setTaskId(taskDetail.getTaskId());
        cookieResult.setPolicyItemId(itemMap.get(PrivacyPolicyItemNoEnum.COOKIE.itemNo).getId());
        cookieResult.setCategory(PrivacyPolicyResultCategoryEnum.NORMAL.value);
        if (isCookie.isNonCompliance()) {
            cookieResult.setStatus(PrivacyPolicyResultStatusEnum.RISK.status);
            cookieResult.setBehaviorStage(isCookie.getNonComplianceBehaviorStage());
        } else {
            cookieResult.setStatus(PrivacyPolicyResultStatusEnum.SAFE.status);
        }
        policyResultList.add(cookieResult);

        // 快速检测才检测
        if (TaskDetectionTypeEnum.isAuto(task.getDetectionType()) && commonDetectInfo != null) {
            //个人信息保护政策检测
            policyResultList.add(buildPrivacyPolicyDetailResult(taskDetail, commonDetectInfo, itemMap));

            //私自收集使用个人信息
            TPrivacyPolicyResult appPersonalInfoResult = new TPrivacyPolicyResult();
            appPersonalInfoResult.setTaskId(taskDetail.getTaskId());
            appPersonalInfoResult.setPolicyItemId(itemMap.get(PrivacyPolicyItemNoEnum.APP_COLLECT_PRIVACY.itemNo).getId());
            appPersonalInfoResult.setCategory(PrivacyPolicyResultCategoryEnum.NORMAL.value);
            if (isApp.isNonCompliance()) {
                appPersonalInfoResult.setStatus(PrivacyPolicyResultStatusEnum.RISK.status);
                appPersonalInfoResult.setBehaviorStage(isApp.getNonComplianceBehaviorStage());
            } else {
                appPersonalInfoResult.setStatus(PrivacyPolicyResultStatusEnum.SAFE.status);
            }
            policyResultList.add(appPersonalInfoResult);
        }
        privacyPolicyResultMapper.insertList(policyResultList);
    }

    /**
     * 分析解压后的文件
     *
     * @param taskDetailVo
     * @param uncompress
     * @return
     */
    private Map<BehaviorStageEnum, DetectDataBO> analysisManual(TaskDetailVO taskDetailVo, String uncompress) {
        Map<BehaviorStageEnum, DetectDataBO> detectDataMap = new HashMap<>(8);
        for (Map.Entry<BehaviorStageEnum, String> entry : ANALYZE_FILE_MAP.entrySet()) {
            try {
                BehaviorStageEnum behaviorStage = entry.getKey();
                detectDataMap.put(entry.getKey(), analyzeAction(behaviorStage, taskDetailVo, uncompress));
            } catch (Exception e) {
                log.error("数据解析失败", e);
            }
        }
        return detectDataMap;
    }

    /**
     * 分析检测得到的数据
     *
     * @param behaviorStage
     * @param taskDetailVO
     * @return
     */
    private DetectDataBO analyzeAction(BehaviorStageEnum behaviorStage, TaskDetailVO taskDetailVO, String uncompress) {
        log.info("检测数据解析，任务ID：[{}]，资产名称：[{}]，解析阶段：[{}]", taskDetailVO.getTaskId(), taskDetailVO.getApk_name(), behaviorStage.getName());
        DetectDataBO data = new DetectDataBO();
        List<TPrivacyActionNougat> nougatList = privacyActionNougatMapper.findByTaskId(taskDetailVO.getTaskId(),
                Collections.emptyList(), Collections.emptyList(), Collections.singletonList(behaviorStage));
        // 按照每秒钟统计数据相同actionId的数据，并更新
        setNougatsCycleTrigger(nougatList, TerminalTypeEnum.WECHAT_APPLET);
        nougatList.stream()
                .filter(nougat -> Objects.nonNull(nougat.getNumberAction()) || Objects.nonNull(nougat.getTriggerCycleTime()))
                .forEach(nougat -> privacyActionNougatMapper.updateCycleTrigger(nougat.getId(), nougat.getNumberAction(), nougat.getTriggerCycleTime()));
        data.setPrivacyActionNougats(nougatList);
        data.setPrivacyOutsideAddresses(privacyOutsideAddressMapper.findByTaskIdAndOutsideStackInfo(taskDetailVO.getTaskId(),
                org.apache.commons.lang3.StringUtils.EMPTY, org.apache.commons.lang3.StringUtils.EMPTY, behaviorStage.getValue()));
        data.setPrivacySensitiveWords(privacySensitiveWordMapper.findByTaskId(taskDetailVO.getTaskId(), behaviorStage.getValue()));
        data.setPrivacySharedPrefs(privacySharedPrefsMapper.findByTaskId(taskDetailVO.getTaskId(), behaviorStage.getValue()));
        setScreenshotImage(taskDetailVO.getTaskId(), data, uncompress, TerminalTypeEnum.getAndValid(taskDetailVO.getTerminal_type()));
        return data;
    }

    /**
     * 分析解压后的文件
     *
     * @param taskDetailVo
     * @param uncompress
     * @return
     */
    private Map<BehaviorStageEnum, DetectDataBO> analysisUncompress(TaskDetailVO taskDetailVo, String uncompress) {
        List<TActionNougat> actionNougatList = actionNougatMapper.findByTerminalType(TerminalTypeEnum.WECHAT_APPLET.getValue());
        List<TSensitiveWord> sensitiveWordList = tSensitiveWordMapper.findByTerminalType(TerminalTypeEnum.WECHAT_APPLET.getValue());
        Map<BehaviorStageEnum, DetectDataBO> detectDataMap = new HashMap<>(8);
        String appletVersion = StringUtils.EMPTY;
        String appletLogoUrl = StringUtils.EMPTY;
        for (Map.Entry<BehaviorStageEnum, String> entry : ANALYZE_FILE_MAP.entrySet()) {
            try {
                BehaviorStageEnum behaviorStage = entry.getKey();
                String fileNames = entry.getValue();
                DetectDataBO detectData = analyzeZipAction(behaviorStage, fileNames, uncompress, taskDetailVo,
                        actionNougatList, sensitiveWordList);
                if (Objects.nonNull(detectData)) {
                    detectDataMap.put(behaviorStage, detectData);
                }
                if (StringUtils.isNotBlank(detectData.getAppletVersion())) {
                    appletVersion = detectData.getAppletVersion();
                }
                if (StringUtils.isNotBlank(detectData.getAppletLogoUrl())) {
                    appletLogoUrl = detectData.getAppletLogoUrl();
                }
            } catch (Exception e) {
                e.getMessage();
            }
        }
        //更新版本号、logo
        updateAppletAssets(taskDetailVo.getAssets_id(), taskDetailVo.getId(), appletVersion, appletLogoUrl);
        return detectDataMap;
    }

    public void updateAppletAssets(Long assetsId, String documentId, String version, String logoUrl) {
        TAssets tAssets = assetsMapper.selectByPrimaryKey(assetsId);
        TAssets updateAssets = new TAssets();
        updateAssets.setId(assetsId);
        boolean isUpdate = false;
        // 资产数据如果没有版本号，需要更新
        if (!StringUtils.equals(tAssets.getVersion(), version) && StringUtils.isNotBlank(version)) {
            updateAssets.setVersion(version);
            isUpdate = true;
        }
        if (StringUtils.isBlank(updateAssets.getLogo()) && StringUtils.isNotBlank(logoUrl)) {
            String bucket = File.separator + "default" + File.separator + "images" + FileUtil.SEPARATOR;
            File diskFile = new File(getLogoDir(bucket), UuidUtil.uuid() + ConstantsUtils.SUFFIX_PNG);
            try {
                HttpUtils.download(logoUrl, diskFile.getAbsolutePath(), null);
                FileVO logoFile = FileVOUtils.convertFileVOByFile(diskFile);
                String imageUploadMethod = commonProperties.getProperty("image.upload.method");
                if (StringUtils.equals(imageUploadMethod, "fdfs")) {
                    singleFastDfsFileService.instance().upload(logoFile);
                    updateAssets.setLogo(commonProperties.getProperty("detection.result.url.prefix") + logoFile.getFileUrl());
                } else {
                    updateAssets.setLogo(FileUtil.encodeData(bucket + diskFile.getName()));
                    assetsService.saveApkLogo(new FileVO(), updateAssets);
                }
                isUpdate = true;
            } catch (Exception e) {
                log.error("微信小程序logo下载错误", e);
            }
        }
        if (isUpdate) {
            assetsMapper.updateByPrimaryKeySelective(updateAssets);
            // 更新当前任务记录的版本号
            updateTaskVersion(documentId, updateAssets.getVersion(), updateAssets.getLogo());
        }
    }


    private File getLogoDir(String bucket) {
        String bucketImages = commonProperties.getFilePath() + bucket;
        File file = new File(bucketImages);
        if (!file.exists()) {
            file.mkdirs();
        }
        return file;
    }

    /**
     * 分析检测得到的数据
     *
     * @param behaviorStage
     * @param fileNames
     * @param uncompress
     * @param taskDetailVO
     * @param sensitiveWords
     * @return
     */
    private DetectDataBO analyzeZipAction(BehaviorStageEnum behaviorStage, String fileNames, String uncompress, TaskDetailVO taskDetailVO,
                                          List<TActionNougat> actionNougatList,
                                          List<TSensitiveWord> sensitiveWords) {
        log.info("检测数据解析，任务ID：[{}]，资产名称：[{}]，解析阶段：[{}]", taskDetailVO.getTaskId(), taskDetailVO.getApk_name(), behaviorStage.getName());
        String[] fileNamePaths = fileNames.split(",");
        // 分析堆栈数据
        String behaviorFilePath = uncompress + File.separator + fileNamePaths[0];
        return appletBehaviorInfoAction.analyzeBehaviorInfo(behaviorFilePath,
                taskDetailVO, behaviorStage, actionNougatList, sensitiveWords);
    }

    @Override
    public void callBackStaticDetection(AppletStaticDetectionInfo appletQuery) throws IjiamiCommandException {
        TTask task = taskMapper.selectByPrimaryKey(appletQuery.getTaskId());
        if (task == null) {
            throw new IjiamiCommandException("请求的任务不存在！");
        }
        TAssets assets = assetsMapper.selectByPrimaryKey(task.getAssetsId());
        if (assets == null) {
            throw new IjiamiCommandException("该任务找不到对应的资产信息");
        }
        if (task.getTaskTatus() != DetectionStatusEnum.DETECTION_IN){
            return;
        }
        if ("examiningFail".equals(appletQuery.getPermissions())){
            log.info("微信小程序静态检测失败");
            taskDAO.updateStaticFailure(task, "静态检测失败，请检查AppId是否正确");
            sendAppletFailBroadcast(task, "微信小程序静态检测失败", 5000);
            return;
        }
        //接收静态检测进度提醒
        if (task.getTaskId().toString().equals(appletQuery.getPermissions())){
            log.info("收到微信小程序静态检测进度提醒!");
            int progress = 50;
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("_id", task.getApkDetectionDetailId());
            Update update = new Update();
            update.set("progress", progress);
            update(paramMap, update);
            sendStaticTaskProgressMessage(progress, task, 5000);
            return;
        }
        updatePermission(appletQuery.getPermissions(), assets, task);
        sendAppletSuccessBroadcast(task, "微信小程序静态检测完成");
    }

    @Override
    public Long findByDeviceHardwareSerial(String deviceSerial) {
        TTask task = taskMapper.findByDeviceHardwareSerial(deviceSerial);
        if (Objects.isNull(task)) {
            return null;
        }
        return task.getTaskId();
    }

    /**
     * 发送失败广播
     *
     * @param task
     */
    private void sendAppletFailBroadcast(TTask task, String msg, long delayMillis) {
        sendMessageService.sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_DYNAMIC_ERROR, msg, task, delayMillis);
    }

    /**
     * 发送失败广播
     *
     * @param task
     */
    private void sendAppletFailBroadcast(TTask task, String msg) {
        sendMessageService.sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_DYNAMIC_ERROR, msg, task);
    }

    /**
     * 发送成功广播
     *
     * @param task
     */
    private void sendAppletSuccessBroadcast(TTask task, String msg) {
        sendMessageService.sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_DYNAMIC_FINISH, msg, task);
    }

    public void updatePermission(String permission, TAssets assets, TTask task) {
        if (StringUtils.isEmpty(permission)) {
            return;
        }
        log.info("更新微信小程序静态权限！");
        String createTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(System.currentTimeMillis());
        List<DetectionItem<? extends ResultContent>> detectionItems = new ArrayList<>();
        detectionItems.add(buildAppletBaseInfoItem(assets.getName(), assets.getAppId(), assets.getVersion(), createTime));
        detectionItems.add(buildAppletPermissionItem(matchPermission(permission), createTime));
        taskDAO.updateStaticSuccess(task.getTaskId(), detectionItems);
    }

    /**
     * 根据返回的api值转换成需要存储的权限值
     * @param permissions
     * @return
     */
    private List<String> matchPermission(String permissions) {
        List<String> apiList = Arrays.asList(permissions.replaceAll("[\"'\\[\\]]", "").split(","));
        return appletScopeApiMapper.findInApi(apiList);
    }

    private CommonDetectInfo analysisMiitDetectResult(TTask task, String uncompress, Map<BehaviorStageEnum, DetectDataBO> detectDataMap) {
        // 深度检测直接返回
        if (!TaskDetectionTypeEnum.isAuto(task.getDetectionType())) {
            return null;
        }
        privacyLawsDetailMapper.deleteByTaskId(task.getTaskId());
        privacyLawsResultMapper.deleteByTaskId(task.getTaskId());
        appLawsDectDetailMapper.deleteByTaskId(task.getTaskId());
        appLawsRiskCollectMapper.deleteByTaskId(task.getTaskId());
        sdkDectDetailMapper.deleteByTaskId(task.getTaskId());
        sdkLawsDectDetailMapper.deleteByTaskId(task.getTaskId());
        privacyLawsConclusionActionMapper.deleteByTaskId(task.getTaskId());
        List<SdkVO> sdkVOList = getSdkList(task);
        CommonDetectInfo commonDetectInfo = buildMiniProgramDetectInfo(task, uncompress, detectDataMap);
        
        //查询检测项
        TTaskExtendVO extend = taskExtendMapper.findTaskByTaskId(task.getTaskId());
        for (PrivacyLawId lawId: getDetectionLawIds(extend, task.getTerminalType())) {
            analysisLaw(task, sdkVOList, commonDetectInfo, Collections.emptyList(), lawId);
        }
        saveSdkDectDetail(task, sdkVOList);
        return commonDetectInfo;
    }

    private CommonDetectInfo buildMiniProgramDetectInfo(TTask task, String uncompress, Map<BehaviorStageEnum, DetectDataBO> detectDataMap) {
        log.info("TaskId:{} buildMiniProgramDetectInfo uncompress={}", task.getTaskId(), uncompress);

        // 组装公共参数
        CommonDetectInfo commonDetectInfo = buildCommonDetectInfo(task, uncompress, detectDataMap);

        // 从uncomperss中解析出的数据
        List<ResultDataLogBO> resultDataLogBOList = miniProgramResultDataLogParser.parser(uncompress + File.separator + "resultDataLog", commonDetectInfo.getApkName());
        for (ResultDataLogBO logBo:resultDataLogBOList) {
            logBo.setTerminalType(TerminalTypeEnum.WECHAT_APPLET);
        }
        commonDetectInfo.setResultDataLogs(resultDataLogBOList);
        List<ResultDataLogBO> privacyResults = new ArrayList<>();
        for (ResultDataLogBO resultDataLogBO : resultDataLogBOList) {
            if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_PRIVACY_INTERFACE.getValue()) {
                privacyResults.add(resultDataLogBO);
            }
        }
        List<PrivacyPolicyTextInfo> subPagePrivacyDetailList = getSubPagePrivacyDetailByHtmlDir(uncompress + File.separator + "privacyhtml");
        subPagePrivacyDetailList.addAll(getPdfTextByPdfDir(uncompress + File.separator + "privacypdf"));
        TAssets assets = assetsMapper.getAssetByTaskId(task.getTaskId());
        // 先解析清单
        if (StringUtils.isNotBlank(assets.getThirdPartyShareListPath())) {
            analysisCheckListByUpload(assets, commonDetectInfo);
        } else {
            analysisCheckListByUi(commonDetectInfo, privacyResults, subPagePrivacyDetailList);
        }
        if (StringUtils.isNotBlank(assets.getPrivacyPolicyPath())) {
            // 用户有上传隐私文件
            analysisUserUploadPrivacyPolicy(assets, commonDetectInfo);
        } else if (privacyResults.isEmpty() && subPagePrivacyDetailList.isEmpty()) {
            commonDetectInfo.setHasPrivacyPolicy(false);
            commonDetectInfo.setPrivacyPolicyImg(null);
            commonDetectInfo.setPrivacyPolicyContent(null);
        } else {
            analysisDetectPrivacyPolicy(task, commonDetectInfo, privacyResults, subPagePrivacyDetailList);
        }
        // 没有单独的第三方SDK清单，尝试从隐私政策中解析
        analysisCheckListByPolicyContent(commonDetectInfo);
        return commonDetectInfo;
    }

    private void analysisDetectPrivacyPolicy(TTask task,
                                             CommonDetectInfo commonDetectInfo,
                                             List<ResultDataLogBO> privacyResults,
                                             List<PrivacyPolicyTextInfo> subPagePrivacyDetailList) {
        commonDetectInfo.setHasPrivacyPolicy(true);
        // 隐私政策详情文件
        List<ResultDataLogBO> privacyDetailResults = new ArrayList<>();
        for (ResultDataLogBO privacyResult : privacyResults) {
            if (privacyResult.getUiDumpResult() != null && privacyResult.getUiDumpResult().getUiType() == MiitUITypeEnum.POLICY_DETAIL.getValue()) {
                privacyDetailResults.add(privacyResult);
            }
        }
        // 没详情文件 取所有的
        if (privacyDetailResults.size() == 0) {
            privacyDetailResults.addAll(privacyResults);
        }
        // 隐私文本拼接 取最长文本图片
        String privacyImagePath = null;
        String privacyContent = null;
        int maxLength = 0;
        StringBuilder privacyContentBuilder = new StringBuilder();
        for (ResultDataLogBO privacyResult : privacyDetailResults) {
            if (privacyResult.getUiDumpResult() != null && org.apache.commons.lang3.StringUtils.isNotBlank(privacyResult.getUiDumpResult().getFullText())) {
                String[] uiList = privacyResult.getUiDumpResult().getFullText().split("\n");
                for (String string : uiList) {
                    if (org.apache.commons.lang3.StringUtils.isNoneBlank(string) && string.startsWith("。")) {
                        string = string.replaceFirst("。", "");
                    }
                    if (!privacyContentBuilder.toString().contains(string)) {
                        privacyContentBuilder.append(string).append("\n");
                    }
                }
                int length = privacyResult.getUiDumpResult().getFullText().length();
                if (length > maxLength) {
                    maxLength = length;
                    privacyImagePath = privacyResult.getImgPath();
                    privacyContent = privacyContentBuilder.toString();
                }
            }
        }
        if (StringUtils.isBlank(privacyImagePath) && subPagePrivacyDetailList.size() > 0) {
            privacyContent = subPagePrivacyDetailList.get(0).content;
        }
        commonDetectInfo.setPrivacyPolicyContent(mergePrivacyPolicySubPage(privacyContent, subPagePrivacyDetailList));
        uploadPrivacyPolicyImage(task, commonDetectInfo, privacyImagePath);
        setPrivacyPolicyNlp(commonDetectInfo);
    }

//    private void analysisLaw(TTask task, List<SdkVO> sdkVOList, CommonDetectInfo commonDetectInfo, PrivacyLawId lawId) {
//        // 查询检测项及检测关键词
//        List<TPrivacyLawsBasis> regulations = privacyLawsBasisMapper.selectItemNoInfoByLawId(lawId.id);
//        List<String> itemNos = regulations.stream().map(TPrivacyLawsBasis::getItemNo).distinct().collect(Collectors.toList());
//        Map<String, List<TPrivacyLawsBasis>> itemNoWithActionAndKeys = regulations.stream().collect(Collectors.groupingBy(TPrivacyLawsBasis::getItemNo));
//        List<DetectResult> detectResults = new ArrayList<>();
//        itemNos.forEach(itemNo -> {
//            CustomDetectInfo customDetectInfo = makeCustomDetectInfo(itemNo, itemNoWithActionAndKeys);
//            // 设置回调
//            IDetectCallback callback = new DefaultDetectCallback();
//            AbstractDetectPoint instance = detectPointManager.getInstance(itemNo);
//            instance.startDetect(commonDetectInfo, customDetectInfo, callback);
//            detectResults.add(callback.getResult());
//        });
//        //保存164号文结果
//        saveLawDetectData(task, sdkVOList, detectResults,
//                itemNoWithActionAndKeys, Collections.emptyList(), lawId);
//    }


    protected Map<BehaviorStageEnum, DetectDataBO> getManualDetectionData(Long taskId) {
        Map<BehaviorStageEnum, DetectDataBO> detectDataMap = new HashMap<>();
        DetectDataBO detectDataBO = new DetectDataBO();

        Example queryActionNougat = new Example(TPrivacyActionNougat.class);
        queryActionNougat.createCriteria().andEqualTo("taskId", taskId);
        List<TPrivacyActionNougat> nougatList = privacyActionNougatMapper.selectByExample(queryActionNougat);
        detectDataBO.setPrivacyActionNougats(nougatList);

        Example queryOutsideAddress = new Example(TPrivacyOutsideAddress.class);
        queryOutsideAddress.createCriteria().andEqualTo("taskId", taskId);
        List<TPrivacyOutsideAddress> privacyOutsideAddresses = privacyOutsideAddressMapper.selectByExample(queryOutsideAddress);
        detectDataBO.setPrivacyOutsideAddresses(privacyOutsideAddresses);

        Example querySensitiveWord = new Example(TPrivacySensitiveWord.class);
        querySensitiveWord.createCriteria().andEqualTo("taskId", taskId);
        List<TPrivacySensitiveWord> sensitiveInserts = privacySensitiveWordMapper.selectByExample(querySensitiveWord);
        detectDataBO.setPrivacySensitiveWords(sensitiveInserts);

        Example querySharedPrefs = new Example(TPrivacySharedPrefs.class);
        querySharedPrefs.createCriteria().andEqualTo("taskId", taskId);
        List<TPrivacySharedPrefs> shareInserts = privacySharedPrefsMapper.selectByExample(querySharedPrefs);
        detectDataBO.setPrivacySharedPrefs(shareInserts);

        detectDataMap.put(BehaviorStageEnum.BEHAVIOR_FRONT, detectDataBO);
        return detectDataMap;
    }


//    private void saveScreenshotImageData(Long taskId, JSONObject cmdData) {
//        String imgDetail = cmdData.optString(CMD_DATA_ANDROID_IMG);
//        if (org.apache.commons.lang3.StringUtils.isNotBlank(imgDetail)) {
//            TManualScreenshotImage image = getScreenshotImageByTaskId(taskId);
//            if (image == null) {
//                image = new TManualScreenshotImage();
//                image.setTaskId(taskId);
//                image.setImageData(imgDetail);
//                image.setCreateTime(new Date());
//                try {
//                    manualScreenshotImageMapper.insert(image);
//                } catch (org.springframework.dao.DuplicateKeyException e) {
//                    // 回传速度过快导致唯一索引冲突，不用管
//                    log.info("重复接受截图信息 {}", e.getMessage());
//                }
//                log.info("写入截图数据 taskId={}", taskId);
//            } else if (!imgDetail.equals(image.getImageData()) && strLength(imgDetail) > strLength(image.getImageData())) {
//                log.info("更新截图数据 taskId={} new={} old={}", taskId, imgDetail, image.getImageData());
//                image.setImageData(imgDetail);
//                image.setCreateTime(new Date());
//                manualScreenshotImageMapper.updateByPrimaryKeySelective(image);
//            }
//        } else {
//            log.info("无截图数据 taskId={} imgDetail={}", taskId, imgDetail);
//        }
//    }

    public void insertManualScreenshotImageByTasId(Long taskId, Map<BehaviorStageEnum, DetectDataBO> detectDataMap) {
        screenshotImageService.deleteByTaskId(taskId);
        Map<Long, TActionNougat> actionNougatMap = actionNougatMapper.findByTerminalType(TerminalTypeEnum.WECHAT_APPLET.getValue())
                .stream()
                .collect(Collectors.toMap(TActionNougat::getActionId, Function.identity(), (entity1, entity2) -> entity1));
        for (Map.Entry<BehaviorStageEnum, DetectDataBO> entry : detectDataMap.entrySet()) {
            // 计算行为触发次数（秒/次）
            setNougatsCycleTrigger(entry.getValue().getPrivacyActionNougats(), TerminalTypeEnum.WECHAT_APPLET);
            entry.getValue().getPrivacyActionNougats().stream()
                    .filter(action -> Objects.nonNull(action.getNumberAction()) && Objects.nonNull(action.getTriggerCycleTime()))
                    .forEach(action -> {
                        privacyActionNougatMapper.updateCycleTrigger(action.getId(), action.getNumberAction(), action.getTriggerCycleTime());
                    });
            //根据任务id获取到手动截图信息
            List<ScreenshotImage> screenshotImages = screenshotImageService.getManualScreenshotImage(taskId);
            screenshotImageService.saveManualBehaviorImg(taskId, entry.getValue().getPrivacyActionNougats(), screenshotImages, actionNougatMap);
            // 通讯行为数据
            screenshotImageService.saveManualBehaviorImgOutside(taskId, entry.getValue().getPrivacyOutsideAddresses(), screenshotImages);
            // 传输个人信息
            screenshotImageService.saveManualBehaviorImgSensitiveWord(taskId, entry.getValue().getPrivacySensitiveWords(), screenshotImages);
            // 储存个人信息
            screenshotImageService.saveManualBehaviorImgShared(taskId, entry.getValue().getPrivacySharedPrefs(), screenshotImages);
        }
    }

//    private TManualScreenshotImage getScreenshotImageByTaskId(Long taskId) {
//        TManualScreenshotImage query = new TManualScreenshotImage();
//        query.setTaskId(taskId);
//        return manualScreenshotImageMapper.selectOne(query);
//    }

    /**
     * 广播任务状态，通知页面更新任务状态
     *
     * @param typeEnum
     * @param describe
     * @param task
     */
    private void sendTaskStatusBroadcast(BroadcastMessageTypeEnum typeEnum, String describe, TTask task) {
        sendMessageService.sendTaskStatusBroadcast(typeEnum, describe, task);
    }

    private void sendStaticTaskProgressMessage(int progress, TTask task, long delayMillis) {
        sendMessageService.sendTaskProgressBroadcast(BroadcastMessageTypeEnum.DETECTION_RUNNING, progress, task, delayMillis);
    }

//    private int strLength(String str) {
//        return str == null ? 0 : str.length();
//    }

    private void removeTaskData(TTask task) {
        dynamicTaskDataService.removeTaskContext(task);
    }

    @Override
    public void analysisLaw(TTask task, int dynamicType) {
        String lockKey = KEY_ANALYSIS_TASK_DATA_PREFIX + task.getTaskId() + ":" + dynamicType;
        if (!distributedLockService.tryLock(lockKey, TimeUnit.MINUTES.toMillis(10))) {
            log.info("TaskId:{} key={} 检测数据处理已经在解析中", task.getTaskId(), lockKey);
            return;
        }
        try {
            taskDAO.updateLawSuccess(task.getTaskId());
            sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_LAW_FINISH, "微信小程序法规检测完成", task);
        } catch (Throwable e) {
            log.error("TaskId:{} 检测数据处理 - {}，数据解析异常:{}", task.getTaskId(), DetectionTypeEnum.LAW.getName(), e.getMessage(), e);
            updateTaskInfoWhenException(task, DetectionTypeEnum.LAW, analysisErrorMsg(e));
            sendAppletFailBroadcast(task, "动态检测失败");
        } finally {
            distributedLockService.unlock(lockKey);
            removeTaskData(task);
        }
    }
}
