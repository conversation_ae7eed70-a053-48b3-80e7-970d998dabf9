package cn.ijiami.detection.service.impl.compliance;

import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import cn.ijiami.detection.DTO.compliance.ApkChannelDto;
import cn.ijiami.detection.DTO.compliance.ComplianceCategoryItemDto;
import cn.ijiami.detection.DTO.compliance.ComplianceConclusionDto;
import cn.ijiami.detection.DTO.compliance.ComplianceItemResultDto;
import cn.ijiami.detection.DTO.compliance.ComplianceReportCategoryDto;
import cn.ijiami.detection.VO.PermissionVO;
import cn.ijiami.detection.VO.detection.BaseMessageVO;
import cn.ijiami.detection.VO.detection.SdkVO;
import cn.ijiami.detection.config.IjiamiCommonProperties;
import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import cn.ijiami.detection.entity.TPrivacySharedPrefs;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.TaskDetectionTypeEnum;
import cn.ijiami.detection.server.client.base.enums.TerminalTypeEnum;
import cn.ijiami.detection.mapper.TPrivacySensitiveWordMapper;
import cn.ijiami.detection.mapper.TPrivacySharedPrefsMapper;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.mapper.compliance.ComplianceAssessCategoryMapper;
import cn.ijiami.detection.mapper.compliance.ComplianceAssessItemMapper;
import cn.ijiami.detection.mapper.compliance.ComplianceAssessPointMapper;
import cn.ijiami.detection.mapper.compliance.ComplianceDetectAssessRelMapper;
import cn.ijiami.detection.mapper.compliance.ComplianceItemPointMapper;
import cn.ijiami.detection.mapper.compliance.ComplianceItemPointResultMapper;
import cn.ijiami.detection.mapper.compliance.CompliancePersonalInfoMapper;
import cn.ijiami.detection.mapper.compliance.ComplianceSdkMapper;
import cn.ijiami.detection.service.api.IChannelMonitoringService;
import cn.ijiami.detection.service.api.ITaskService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service("iosComplianceDownloadReport")
public class IosComplianceDownloadReport extends AbstractComplianceReport {

    @Resource
    private ITaskService taskService;
    @Resource
    private TTaskMapper taskMapper;
    @Resource
    private TPrivacySensitiveWordMapper privacySensitiveWordMapper;
    @Resource
    private TPrivacySharedPrefsMapper privacySharedPrefsMapper;
    @Resource
    private IChannelMonitoringService channelMonitoringService;

    public IosComplianceDownloadReport(ComplianceAssessItemMapper complianceAssessItemMapper, ComplianceAssessPointMapper complianceAssessPointMapper,
                                       ComplianceItemPointResultMapper complianceItemPointResultMapper, ComplianceDetectAssessRelMapper complianceDetectAssessRelMapper,
                                       ComplianceAssessCategoryMapper complianceAssessCategoryMapper,
                                       ComplianceSdkMapper complianceSdkMapper, ComplianceItemPointMapper complianceItemPointMapper,
                                       CompliancePersonalInfoMapper compliancePersonalInfoMapper, IjiamiCommonProperties ijiamiCommonProperties) {
        super(complianceAssessItemMapper, complianceAssessPointMapper, complianceItemPointResultMapper, complianceDetectAssessRelMapper, complianceAssessCategoryMapper,
                complianceSdkMapper, complianceItemPointMapper, compliancePersonalInfoMapper,ijiamiCommonProperties);
    }


    @Override
    public Map<String, Object> buildReportData(long taskId,String documentId, Integer type,Integer terminalType) {
        Map<String, Object> dataMap = new HashMap<>();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        try {
            TTask task = taskMapper.selectByPrimaryKey(taskId);

            int behavior = BehaviorStageEnum.BEHAVIOR_FRONT.getValue();
            if (task.getTerminalType() == TerminalTypeEnum.IOS) {
                behavior = 0;
            }
            //更新检测点结果
            updateDetectPointResult(taskId);

            //处理ios涉及个人信息权限
            List<List<PermissionVO>> permissionVOList = constructAndroidPermissions(taskId);
            dataMap.put("androidPermissionDTOS", permissionVOList);
            //ios涉及个人信息权限截图
            buildPermissionPic(taskId,dataMap);

            //处理评估项/评估点数据结构
            ComplianceCategoryItemDto complianceCategoryItemDto = constructAssessItems(taskId,terminalType, TaskDetectionTypeEnum.getItem(task.getDetectionType()));
            dataMap.put("assessItemDTOS", complianceCategoryItemDto.getComplianceAssessItemDtos());
            //评估点
            List<ComplianceItemResultDto> complianceItemResultDtos = complianceCategoryItemDto.getComplianceItemResultDtos();
            if(CollectionUtils.isNotEmpty(complianceItemResultDtos)){
                Map<String, List<ComplianceItemResultDto>> categoryResultMap = complianceItemResultDtos.stream().collect(Collectors.groupingBy(ComplianceItemResultDto::getCategoryName));
                ArrayList<ComplianceReportCategoryDto> complianceReportCategoryDtos = new ArrayList<>();
                categoryResultMap.forEach((k,v)->{
                    ComplianceReportCategoryDto complianceReportCategoryDto = new ComplianceReportCategoryDto();
                    complianceReportCategoryDto.setName(k);
                    complianceReportCategoryDto.setItemDtos(v);
                    complianceReportCategoryDtos.add(complianceReportCategoryDto);
                });
                dataMap.put("categoryDTOS", complianceReportCategoryDtos);
            }

            //处理应用基本信息类型
            String typeName = buildBasicInfoType(taskId);

            //统计评估结论
            ComplianceConclusionDto complianceConclusionDTO = buildPointResultStatistics(complianceCategoryItemDto.getComplianceAssessItemDtos());
            dataMap.put("complianceConclusionDTO", complianceConclusionDTO);

            //app基本信息
            BaseMessageVO appBaseInfo = taskService.getBaseMessage(documentId);
            //渠道检测数据对接
            ApkChannelDto apkChannelDto = channelMonitoringService.getApkChannelForReport(appBaseInfo.getPackageName(), appBaseInfo.getApkMd5());

            dataMap.put("apkChannelBO",apkChannelDto);
            appBaseInfo.setTypeName(typeName);
            //检测手机环境
            Map<String,String> reportBaseInfo = new HashMap<>();
            reportBaseInfo.put("phoneBrand", "-");         //手机品牌
            reportBaseInfo.put("phoneModel", "-");         //手机型号
            reportBaseInfo.put("phoneSystemVersion", "iOS13.6.1"); //手机系统版本
            builtReportInfo(task,reportBaseInfo);

            List<SdkVO> sdkList = taskService.getSdkList(documentId,taskId);//已解析Sdk信息/通讯信息/权限信息
            sdkList = sdkList.stream().collect(
                    collectingAndThen(
                            toCollection(() -> new TreeSet<>(Comparator.comparing(SdkVO::getName))), ArrayList::new)
            );
            // 传输个人信息
            List<TPrivacySensitiveWord> privacySensitiveWords = privacySensitiveWordMapper.queryTprivacySensitiveWords(task.getTaskId(), behavior);
            // 存储个人信息
            List<TPrivacySharedPrefs> sharedPrefs = privacySharedPrefsMapper.findTprivacySharedPrefs(task.getTaskId(), behavior);
            //将检测时间格式化为yyyy-MM-dd
            String apkDetectionStarttime = appBaseInfo.getApkDetectionStarttime();
            if(apkDetectionStarttime != null){
                appBaseInfo.setApkDetectionStarttime(format.format(format.parse(apkDetectionStarttime)));
            }
            // app基本信息
            dataMap.put("appBaseInfo", appBaseInfo);
            // 检测手机环境
            dataMap.put("reportBaseInfo", reportBaseInfo);

            if(CollectionUtils.isNotEmpty(sdkList)){
                //组装SDK行为分析
                sdkActionNougat(sdkList,taskId);
                //区分境内境外通讯行为数据
                sdkOutsideAddresses(sdkList,dataMap);
                //评估点为个人信息出境情况的特殊处理
                pointSdkOutsideAddresses(taskId,dataMap);
                //组装SDK权限使用次数
                sdkPermissionVOs(sdkList,taskId);
                //组装SDK个人传输信息
                sdkSensitiveWords(sdkList,privacySensitiveWords);
                //组装SDK存储个人信息
                sdkSharedPrefs(sdkList, sharedPrefs);
                //sdk行为数据分析统计
                sdkActionAnalysis(sdkList);
                //sdk行为统计
                sdkActionAnalysisTotal(sdkList,dataMap);
            }

            dataMap.put("sdkList", sdkList);

        } catch (Exception e) {
            log.error("下载报告数据解析失败 : ",e);
        }
        return dataMap;
    }
}
