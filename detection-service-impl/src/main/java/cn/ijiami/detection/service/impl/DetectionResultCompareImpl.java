package cn.ijiami.detection.service.impl;

import cn.ijiami.base.common.user.IUser;
import cn.ijiami.detection.VO.*;
import cn.ijiami.detection.VO.detection.AppStoreComplianceCheckVO;
import cn.ijiami.detection.VO.detection.BaseMessageCompareVO;
import cn.ijiami.detection.VO.detection.BaseMessageVO;
import cn.ijiami.detection.VO.detection.SdkVO;
import cn.ijiami.detection.entity.*;
import cn.ijiami.detection.enums.*;
import cn.ijiami.detection.mapper.*;
import cn.ijiami.detection.query.*;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.server.client.base.enums.DetectionStatusEnum;
import cn.ijiami.detection.server.client.base.enums.DynamicAutoStatusEnum;
import cn.ijiami.detection.server.client.base.enums.LawResultStatusEnum;
import cn.ijiami.detection.server.client.base.enums.TerminalTypeEnum;
import cn.ijiami.detection.service.IDetectionResultCompare;
import cn.ijiami.detection.service.api.*;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.LawDetectResultUtils;
import cn.ijiami.detection.utils.StreamUtils;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

import static cn.ijiami.detection.constant.PinfoConstant.CHECK_ITEM;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectionResultCompareImpl.java
 * @Description 任务对比
 * @createTime 2024年01月11日 17:35:00
 */
@Slf4j
@Service
public class DetectionResultCompareImpl implements IDetectionResultCompare {

    @Autowired
    private TDetectionResultCompareMapper tDetectionResultCompareMapper;

    @Autowired
    private TTaskMapper taskMapper;

    @Autowired
    private TTaskExtendMapper taskExtendMapper;

    @Autowired
    private TAssetsMapper assetsMapper;

    @Autowired
    private IPrivacyDetectionService privacyDetectionService;

    @Autowired
    private IPrivacyDetectionTransferRiskService iPrivacyDetectionTransferRiskService;

    @Autowired
    private StaticFunctionAnalysisService staticFunctionAnalysisService;

    @Autowired
    private IPrivacyActionNougatService privacyActionNougatService;

    @Autowired
    private IMiitDetectService miitDetectService;

    @Autowired
    private TPrivacyPolicyMapper privacyPolicyMapper;

    @Autowired
    private TPrivacyLawsRegulationsMapper privacyLawsRegulationsMapper;

    @Autowired
    private TDetectionResultCompareTaskMapper tDetectionResultCompareTaskMapper;

    @Autowired
    private IapkCategoryService iapkCategoryService;

    @Autowired
    private TSdkCheckListMapper sdkCheckListMapper;

    @Autowired
    private TPrivacyPolicyResultMapper privacyPolicyResultMapper;

    @Autowired
    private TPrivacyLawsResultMapper privacyLawsResultMapper;

    @Override
    public List<CountLawDetectResult> getCompliance(Long id, Long lawId) {
        DetectionResultCompareDetailVO compare = tDetectionResultCompareMapper.findById(id);
        if (Objects.nonNull(compare)) {
            List<CountLawDetectResult> lawDetectResults = compare.getTaskList().stream()
                    .map(task -> miitDetectService.findLawDetectResultByTaskId(lawId, task.getTaskId()))
                    .collect(Collectors.toList());
            return compareLawDetectResult(lawDetectResults);
        }
        return Collections.emptyList();
    }

    @Override
    public List<Map<String, Object>> getLawList(Long id) {
        DetectionResultCompareDetailVO compare = tDetectionResultCompareMapper.findById(id);
        if (Objects.nonNull(compare)) {
            return compare.getTaskList().stream()
                    .filter(Objects::nonNull)
                    .flatMap(task -> privacyLawsRegulationsMapper.selectAllLaw(task.getTaskId()).stream())
                    .filter(StreamUtils.distinctByKey(map -> map.get("lawId")))
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    private static List<CountLawDetectResult> compareLawDetectResult(List<CountLawDetectResult> lawDetectResults) {
        List<CountLawDetectResult> compares = new ArrayList<>();
        if (lawDetectResults == null || lawDetectResults.isEmpty()) {
            return compares;
        }
        // 进行对比
        for (CountLawDetectResult lawDetectResult : lawDetectResults) {
            CountLawDetectResult result = createCompareLawDetectResult(lawDetectResult, lawDetectResults);
            if (Objects.nonNull(result)) {
                compares.add(result);
            }
        }
        return compares;
    }

    private static CountLawDetectResult createCompareLawDetectResult(CountLawDetectResult target,
                                                                            List<CountLawDetectResult> lawDetectResults) {
        if (Objects.isNull(target) || CollectionUtils.isEmpty(target.getLawDetectResult().getNextLaws())) {
            return target;
        }
        List<List<LawDetectResultVO>> allCheckLawItems = lawDetectResults.stream()
                .map(result -> {
                    if (Objects.isNull(result)) {
                        return new ArrayList<LawDetectResultVO>();
                    } else {
                        return findCheckLawItems(result.getLawDetectResult(), new ArrayList<>());
                    }
                })
                .collect(Collectors.toList());
        List<LawDetectResultVO> checkList = findCheckLawItems(target.getLawDetectResult(), new ArrayList<>());
        for (LawDetectResultVO checkItem : checkList) {
            checkItem.setSame(isAllLawDetectResultMatch(checkItem, allCheckLawItems));
        }
        return target;
    }

    private static boolean isAllLawDetectResultMatch(LawDetectResultVO target, List<List<LawDetectResultVO>> lawDetectResults) {
        return lawDetectResults.stream().allMatch(detect ->
                detect.stream().anyMatch(result ->
                        StringUtils.equals(result.getItemNo(), target.getItemNo()) &&
                                result.getResultStatus() == target.getResultStatus()));
    }

    private static List<LawDetectResultVO> findCheckLawItems(LawDetectResultVO target, List<LawDetectResultVO> checkList) {
        checkList.add(target);
        if (CollectionUtils.isNotEmpty(target.getNextLaws())) {
            for (LawDetectResultVO resultVO:target.getNextLaws()) {
                findCheckLawItems(resultVO, checkList);
            }
        }
        return checkList;
    }

    @Override
    public List<BehaviorsStatisticsVO> getBehaviorsStatistics(Long id, Boolean merge) {
        DetectionResultCompareDetailVO compare = tDetectionResultCompareMapper.findById(id);
        if (Objects.nonNull(compare)) {
            return compare.getTaskList().stream()
                    .map(TTask::getTaskId)
                    .map(taskId -> {
                        BehaviorsStatisticsVO statisticsVO = new BehaviorsStatisticsVO();
                        statisticsVO.setTaskId(taskId);
                        statisticsVO.setGrantList(privacyActionNougatService.countBehaviorsByTaskIdAndStage(taskId, BehaviorStageEnum.BEHAVIOR_GRANT.getValue(), merge));
                        statisticsVO.setFrontList(privacyActionNougatService.countBehaviorsByTaskIdAndStage(taskId, BehaviorStageEnum.BEHAVIOR_FRONT.getValue(), merge));
                        statisticsVO.setGroundList(privacyActionNougatService.countBehaviorsByTaskIdAndStage(taskId, BehaviorStageEnum.BEHAVIOR_GROUND.getValue(), merge));
                        statisticsVO.setExitList(privacyActionNougatService.countBehaviorsByTaskIdAndStage(taskId, BehaviorStageEnum.BEHAVIOR_EXIT.getValue(), merge));
                        statisticsVO.setAllList(privacyActionNougatService.countBehaviorsByTaskId(taskId, merge));
                        return statisticsVO;
                    })
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public List<StaticFunctionCompareVO> getStaticFunctionBehaviorList(Long id, List<PrivacyStatusEnum> privacyList) {
        DetectionResultCompareDetailVO compare = tDetectionResultCompareMapper.findById(id);
        if (Objects.nonNull(compare)) {
            List<StaticFunctionCompareVO> compareVOList = compare.getTaskList().stream()
                    .map(TTask::getTaskId)
                    .map(taskId -> {
                        StaticFunctionCompareVO compareVO = new StaticFunctionCompareVO();
                        compareVO.setTaskId(taskId);
                        compareVO.setStaticFunctionBehaviorlist(staticFunctionAnalysisService.getBehaviorList(taskId, privacyList));
                        return compareVO;
                    })
                    .collect(Collectors.toList());
            return staticFunctionBehaviorListDiff(compareVOList);
        }
        return Collections.emptyList();
    }

    private List<StaticFunctionCompareVO> staticFunctionBehaviorListDiff(List<StaticFunctionCompareVO> compareVOList) {
        // 遍历每个SdkCompareVO
        for (StaticFunctionCompareVO current : compareVOList) {
            List<StaticFunctionBehaviorVO> diffs = new ArrayList<>();
            // 与其他SdkCompareVO比较
            for (StaticFunctionCompareVO other : compareVOList) {
                if (current != other) {
                    // 找出当前列表中独有的
                    List<StaticFunctionBehaviorVO> uniqueInCurrent = current.getStaticFunctionBehaviorlist().stream()
                            .filter(c -> other.getStaticFunctionBehaviorlist().stream().noneMatch(o ->
                                    Objects.equals(c.getActionId(), o.getActionId())))
                            .collect(Collectors.toList());
                    diffs.addAll(uniqueInCurrent);
                }
            }
            // 更新差异列表
            current.setStaticFunctionBehaviorDiffList(diffs);
        }
        return compareVOList;
    }

    @Override
    public List<LawInfoRiskDetailCompareVO> getLawInfoRiskDetail(Long id) {
        DetectionResultCompareDetailVO compare = tDetectionResultCompareMapper.findById(id);
        if (Objects.nonNull(compare)) {
            List<List<TPrivacyPolicyResult>> riskDetailVOList = compare.getTaskList().stream()
                    .filter(Objects::nonNull)
                    .map(task -> iPrivacyDetectionTransferRiskService.getLawInfoRiskDetail(task.getApkDetectionDetailId()))
                    .collect(Collectors.toList());
            return compareLawRiskList(riskDetailVOList);
        }
        return Collections.emptyList();
    }

    public static List<LawInfoRiskDetailCompareVO> compareLawRiskList(List<List<TPrivacyPolicyResult>> infoRiskDetailVOS) {
        List<LawInfoRiskDetailCompareVO> compares = new ArrayList<>();
        if (infoRiskDetailVOS == null || infoRiskDetailVOS.isEmpty()) {
            return compares;
        }
        // 进行对比
        for (List<TPrivacyPolicyResult> detailVO : infoRiskDetailVOS) {
            compares.add(createCompareLawRiskList(detailVO, infoRiskDetailVOS));
        }
        return compares;
    }

    private static LawInfoRiskDetailCompareVO createCompareLawRiskList(List<TPrivacyPolicyResult> target,
                                                                            List<List<TPrivacyPolicyResult>> infoRiskDetailVOS) {
        LawInfoRiskDetailCompareVO compareVO = new LawInfoRiskDetailCompareVO();
        List<FieldCompare<TPrivacyPolicyResult>> fieldCompareList = new ArrayList<>();
        for (TPrivacyPolicyResult detectionResultVO : target) {
            FieldCompare<TPrivacyPolicyResult> fieldCompare = new FieldCompare<>();
            // 单个风险项与其它检测结果的同个检测项横向对比
            fieldCompare.setSame(isAllLawRiskMatch(detectionResultVO, infoRiskDetailVOS));
            fieldCompare.setValue(detectionResultVO);
            fieldCompareList.add(fieldCompare);
        }
        compareVO.setList(fieldCompareList);
        return compareVO;
    }

    private static boolean isAllLawRiskMatch(TPrivacyPolicyResult resultVO, List<List<TPrivacyPolicyResult>> infoRiskDetailVOS) {
        return infoRiskDetailVOS.stream().allMatch(risk ->
                risk.stream().anyMatch(detail ->
                        StringUtils.equals(detail.getItemNo(), resultVO.getItemNo()) &&
                                StringUtils.equals(detail.getResult(), resultVO.getResult())));
    }

    @Override
    public List<PersonalInfoRiskDetailCompareVO> getPersonalInfoRiskDetail(Long id) {
        DetectionResultCompareDetailVO compare = tDetectionResultCompareMapper.findById(id);
        if (Objects.nonNull(compare)) {
            List<PersonalInfoRiskDetailVO> riskDetailVOList =compare.getTaskList().stream()
                    .filter(Objects::nonNull)
                    .map(task -> iPrivacyDetectionTransferRiskService.getPersonalInfoRiskDetail(task.getApkDetectionDetailId()))
                    .collect(Collectors.toList());
            return compareDetectionResultList(riskDetailVOList);
        }
        return Collections.emptyList();
    }

    public static List<PersonalInfoRiskDetailCompareVO> compareDetectionResultList(List<PersonalInfoRiskDetailVO> infoRiskDetailVOS) {
        List<PersonalInfoRiskDetailCompareVO> compares = new ArrayList<>();
        if (infoRiskDetailVOS == null || infoRiskDetailVOS.isEmpty()) {
            return compares;
        }
        // 进行对比
        for (PersonalInfoRiskDetailVO detailVO : infoRiskDetailVOS) {
            compares.add(createCompareDetectionResult(detailVO, infoRiskDetailVOS));
        }
        return compares;
    }

    private static PersonalInfoRiskDetailCompareVO createCompareDetectionResult(PersonalInfoRiskDetailVO target, List<PersonalInfoRiskDetailVO> infoRiskDetailVOS) {
        PersonalInfoRiskDetailCompareVO compareVO = new PersonalInfoRiskDetailCompareVO();
        compareVO.setFlag(target.getFlag());
        List<FieldCompare<PrivacyDetectionResultVO>> fieldCompareList = new ArrayList<>();
        for (PrivacyDetectionResultVO detectionResultVO : target.getList()) {
            FieldCompare<PrivacyDetectionResultVO> fieldCompare = new FieldCompare<>();
            // 单个风险项与其它检测结果的同个检测项横向对比
            fieldCompare.setSame(isAllPersonalRiskMatch(detectionResultVO, infoRiskDetailVOS));
            fieldCompare.setValue(detectionResultVO);
            fieldCompareList.add(fieldCompare);
        }
        compareVO.setList(fieldCompareList);
        compareVO.setTaskId(target.getTaskId());
        return compareVO;
    }

    private static boolean isAllPersonalRiskMatch(PrivacyDetectionResultVO resultVO, List<PersonalInfoRiskDetailVO> infoRiskDetailVOS) {
        return infoRiskDetailVOS.stream().allMatch(risk ->
                risk.getList().stream().anyMatch(detail ->
                        StringUtils.equals(detail.getItemNo(), resultVO.getItemNo()) &&
                                StringUtils.equals(detail.getResult(), resultVO.getResult())));
    }

    @Override
    public List<SdkCompareVO> getSDKList(Long id) {
        DetectionResultCompareDetailVO compare = tDetectionResultCompareMapper.findById(id);
        if (Objects.nonNull(compare)) {
            List<SdkCompareVO> sdkCompareList =  compare.getTaskList().stream()
                    .filter(Objects::nonNull)
                    .map(task -> {
                        try {
                            SdkCompareVO sdkCompareVO = new SdkCompareVO();
                            sdkCompareVO.setTaskId(task.getTaskId());
                            sdkCompareVO.setSdkVOList(privacyDetectionService.getSDKList(task.getApkDetectionDetailId(), task.getTaskId())
                                    .stream()
                                    .sorted(Comparator.comparing(SdkVO::getName))
                                    .collect(Collectors.toList()));
                            return sdkCompareVO;
                        } catch (IjiamiApplicationException e) {
                            throw new RuntimeException(e);
                        }
                    })
                    .collect(Collectors.toList());
            return sdkListDiff(sdkCompareList);
        }
        return Collections.emptyList();
    }

    private List<SdkCompareVO> sdkListDiff(List<SdkCompareVO> sdkCompareList) {
        // 遍历每个SdkCompareVO
        for (SdkCompareVO current : sdkCompareList) {
            List<SdkVO> diffs = new ArrayList<>();
            // 与其他SdkCompareVO比较
            for (SdkCompareVO other : sdkCompareList) {
                if (current != other) {
                    // 找出当前列表中独有的
                    List<SdkVO> uniqueInCurrent = current.getSdkVOList().stream()
                            .filter(sdk -> other.getSdkVOList().stream().noneMatch(o ->
                                    Objects.equals(sdk.getName(), o.getName())
                                            && Objects.equals(sdk.getPackageName(), o.getPackageName())))
                            .collect(Collectors.toList());
                    diffs.addAll(uniqueInCurrent);
                }
            }
            // 更新sdkDifferList
            current.setSdkDiffList(diffs);
        }
        return sdkCompareList;
    }

    @Override
    public List<SafeDetailCompareVO> getSafeDetail(Long id, Integer behaviorStage) {
        DetectionResultCompareDetailVO compare = tDetectionResultCompareMapper.findById(id);
        if (Objects.nonNull(compare)) {
            List<SafeDetailCompareVO> safeDetailCompareVOList= compare.getTaskList().stream()
                    .filter(Objects::nonNull)
                    .map(task -> {
                        SafeDetailCompareVO safeDetailCompareVO = new SafeDetailCompareVO();
                        SafeDetailVO safeDetailVO = privacyDetectionService.getSafeDetail(task.getApkDetectionDetailId(), behaviorStage);
                        BeanUtils.copyProperties(safeDetailVO, safeDetailCompareVO);
                        return safeDetailCompareVO;
                    })
                    .collect(Collectors.toList());
            return safeDetailListDiff(safeDetailCompareVOList);
        }
        return Collections.emptyList();
    }

    private List<SafeDetailCompareVO> safeDetailListDiff(List<SafeDetailCompareVO> safeDetailCompareList) {
        // 遍历每个SdkCompareVO
        for (SafeDetailCompareVO current : safeDetailCompareList) {
            List<List<PermissionVO>> declarePermissionDiffs = new ArrayList<>();
            // 初始化
            current.getList().forEach(list -> declarePermissionDiffs.add(new ArrayList<>()));
            // 与其他SdkCompareVO比较
            for (SafeDetailCompareVO other : safeDetailCompareList) {
                if (current != other) {
                    // 找出当前列表中独有的
                    for (int i=0; i<current.getList().size(); i++) {
                        List<PermissionVO> currentList = current.getList().get(i);
                        // 避免被对比的列表长度不够
                        if (i < other.getList().size()) {
                            List<PermissionVO> otherList = other.getList().get(i);
                            declarePermissionDiffs.get(i).addAll(currentList.stream()
                                    .filter(c ->
                                            otherList.stream()
                                            .noneMatch(o -> Objects.equals(o.getName(), c.getName())
                                                    && Objects.equals(o.getRemark(), c.getRemark())
                                                    && Objects.equals(o.getAliasName(), c.getAliasName())))
                                    .collect(Collectors.toList()));
                        } else {
                            declarePermissionDiffs.get(i).addAll(currentList);
                        }
                    }
                }
            }
            // 更新sdkDifferList
            current.setPermissionDiffList(declarePermissionDiffs);
        }
        return safeDetailCompareList;
    }
    @Override
    public List<BaseMessageCompareVO> basicDetail(Long id) {
        DetectionResultCompareDetailVO compare = tDetectionResultCompareMapper.findById(id);
        if (Objects.nonNull(compare)) {
            List<BaseMessageVO> baseMessageVOList = compare.getTaskList().stream()
                    .filter(Objects::nonNull)
                    .map(task -> {
                        try {
                            return privacyDetectionService.getAppBaseInfo(task.getApkDetectionDetailId());
                        } catch (IjiamiApplicationException e) {
                            throw new RuntimeException(e);
                        }
                    })
                    .collect(Collectors.toList());
            List<PrivacyCategoryVO> privacyCategoryVOList = compare.getTaskList()
                    .stream()
                    .map(TTask::getTaskId)
                    .map(taskId -> iapkCategoryService.findByTaskId(taskId))
                    .collect(Collectors.toList());
            try {
                return compareInfoList(baseMessageVOList, privacyCategoryVOList);
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        }
        return Collections.emptyList();
    }

    public static List<BaseMessageCompareVO> compareInfoList(List<BaseMessageVO> infoList,
                                                             List<PrivacyCategoryVO> privacyCategoryVOList) throws IllegalAccessException {
        List<BaseMessageCompareVO> compares = new ArrayList<>();

        if (infoList == null || infoList.isEmpty()) {
            return compares;
        }

        // 对每个 Info 对象创建一个 InfoCompare 对象
        for (BaseMessageVO info : infoList) {
            compares.add(createInfoCompare(info, infoList, privacyCategoryVOList));
        }

        return compares;
    }

    private static BaseMessageCompareVO createInfoCompare(BaseMessageVO info, List<BaseMessageVO> infoList,
                                                          List<PrivacyCategoryVO> privacyCategoryVOList) throws IllegalAccessException {
        BaseMessageCompareVO infoCompare = new BaseMessageCompareVO();

        for (Field field : BaseMessageVO.class.getDeclaredFields()) {
            if (field.getName().equalsIgnoreCase("serialVersionUID")) {
                continue;
            }
            field.setAccessible(true);
            Object value = field.get(info);
            try {
                // 检查此字段值是否在所有 Info 对象中相同
                boolean isSame = true;
                for (BaseMessageVO otherInfo : infoList) {
                    Field otherField = otherInfo.getClass().getDeclaredField(field.getName());
                    otherField.setAccessible(true);
                    Object otherValue = otherField.get(otherInfo);

                    if (!Objects.equals(value, otherValue)) {
                        isSame = false;
                        break;
                    }
                }
                // 将 FieldCompare 对象设置到 InfoCompare 对应字段
                Field compareField = BaseMessageCompareVO.class.getDeclaredField(field.getName());
                compareField.setAccessible(true);
                if (compareField.getType().getTypeName().equals(FieldCompare.class.getTypeName())) {
                    compareField.set(infoCompare, new FieldCompare<>(value, isSame));
                } else {
                    compareField.set(infoCompare, value);
                }
            } catch (NoSuchFieldException e) {
                log.info("{} 找不到，不进行对比", field.getName());
            }
        }
        // 对比应用功能是否相同
        PrivacyCategoryVO categoryVO;
        Optional<PrivacyCategoryVO> opt = privacyCategoryVOList.stream().filter(p -> p.getTaskId().equals(info.getTaskId())).findFirst();
        if (opt.isPresent()) {
            categoryVO = opt.get();
        } else {
            categoryVO = new PrivacyCategoryVO();
            categoryVO.setPrivacyCategoryIds(Collections.emptyList());
            categoryVO.setPrivacyCategoryNames(Collections.emptyList());
            categoryVO.setTaskId(info.getTaskId());
        }
        boolean isSame = true;
        for (PrivacyCategoryVO v:privacyCategoryVOList) {
            if (!Objects.equals(v.getPrivacyCategoryIds(), categoryVO.getPrivacyCategoryIds())) {
                isSame = false;
                break;
            }
        }
        infoCompare.setPrivacyCategory(new FieldCompare<>(categoryVO, isSame));
        return infoCompare;
    }

    @Override
    public void delete(IUser user, Long id) {
        TDetectionResultCompare compare = tDetectionResultCompareMapper.selectByPrimaryKey(id);
        if (Objects.nonNull(compare)) {
            if (compare.getStatus() == StatusEnum.DELETE) {
                throw new IllegalArgumentException("已删除");
            }
            compare.setStatus(StatusEnum.DELETE);
            tDetectionResultCompareMapper.updateByPrimaryKeySelective(compare);
        }
    }

    @Transactional
    @Override
    public void create(IUser user, CreateDetectionCompare createDetectionCompare, boolean isAdmin) {
        if (createDetectionCompare.getTaskIds().size() < 2) {
            throw new IllegalArgumentException("对比数量不能少于2个");
        }
        List<TTask> taskList = createDetectionCompare
                .getTaskIds()
                .stream()
                .map(taskId -> taskMapper.selectByPrimaryKey(taskId))
                .collect(Collectors.toList());
        if (taskList.stream().anyMatch(Objects::isNull) || taskList.isEmpty()) {
            throw new IllegalArgumentException("任务不存在");
        }
        TDetectionResultCompare tDetectionResultCompare = gettDetectionResultCompare(user, taskList);
        tDetectionResultCompareMapper.insert(tDetectionResultCompare);
        createDetectionCompare.getTaskIds().forEach(taskId -> {
            TDetectionResultCompareTask detectionResultCompareTask = new TDetectionResultCompareTask();
            detectionResultCompareTask.setReportId(tDetectionResultCompare.getId());
            detectionResultCompareTask.setTaskId(taskId);
            tDetectionResultCompareTaskMapper.insert(detectionResultCompareTask);
        });
    }

    @NotNull
    private static TDetectionResultCompare gettDetectionResultCompare(IUser user, List<TTask> taskList) {
        TTask firstTask = taskList.get(0);
        for (TTask task: taskList) {
            if (task.getTerminalType() != firstTask.getTerminalType()) {
                throw new IllegalArgumentException("任务的平台不一致");
            }
            if (task.getIsDelete() == BooleanEnum.TRUE.value) {
                throw new IllegalArgumentException("任务已删除");
            }
            if (task.getTaskTatus() != DetectionStatusEnum.DETECTION_OVER) {
                throw new IllegalArgumentException("任务静态检测未完成");
            }
            if (task.getDynamicStatus() != DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED) {
                throw new IllegalArgumentException("任务动态检测未完成");
            }
        }
        TDetectionResultCompare tDetectionResultCompare = new TDetectionResultCompare();
        tDetectionResultCompare.setCreateTime(new Date());
        tDetectionResultCompare.setStatus(StatusEnum.NORMAL);
        tDetectionResultCompare.setCreateUserId(user.getUserId());
        tDetectionResultCompare.setTerminalType(firstTask.getTerminalType());
        return tDetectionResultCompare;
    }

    @Override
    public PageInfo<DetectionCompareVO> findByPage(IUser user, DetectionCompareQuery query, boolean isAdmin) {
        if (query.getPage() != null && query.getRows() != null) {
            PageHelper.startPage(query.getPage(), query.getRows());
        }
        List<TDetectionResultCompare> resultList = tDetectionResultCompareMapper.
                findByPage(TerminalTypeEnum.getAndValid(query.getTerminalType()), query.getName(), isAdmin ? null : user.getUserId());
        PageInfo<TDetectionResultCompare> page = new PageInfo<>(resultList);
        PageInfo<DetectionCompareVO> detectionSummaryPage = new PageInfo<>();
        BeanUtils.copyProperties(page, detectionSummaryPage);
        detectionSummaryPage.setList(resultList.stream().map(result -> {
            DetectionCompareVO compareVO = new DetectionCompareVO();

            List<DetectionSummary> detectionSummaryList = tDetectionResultCompareMapper.findDetectionSummaryById(result.getId());

            compareVO.setReportId(result.getId());
            compareVO.setDetectionList(detectionSummaryList);
            compareVO.setCreateTime(result.getCreateTime());
            return compareVO;
        }).collect(Collectors.toList()));
        return detectionSummaryPage;
    }

    @Override
    public List<TPrivacyPolicyType> countLaw(Long id) {
        DetectionResultCompareDetailVO compare = tDetectionResultCompareMapper.findById(id);
        if (Objects.nonNull(compare)) {
            return compare.getTaskList().stream()
                    .filter(Objects::nonNull)
                    .flatMap(task -> privacyPolicyMapper.countLawByTaskId(task.getTaskId(), task.getTerminalType().getValue()).stream())
                    .filter(StreamUtils.distinctByKey(TPrivacyPolicyType::getType))
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public List<CompareLawDetectDetailVO> findDetailPageByTaskIdAndItemNo(LawItemResultDetailCompareQuery query) {
        DetectionResultCompareDetailVO compare = tDetectionResultCompareMapper.findById(query.getReportId());
        if (Objects.nonNull(compare)) {
            return compare.getTaskList().stream()
                    .filter(Objects::nonNull)
                    .map(task -> {
                        LawItemResultDetailQuery taskItemQuery = new LawItemResultDetailQuery();
                        taskItemQuery.setItemNo(query.getItemNo());
                        taskItemQuery.setTaskId(task.getTaskId());
                        taskItemQuery.setPage(query.getPage());
                        taskItemQuery.setRows(query.getRows());
                        Optional<TPrivacyPolicyResult> policyResultOptional = findPrivacyPolicyDetail(task.getTaskId());
                        LawDetectDetailVO lawDetectDetailVO = miitDetectService.findDetailPageByTaskIdAndItemNo(taskItemQuery);
                        CompareLawDetectDetailVO compareLawDetectDetailVO = new CompareLawDetectDetailVO();
                        BeanUtils.copyProperties(lawDetectDetailVO, compareLawDetectDetailVO);
                        compareLawDetectDetailVO.setTaskId(task.getTaskId());
                        LawDetectResultVO itemResult = miitDetectService.findLawDetectResultItem(task.getTaskId(), query.getItemNo());
                        if (itemResult != null) {
                            compareLawDetectDetailVO.setConclusion(itemResult.getConclusion());
                            if (itemResult.getResultStatus() == LawResultStatusEnum.NON_COMPLIANCE) {
                                compareLawDetectDetailVO.setSuggestion(itemResult.getSuggestion());
                            }
                        }
                        if (policyResultOptional.isPresent()) {
                            compareLawDetectDetailVO.setPrivacyPolicyText(policyResultOptional.get().getDetailResult());
                            compareLawDetectDetailVO.setThirdPartySharingText(policyResultOptional.get().getThirdPartySharingText());
                            compareLawDetectDetailVO.setThirdPartySharingChecklist(policyResultOptional.get().getThirdPartySharingChecklist());
                        }
                        return compareLawDetectDetailVO;
                    })
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    private Optional<TPrivacyPolicyResult> findPrivacyPolicyDetail(Long taskId) {
        List<TPrivacyPolicyResult> result = privacyPolicyResultMapper.findByTaskId(taskId);
        TSdkCheckList record = new TSdkCheckList();
        record.setTaskId(taskId);
        Optional<TPrivacyPolicyResult> policyDetail = findByItemNo(result, PrivacyPolicyItemNoEnum.PRIVACY_POLICY_DETAIL);
        List<TSdkCheckList> sdkCheckList = sdkCheckListMapper.select(record);
        if (!sdkCheckList.isEmpty()) {
            if (policyDetail.isPresent()) {
                String sdkJson = sdkCheckList.get(0).getSdkList();
                if (StringUtils.isNotBlank(sdkJson)) {
                    policyDetail.get().setThirdPartySharingChecklist(CommonUtil.jsonToBean(sdkJson, new TypeReference<List<CheckList.Row>>() {
                    }));
                } else {
                    policyDetail.get().setThirdPartySharingText(sdkCheckList.get(0).getOriginalText());
                }
            }
        }
        return policyDetail;
    }

    private Optional<TPrivacyPolicyResult> findByItemNo(List<TPrivacyPolicyResult> result, PrivacyPolicyItemNoEnum itemNoEnum) {
        return result.stream().filter(r -> StringUtils.equals(r.getItemNo(), itemNoEnum.itemNo)).findFirst();
    }

    @Override
    public List<DetectionCompareSdkVO> getLawDetailBehaviorSdk(Long reportId, Integer dataType, String itemNo) {
        DetectionResultCompareDetailVO compare = tDetectionResultCompareMapper.findById(reportId);
        if (Objects.nonNull(compare)) {
            return compare.getTaskList().stream()
                    .filter(Objects::nonNull)
                    .map(task -> {
                        DetectionCompareSdkVO compareSdkVO = new DetectionCompareSdkVO();
                        compareSdkVO.setBehaviorSdkList(privacyActionNougatService.getLawDetailBehaviorSdk(task.getTaskId(), dataType, itemNo));
                        compareSdkVO.setTaskId(task.getTaskId());
                        return compareSdkVO;
                    })
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public List<BehaviorPermissionVo> getLawDetailBehaviorApplyNameAndPermission(Long reportId, Integer dataType, String itemNo) {
        DetectionResultCompareDetailVO compare = tDetectionResultCompareMapper.findById(reportId);
        if (Objects.nonNull(compare)) {
            return compare.getTaskList().stream()
                    .filter(Objects::nonNull)
                    .map(task -> {
                        return privacyActionNougatService.getLawDetailBehaviorApplyNameAndPermission(task.getTaskId(), dataType, itemNo);
                    })
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public List<AppStoreComplianceCheckCompareVO> getAppStoreComplianceCheck(Long reportId) {
        DetectionResultCompareDetailVO compare = tDetectionResultCompareMapper.findById(reportId);
        if (Objects.nonNull(compare)) {
            List<AppStoreComplianceCheckVO> appStoreComplianceCheckVOList = compare.getTaskList().stream()
                    .filter(Objects::nonNull)
                    .map(task -> iPrivacyDetectionTransferRiskService.getAppStoreComplianceCheck(task.getTaskId()))
                    .collect(Collectors.toList());
            return appStoreComplianceCheckListDiff(appStoreComplianceCheckVOList);
        }
        return Collections.emptyList();
    }

    private List<AppStoreComplianceCheckCompareVO> appStoreComplianceCheckListDiff(List<AppStoreComplianceCheckVO> appStoreComplianceCheckVOList) {
        List<AppStoreComplianceCheckCompareVO> compareList = appStoreComplianceCheckVOList.stream().map(vo -> {
            AppStoreComplianceCheckCompareVO compareVO = new AppStoreComplianceCheckCompareVO();
            compareVO.setCheckList(vo.getCheckList());
            compareVO.setUndeclaredList(vo.getUndeclaredList());
            compareVO.setAppStorePrivacyApiList(vo.getAppStorePrivacyApiList());
            return compareVO;
        }).collect(Collectors.toList());
        // 遍历每个SdkCompareVO
        for (AppStoreComplianceCheckCompareVO current : compareList) {
            List<SdkVO> undeclaredDiffList = new ArrayList<>();
            List<AppStorePrivacyApiVO> appStorePrivacyApiDifflist = new ArrayList<>();
            List<TPrivacyPolicyResult> checkDiffList = new ArrayList<>();
            // 与其他SdkCompareVO比较
            for (AppStoreComplianceCheckCompareVO other : compareList) {
                if (current != other) {
                    // 找出当前列表中独有的
                    if (CollectionUtils.isNotEmpty(current.getUndeclaredList())) {
                        undeclaredDiffList.addAll(
                                current.getUndeclaredList().stream().filter(c -> noneMatchUndeclaredDiffList(c, other))
                                        .collect(Collectors.toList()));
                    }
                    if (CollectionUtils.isNotEmpty(current.getAppStorePrivacyApiList())) {
                        appStorePrivacyApiDifflist.addAll(
                                current.getAppStorePrivacyApiList().stream().filter(c -> noneMatchAppStorePrivacyApiDiffList(c, other))
                                        .collect(Collectors.toList()));
                    }
                    if (CollectionUtils.isNotEmpty(current.getCheckList())) {
                        checkDiffList.addAll(
                                current.getCheckList().stream().filter(c -> noneMatchCheckDiffList(c, other))
                                        .collect(Collectors.toList()));
                    }
                }
            }
            current.setAppStorePrivacyApiDiffList(appStorePrivacyApiDifflist);
            current.setCheckDiffList(checkDiffList);
            current.setUndeclaredDiffList(undeclaredDiffList);
        }
        return compareList;
    }

    private boolean noneMatchUndeclaredDiffList(SdkVO current, AppStoreComplianceCheckCompareVO other) {
        return CollectionUtils.isEmpty(other.getUndeclaredList())
                || other.getUndeclaredList()
                .stream()
                .noneMatch(o -> Objects.equals(o.getName(), current.getName())
                        && Objects.equals(o.getVersion(), current.getVersion()));
    }


    private boolean noneMatchAppStorePrivacyApiDiffList(AppStorePrivacyApiVO current, AppStoreComplianceCheckCompareVO other) {
        return CollectionUtils.isEmpty(other.getAppStorePrivacyApiList())
                || other.getAppStorePrivacyApiList()
                .stream()
                .noneMatch(o -> Objects.equals(o.getApiName(), current.getApiName())
                        && Objects.equals(o.getPermissionName(), current.getPermissionName()));
    }


    private boolean noneMatchCheckDiffList(TPrivacyPolicyResult current, AppStoreComplianceCheckCompareVO other) {
        return CollectionUtils.isEmpty(other.getCheckList())
                || other.getCheckList()
                .stream()
                .noneMatch(o -> Objects.equals(o.getPolicyItemId(), current.getPolicyItemId())
                        && Objects.equals(o.getStatus(), current.getStatus()));
    }
}
