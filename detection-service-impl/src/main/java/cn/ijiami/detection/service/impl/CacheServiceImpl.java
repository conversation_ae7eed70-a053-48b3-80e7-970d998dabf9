package cn.ijiami.detection.service.impl;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import cn.ijiami.detection.VO.*;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacyActionNougatExtend;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.mapper.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;

import cn.ijiami.detection.VO.detection.privacy.IOSRealTimeLog;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.service.api.CacheService;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

@Slf4j
@Service
public class CacheServiceImpl implements CacheService {

    @Autowired
    private TAssetsMapper assetsMapper;

    @Autowired
    private RedisTemplate<String, AndroidSensorLog> sensorLogTemplate;

    @Autowired
    private RedisTemplate<String, String> redisStringTemplate;

    @Autowired
    private RedisTemplate<String,ChannelMonitoring> channelMonitoringTemplate;

    public final static Long SUCCESS = 1L;
    public final static Long FAILURE = 0L;

    @Override
    public void cacheSensorAction(Long taskId, AndroidSensorLog log) {
        // 日志数据保存到缓存中
        String cacheTaskLogKey = PinfoConstant.CACHE_DYNAMIC_SENSOR_LOG_PREFIX + taskId;
        Long length = sensorLogTemplate.opsForList().leftPush(cacheTaskLogKey, log);
        // 第一次添加数据，设置过期时间
        if (Objects.nonNull(length) && length == 1) {
            sensorLogTemplate.expire(cacheTaskLogKey, 6, TimeUnit.HOURS);
        }
    }

    @Override
    public List<AndroidSensorLog> getSensorLogList(Long taskId, int from, int to) {
        return sensorLogTemplate.opsForList()
                .range(PinfoConstant.CACHE_DYNAMIC_SENSOR_LOG_PREFIX + taskId, from, to);
    }

    @Override
    public Long getSensorLogListSize(Long taskId) {
        return sensorLogTemplate.opsForList().size(PinfoConstant.CACHE_DYNAMIC_SENSOR_LOG_PREFIX + taskId);
    }

    @Override
    public void cleanSensorAction(Long taskId) {
        String cacheTaskLogKey = PinfoConstant.CACHE_DYNAMIC_SENSOR_LOG_PREFIX + taskId;
        if (sensorLogTemplate.hasKey(cacheTaskLogKey)) {
            sensorLogTemplate.delete(cacheTaskLogKey);
            log.info("清除缓存 {}", cacheTaskLogKey);
        }
    }
    @Override
    public void setNotificationId(String key, String notificationId) {
        set(key, notificationId, 10L, TimeUnit.MINUTES);
    }

    @Override
    public String getNotificationId(String key) {
        return get(key);
    }

    @Override
    public void set(String key, String value, Long expire, TimeUnit unit) {
        if (StringUtils.isBlank(key)) {
            throw new IllegalArgumentException("key is blank");
        }
        if (Objects.isNull(value)) {
            throw new IllegalArgumentException("value is null");
        }
        redisStringTemplate.opsForValue().set(key, value);
        if (Objects.nonNull(expire)) {
            redisStringTemplate.expire(key, expire, unit);
        }
    }

    @Override
    public void setInt(String key, Integer value, Long expire, TimeUnit unit) {
        if (StringUtils.isBlank(key)) {
            throw new IllegalArgumentException("key is blank");
        }
        if (Objects.isNull(value)) {
            throw new IllegalArgumentException("value is null");
        }
        set(key, String.valueOf(value), expire, unit);
    }

    @Override
    public Integer getInt(String key) {
        String value = get(key);
        return Objects.isNull(value) ? null : Integer.parseInt(value);
    }

    @Override
    public void setLong(String key, Long value, Long expire, TimeUnit unit) {
        if (StringUtils.isBlank(key)) {
            throw new IllegalArgumentException("key is blank");
        }
        if (Objects.isNull(value)) {
            throw new IllegalArgumentException("value is null");
        }
        set(key, String.valueOf(value), expire, unit);
    }

    @Override
    public Long getLong(String key) {
        String value = get(key);
        return Objects.isNull(value) ? null : Long.parseLong(value);
    }

    @Override
    public String get(String key) {
        return redisStringTemplate.opsForValue().get(key);
    }

    @Override
    public void delete(String key) {
        redisStringTemplate.delete(key);
    }

    @Override
    public boolean compareAndSet(String key, String expect, String update, long expireTimeMillis) {
        String luaScript =
                "if redis.call('get', KEYS[1]) == ARGV[1] then" +
                        "    redis.call('set', KEYS[1], ARGV[2])" +
                        "    redis.call('pexpire', KEYS[1], ARGV[3]);" +
                        "return " + SUCCESS + ";" +
                        "else " +
                        "return " + FAILURE + ";" +
                        "end;";
        DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>(luaScript, Long.class);
        Long result = redisStringTemplate.execute(redisScript, Collections.singletonList(key), expect, update, String.valueOf(expireTimeMillis));
        return SUCCESS.equals(result);
    }

    @Override
    public ListOperations<String, String> opsForStringList() {
        return redisStringTemplate.opsForList();
    }

    @Override
    public SetOperations<String, String> opsForStringSet() {
        return redisStringTemplate.opsForSet();
    }


    /**
     * 将渠道风险数据根据缓存起来
     * @param apkMd5
     * @param channel
     */
    @Override
    public void cacheChannelMonitoringData(String apkMd5, ChannelMonitoring channel) {
        // 日志数据保存到缓存中
        String cacheKey = PinfoConstant.CACHE_CHANNEL_MONITORING_PREFIX + apkMd5;
        Long length = channelMonitoringTemplate.opsForList().leftPush(cacheKey, channel);
        // 第一次添加数据，设置过期时间
        if (Objects.nonNull(length) && length == 1) {
            channelMonitoringTemplate.expire(cacheKey, 30, TimeUnit.MINUTES);
        }
    }


    /**
     * 判断是否存在该key对象
     * @param apkMd5
     * @return
     */
    @Override
    public boolean hasChannelMonitoringKey(String apkMd5){
        String cacheTaskLogKey = PinfoConstant.CACHE_CHANNEL_MONITORING_PREFIX + apkMd5;
        return channelMonitoringTemplate.hasKey(cacheTaskLogKey);
    }

    /**
     * 根据key获取所有值
     * @param apkMd5
     * @return
     */
    @Override
    public List<ChannelMonitoring> getAllValuesByKey(String apkMd5){
        String cacheTaskLogKey = PinfoConstant.CACHE_CHANNEL_MONITORING_PREFIX + apkMd5;
        return channelMonitoringTemplate.opsForList().range(cacheTaskLogKey,0,-1);
    }

    /**
     * 渠道风险监控缓存清理
     * @param apkMd5
     */
    @Override
    public void cleanChannelMonitoringCache(String apkMd5) {
        String cacheTaskLogKey = PinfoConstant.CACHE_CHANNEL_MONITORING_PREFIX + apkMd5;
        if(channelMonitoringTemplate.hasKey(cacheTaskLogKey)){
            channelMonitoringTemplate.delete(cacheTaskLogKey);
            log.info("清理渠道风险监控数据缓存{}",cacheTaskLogKey);
        }
    }
}
