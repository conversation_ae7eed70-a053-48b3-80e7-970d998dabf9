package cn.ijiami.detection.service.impl;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import cn.ijiami.detection.VO.AndroidSensorLog;
import cn.ijiami.detection.bean.DynamicTaskContext;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.service.DetectionDataService;
import cn.ijiami.detection.service.api.IDynamicTaskContextService;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;

import cn.ijiami.detection.VO.RealTimeBehaviorLog;
import cn.ijiami.detection.VO.RealTimeNetLog;
import cn.ijiami.detection.VO.detection.privacy.IOSRealTimeLog;
import cn.ijiami.detection.analyzer.bo.ActionStackBO;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.entity.TDynamicBehaviorImg;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import cn.ijiami.detection.entity.TPrivacySharedPrefs;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.server.client.base.enums.TerminalTypeEnum;
import cn.ijiami.detection.mapper.TActionNougatMapper;
import cn.ijiami.detection.mapper.TDynamicBehaviorImgMapper;
import cn.ijiami.detection.mapper.TPrivacyActionNougatMapper;
import cn.ijiami.detection.mapper.TPrivacyOutsideAddressMapper;
import cn.ijiami.detection.mapper.TPrivacySensitiveWordMapper;
import cn.ijiami.detection.mapper.TPrivacySharedPrefsMapper;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.query.task.TaskLogQuery;
import cn.ijiami.detection.service.api.CacheService;
import cn.ijiami.detection.service.api.IGetActionDetailDataService;
import cn.ijiami.detection.utils.DataHandleUtil;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import lombok.extern.slf4j.Slf4j;

/**
 * 行为展示详情数据公共服务类
 */
@Slf4j
@Service
public class GetActionDetailDataServiceImpl implements IGetActionDetailDataService {
    private static final int PAGE_SIZE = 500;

    private final TPrivacyActionNougatMapper privacyActionNougatMapper;
    private final TPrivacyOutsideAddressMapper privacyOutsideAddressMapper;
    private final TTaskMapper tTaskMapper;
    private final TDynamicBehaviorImgMapper tDynamicBehaviorImgMapper;
    private final TPrivacySensitiveWordMapper privacySensitiveWordMapper;
    private final TPrivacySharedPrefsMapper privacySharedPrefsMapper;
    private final TActionNougatMapper actionNougatMapper;

    private final CacheService cacheService;

    private final DetectionDataService detectionDataService;

    private final IDynamicTaskContextService dynamicTaskContextService;

    public GetActionDetailDataServiceImpl(TPrivacyActionNougatMapper privacyActionNougatMapper, TPrivacyOutsideAddressMapper privacyOutsideAddressMapper,
                                          TTaskMapper tTaskMapper, TDynamicBehaviorImgMapper tDynamicBehaviorImgMapper,
                                          TPrivacySensitiveWordMapper privacySensitiveWordMapper, TPrivacySharedPrefsMapper privacySharedPrefsMapper,
                                          TActionNougatMapper actionNougatMapper, CacheService cacheService, IDynamicTaskContextService dynamicTaskContextService,
                                          DetectionDataService detectionDataService) {
        this.privacyActionNougatMapper = privacyActionNougatMapper;
        this.privacyOutsideAddressMapper = privacyOutsideAddressMapper;
        this.tTaskMapper = tTaskMapper;
        this.tDynamicBehaviorImgMapper = tDynamicBehaviorImgMapper;
        this.privacySensitiveWordMapper = privacySensitiveWordMapper;
        this.actionNougatMapper = actionNougatMapper;
        this.privacySharedPrefsMapper = privacySharedPrefsMapper;
        this.cacheService = cacheService;
        this.dynamicTaskContextService = dynamicTaskContextService;
        this.detectionDataService = detectionDataService;
    }

    @Override
    public Object getActionDetailDataById(Long id, String packageName,Integer type,Long taskId) {
        Object object = null;
        if((id==null || id==0L) || (type==null || type==0)){
            return object;
        }

        //应用行为详情数据查询
        if(type==1){
            TPrivacyActionNougat tPrivacyActionNougat = privacyActionNougatMapper.selectDataById(id,packageName);

            if(tPrivacyActionNougat==null || tPrivacyActionNougat.getTaskId()==null || tPrivacyActionNougat.getId()==null){
                return object;
            }

            //存放截图key信息
            String screenshots =null;
            //存放截图时填的备注信息
            String remarks = null;
            //获取截图相关信息
            List<TDynamicBehaviorImg> tDynamicBehaviorImgs = tDynamicBehaviorImgMapper.findByActionNougatId(tPrivacyActionNougat.getTaskId(),tPrivacyActionNougat.getActionId(),tPrivacyActionNougat.getId(),type);
            //根据时间搓排序，排序之后取第一个数据
            tDynamicBehaviorImgs=tDynamicBehaviorImgs.stream().sorted(Comparator.comparing(TDynamicBehaviorImg::getPictureTimeStamp).reversed()).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(tDynamicBehaviorImgs) && tDynamicBehaviorImgs.size()>0){
                screenshots=tDynamicBehaviorImgs.get(0).getFileKey();
                remarks=tDynamicBehaviorImgs.get(0).getRemarks();
                //将结果赋值给应用行为
                tPrivacyActionNougat.setScreenshots(screenshots);
                tPrivacyActionNougat.setRemarks(remarks);
            }

            object=tPrivacyActionNougat;
            return object;
        }

        //通讯行为详情查询
        if(type==2 && (taskId!=null && taskId != 0L)){
            TPrivacyOutsideAddress tPrivacyOutsideAddress = privacyOutsideAddressMapper.selectDetailDataByOutsideId(id);

            if(tPrivacyOutsideAddress ==null || tPrivacyOutsideAddress.getTaskId()==null || tPrivacyOutsideAddress.getId()==null){
                return object;
            }

            TTask task = tTaskMapper.selectByPrimaryKey(taskId);
            // 兼容堆栈数据处理方式
            if (task.getTerminalType() == TerminalTypeEnum.ANDROID
                    && StringUtils.isNotEmpty(tPrivacyOutsideAddress.getStackInfo())
                    && !PinfoConstant.DETAILS_EMPTY.equals(tPrivacyOutsideAddress.getStackInfo()) && DataHandleUtil.isJSONValid(tPrivacyOutsideAddress.getStackInfo())) {
                for (ActionStackBO stackBO : JSON.parseArray(tPrivacyOutsideAddress.getStackInfo(), ActionStackBO.class)) {
                    tPrivacyOutsideAddress.setStackInfo(StringUtils.isEmpty(tPrivacyOutsideAddress.getStackInfo())?stackBO.getStackInfo():tPrivacyOutsideAddress.getStackInfo());
                    tPrivacyOutsideAddress.setDetailsData(StringUtils.isEmpty(tPrivacyOutsideAddress.getDetailsData())?stackBO.getDetailsData():tPrivacyOutsideAddress.getDetailsData());
                    tPrivacyOutsideAddress.setResponseData(StringUtils.isEmpty(tPrivacyOutsideAddress.getResponseData())?stackBO.getDetailsData():tPrivacyOutsideAddress.getResponseData());
                    tPrivacyOutsideAddress.setStrTime(tPrivacyOutsideAddress.getStrTime()==null?stackBO.getActionTime():tPrivacyOutsideAddress.getStrTime());
                    tPrivacyOutsideAddress.setActionTime(tPrivacyOutsideAddress.getActionTime()==null?stackBO.getActionTime():tPrivacyOutsideAddress.getActionTime());
                }
            }

            //存放截图key信息
            String screenshots =null;
            //存放截图时填的备注信息
            String remarks = null;
            //获取截图相关信息
            List<TDynamicBehaviorImg> tDynamicBehaviorImgs = tDynamicBehaviorImgMapper.findByActionNougatId(tPrivacyOutsideAddress.getTaskId(),null,tPrivacyOutsideAddress.getId(),type);
            //根据时间搓排序，排序之后取第一个数据
            tDynamicBehaviorImgs=tDynamicBehaviorImgs.stream().sorted(Comparator.comparing(TDynamicBehaviorImg::getPictureTimeStamp).reversed()).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(tDynamicBehaviorImgs) && tDynamicBehaviorImgs.size()>0){
                screenshots=tDynamicBehaviorImgs.get(0).getFileKey();
                remarks=tDynamicBehaviorImgs.get(0).getRemarks();
                tPrivacyOutsideAddress.setScreenshots(screenshots);
                tPrivacyOutsideAddress.setRemarks(remarks);
            }
            if (StringUtils.isBlank(tPrivacyOutsideAddress.getDetailsData())) {
                tPrivacyOutsideAddress.setDetailsData(PinfoConstant.DETAILS_EMPTY);
            } else if (task.getTerminalType() == TerminalTypeEnum.ANDROID) {
                tPrivacyOutsideAddress.setDetailsData(tPrivacyOutsideAddress.getDetailsData());
            }
            if (StringUtils.isBlank(tPrivacyOutsideAddress.getResponseData())) {
                tPrivacyOutsideAddress.setResponseData(PinfoConstant.DETAILS_EMPTY);
            } else if (task.getTerminalType() == TerminalTypeEnum.ANDROID) {
                tPrivacyOutsideAddress.setResponseData(tPrivacyOutsideAddress.getResponseData());
            }
            object=tPrivacyOutsideAddress;
            return object;
        }

        //传输个人信息行为详情查询
        if(type==3 && (taskId!=null && taskId != 0L)){
            TPrivacySensitiveWord tPrivacySensitiveWord=privacySensitiveWordMapper.selectDataById(id);

            if(tPrivacySensitiveWord==null || tPrivacySensitiveWord.getTaskId()==null || tPrivacySensitiveWord.getId()==null){
                return object;
            }

            TTask task = tTaskMapper.selectByPrimaryKey(taskId);

            String tmp = "<span class=\"font-red\" style=\"color:red\">{{word}}</span>";
            // 匹配的关键词
            String word = tPrivacySensitiveWord.getSensitiveWord().toLowerCase();
            if (word.endsWith(":")) {
                word = word.substring(0, word.indexOf(":"));
            }
            // 符合条件的代码
            String code = tPrivacySensitiveWord.getCode().toLowerCase();
            if (StringUtils.isNotBlank(code)) {
                int idx = code.indexOf(word);
                if (idx >= 0) {
                    String head = tPrivacySensitiveWord.getCode().substring(0, idx);
                    String tail = tPrivacySensitiveWord.getCode().substring(idx + word.length());
                    String newWord = tmp.replace("{{word}}", tPrivacySensitiveWord.getSensitiveWord());
                    String newCode = StringEscapeUtils.escapeHtml4(head) + newWord + StringEscapeUtils.escapeHtml4(tail);
                    tPrivacySensitiveWord.setCode(newCode);
                }
                if (StringUtils.contains(tPrivacySensitiveWord.getMethod(), "\n")) {
                    tPrivacySensitiveWord.setMethod(tPrivacySensitiveWord.getMethod().split("\n")[1]);
                }
                //Android设备类型特殊处理
                String stackInfo = tPrivacySensitiveWord.getStackInfo();
                if (task.getTerminalType() == TerminalTypeEnum.ANDROID && StringUtils.isNotBlank(stackInfo) && !PinfoConstant.DETAILS_EMPTY.equals(stackInfo) && DataHandleUtil.isJSONValid(stackInfo)) {
                    ActionStackBO actionStackBO = JSON.parseArray(stackInfo, ActionStackBO.class).get(0);
                    tPrivacySensitiveWord.setStackInfo(StringUtils.isEmpty(tPrivacySensitiveWord.getStackInfo())?actionStackBO.getStackInfo():tPrivacySensitiveWord.getStackInfo());
                    tPrivacySensitiveWord.setDetailsData(StringUtils.isEmpty(tPrivacySensitiveWord.getDetailsData())?actionStackBO.getDetailsData():tPrivacySensitiveWord.getDetailsData());
                    tPrivacySensitiveWord.setActionTime(tPrivacySensitiveWord.getActionTime()==null?actionStackBO.getActionTime():tPrivacySensitiveWord.getActionTime());
                }
            }

            //存放截图key信息
            String screenshots =null;
            //存放截图时填的备注信息
            String remarks = null;
            //获取截图相关信息
            List<TDynamicBehaviorImg> tDynamicBehaviorImgs = tDynamicBehaviorImgMapper.findByActionNougatId(tPrivacySensitiveWord.getTaskId(),null,tPrivacySensitiveWord.getId(),type);
            //根据时间搓排序，排序之后取第一个数据
            tDynamicBehaviorImgs=tDynamicBehaviorImgs.stream().sorted(Comparator.comparing(TDynamicBehaviorImg::getPictureTimeStamp).reversed()).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(tDynamicBehaviorImgs) && tDynamicBehaviorImgs.size()>0){
                screenshots=tDynamicBehaviorImgs.get(0).getFileKey();
                remarks=tDynamicBehaviorImgs.get(0).getRemarks();
                tPrivacySensitiveWord.setScreenshots(screenshots);
                tPrivacySensitiveWord.setRemarks(remarks);
            }
            if (StringUtils.isBlank(tPrivacySensitiveWord.getDetailsData())) {
                tPrivacySensitiveWord.setDetailsData(PinfoConstant.DETAILS_EMPTY);
            } else if (task.getTerminalType() == TerminalTypeEnum.ANDROID) {
                tPrivacySensitiveWord.setDetailsData(tPrivacySensitiveWord.getDetailsData());
            }
            if (StringUtils.isBlank(tPrivacySensitiveWord.getResponseData())) {
                tPrivacySensitiveWord.setResponseData(PinfoConstant.DETAILS_EMPTY);
            } else if (task.getTerminalType() == TerminalTypeEnum.ANDROID) {
                tPrivacySensitiveWord.setResponseData(tPrivacySensitiveWord.getResponseData());
            }
            object=tPrivacySensitiveWord;
            return object;
        }

        //存储个人信息详情查询
        if(type==4){
            TPrivacySharedPrefs tPrivacySharedPrefs = privacySharedPrefsMapper.selectDataById(id);
            if(tPrivacySharedPrefs==null || tPrivacySharedPrefs.getTaskId()==null || tPrivacySharedPrefs.getId()==null){
                return object;
            }
            TTask task = tTaskMapper.selectByPrimaryKey(taskId);

            String tmp = "<span class=\"font-red\" style=\"color:red\">{{word}}</span>";
            String word = tPrivacySharedPrefs.getSensitiveWord().toLowerCase();
            String code = tPrivacySharedPrefs.getContent().toLowerCase();
            int idx = code.indexOf(word);
            if (idx >= 0) {
                String head = tPrivacySharedPrefs.getContent().substring(0, idx);
                String tail = tPrivacySharedPrefs.getContent().substring(idx + word.length());
                String newWord = tmp.replace("{{word}}", tPrivacySharedPrefs.getSensitiveWord());
                String newCode = StringEscapeUtils.escapeHtml4(head) + newWord + StringEscapeUtils.escapeHtml4(tail);
                tPrivacySharedPrefs.setContent(newCode);
            }
            // 兼容之前的数据
            String stackInfo = tPrivacySharedPrefs.getStackInfo();
            if (org.apache.commons.lang.StringUtils.isNotBlank(stackInfo) && !PinfoConstant.DETAILS_EMPTY.equals(stackInfo) && DataHandleUtil.isJSONValid(stackInfo) && task.getTerminalType() == TerminalTypeEnum.ANDROID) {
                ActionStackBO actionStackBO = JSON.parseArray(stackInfo, ActionStackBO.class).get(0);
                tPrivacySharedPrefs.setActionTime(tPrivacySharedPrefs.getActionTime()==null?actionStackBO.getActionTime():tPrivacySharedPrefs.getActionTime());
                tPrivacySharedPrefs.setStackInfo(StringUtils.isEmpty(tPrivacySharedPrefs.getStackInfo())?actionStackBO.getStackInfo():tPrivacySharedPrefs.getStackInfo());
            }

            //存放截图key信息
            String screenshots =null;
            //存放截图时填的备注信息
            String remarks = null;
            //获取截图相关信息
            List<TDynamicBehaviorImg> tDynamicBehaviorImgs = tDynamicBehaviorImgMapper.findByActionNougatId(tPrivacySharedPrefs.getTaskId(),null,tPrivacySharedPrefs.getId(),type);
            //根据时间搓排序，排序之后取第一个数据
            tDynamicBehaviorImgs=tDynamicBehaviorImgs.stream().sorted(Comparator.comparing(TDynamicBehaviorImg::getPictureTimeStamp).reversed()).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(tDynamicBehaviorImgs) && tDynamicBehaviorImgs.size()>0){
                screenshots=tDynamicBehaviorImgs.get(0).getFileKey();
                remarks=tDynamicBehaviorImgs.get(0).getRemarks();
                tPrivacySharedPrefs.setScreenshots(screenshots);
                tPrivacySharedPrefs.setRemarks(remarks);
            }
            object=tPrivacySharedPrefs;
            return object;
        }

        return object;
    }

    @Override
    public RealTimeBehaviorLog getActionDetail(String id, Long taskId) throws IjiamiRuntimeException {
        return detectionDataService.getActionById(Long.parseLong(id));
    }

    @Override
    public List<RealTimeBehaviorLog> getActionListInActionIds(Long taskId, List<Long> actionIdList, List<PrivacyStatusEnum> isPersonalList, List<BehaviorStageEnum> behaviorStageList) {
        return detectionDataService.getActionPage(taskId, actionIdList, isPersonalList, behaviorStageList, 1, PAGE_SIZE).getList();
    }

    @Override
    public PageInfo<RealTimeBehaviorLog> getDynamicLogListByPage(TaskLogQuery query) {
        log.info("start read page page={} rows={}", query.getPage(), query.getRows());
        PageInfo<RealTimeBehaviorLog> msgList = detectionDataService.getActionPage(query.getTaskId(), null, null, null,
                query.getPage(), query.getRows());
        msgList.getList()
                .forEach(log -> {
                    // 移除堆栈信息
                    log.setDetailsData(null);
                    log.setStackInfo(null);
                });
        log.info("start read page end");
        return msgList;
    }

    @Override
    public List<TActionNougat> getDynamicActionList(Long taskId) {
        List<String> actionIdList = detectionDataService.getActionTypeList(taskId);
        if (actionIdList.isEmpty()) {
            return Collections.emptyList();
        } else {
            return actionNougatMapper.findInActionId(actionIdList);
        }
    }

    @Override
    public List<String> getIosDynamicActionList(Long taskId) {
        return detectionDataService.getIosActionTypeList(taskId);
    }

    @Override
    public IOSRealTimeLog getIosDetailLog(String id, Long taskId) throws IjiamiRuntimeException {
        return detectionDataService.getIosActionById(Long.parseLong(id));
    }

    @Override
    public List<IOSRealTimeLog> getIosDetailLogList(List<String> typeList, List<Integer> behaviorStageList, List<Integer> isPersonalList, Long taskId) {
        List<Long> actionIdList = actionNougatMapper.findActionIdByNames(typeList, TerminalTypeEnum.IOS);
        List<PrivacyStatusEnum> isPersonal = isPersonalList.stream().map(PrivacyStatusEnum::getItem).collect(Collectors.toList());
        List<BehaviorStageEnum> behaviorStage = behaviorStageList.stream().map(BehaviorStageEnum::getItem).collect(Collectors.toList());
        PageInfo<IOSRealTimeLog> logList = detectionDataService.getIosActionPage(taskId, actionIdList, isPersonal, behaviorStage, 1, PAGE_SIZE);
        return logList.getList();
    }

    @Override
    public PageInfo<IOSRealTimeLog> getIosDetailLogListByPage(TaskLogQuery query) {
        log.info("start read page page={} rows={}", query.getPage(), query.getRows());
        PageInfo<IOSRealTimeLog> msgPage = detectionDataService.getIosActionPage(query.getTaskId(),
                Collections.emptyList(), Collections.emptyList(), Collections.emptyList(), query.getPage(), query.getRows());
        log.info("start read page end");
        msgPage.setList(removeDetail(msgPage.getList().stream()));
        return msgPage;
    }

    @Override
    public RealTimeNetLog getNetLog(String id, Long taskId) throws IjiamiRuntimeException {
        return detectionDataService.getNetActionById(Long.parseLong(id));
    }

    @Override
    public PageInfo<RealTimeNetLog> getNetLogListByPage(TaskLogQuery query) {
        int from = query.getRows() * (query.getPage() - 1);
        int to = from + (query.getRows() - 1);
        if (to <= from || from < 0) {
            throw new IllegalArgumentException("行数或页数错误");
        }
        log.info("start read page from={} to={}", from, to);
        PageInfo<RealTimeNetLog> msgPage = detectionDataService.getNetActionPage(query.getTaskId(), query.getPage(), query.getRows());
        log.info("start read page end");
        msgPage.getList().forEach(log -> {
            // 移除堆栈信息
            log.setResponseData("");
            log.setRequestData("");
        });
        return msgPage;
    }

    @Override
    public PageInfo<AndroidSensorLog> getSensorLogListByPage(TaskLogQuery query) {
        int from = query.getRows() * (query.getPage() - 1);
        int to = from + (query.getRows() - 1);
        if (to <= from || from < 0) {
            throw new IllegalArgumentException("行数或页数错误");
        }
        log.info("start read page from={} to={}", from, to);
        List<AndroidSensorLog> msgList = cacheService.getSensorLogList(query.getTaskId(), from, to);
        Long msgListTotalSize = cacheService.getSensorLogListSize(query.getTaskId());
        log.info("start read page end");
        List<AndroidSensorLog> logList = msgList.stream()
                .sorted(Comparator.comparingLong(AndroidSensorLog::getActionTime).reversed())
                .collect(Collectors.toList());
        return makePageInfo(logList, query, msgListTotalSize);
    }

    private <T> PageInfo<T> makePageInfo(List<T> logList, TaskLogQuery query, Long msgListTotalSize) {
        PageInfo<T> page = new PageInfo<>();
        page.setList(logList);
        page.setSize(logList.size());
        page.setPageNum(query.getPage());
        page.setPageSize(query.getRows());
        page.setTotal(msgListTotalSize);
        page.setPages((int) (msgListTotalSize / query.getRows() + 1));
        return page;
    }

    private List<IOSRealTimeLog> removeDetail(Stream<IOSRealTimeLog> stream) {
        return stream.peek(log -> {
            // 移除堆栈信息
            log.setStrTrigger(StringUtils.EMPTY);
            log.setStrInfo(StringUtils.EMPTY);
        }).collect(Collectors.toList());
    }

    protected static Long parseLogTime(IOSRealTimeLog iosLog, DateTimeFormatter millisFormatter, DateTimeFormatter secondFormatter) {
        try {
            return LocalDateTime.parse(iosLog.getStrTime(), millisFormatter).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        } catch (Exception e) {
            // 有可能是毫秒数缺失
            int last = iosLog.getStrTime().lastIndexOf(".");
            if (last > 0) {
                String newDate = iosLog.getStrTime().substring(0, last);
                try {
                    return LocalDateTime.parse(newDate, secondFormatter).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                } catch (Exception e2) {
                    log.error("parseLogTime failure", e2);
                    return 0L;
                }
            } else {
                log.error("parseLogTime failure", e);
                return 0L;
            }
        }
    }

    @Override
    public void cleanDynamicAction(Long taskId) {
        TTask task = tTaskMapper.selectByPrimaryKey(taskId);
        if (task == null) {
            log.info("任务不存在 {}", taskId);
            return;
        }
        if (task.getTerminalType() == TerminalTypeEnum.IOS) {
            // 拦截掉清除后1秒内的日志
            cacheService.setLong(PinfoConstant.INTERDICTED_ACTION + taskId, System.currentTimeMillis(), 1L, TimeUnit.SECONDS);
            detectionDataService.cleanDynamicAction(taskId);
            DynamicTaskContext taskContext = dynamicTaskContextService.getTaskContext(taskId);
            if (Objects.nonNull(taskContext)) {
                if (Objects.nonNull(taskContext.getLogCtrl())) {
                    taskContext.getLogCtrl().cleanAllData();
                }
            }
        } else {
            // 拦截掉清除后4秒内的日志，等待idb传回清理完成的日志
            cacheService.setLong(PinfoConstant.INTERDICTED_ACTION + taskId, System.currentTimeMillis(), 4L, TimeUnit.SECONDS);
            detectionDataService.cleanDynamicAction(taskId);
        }
    }
}
