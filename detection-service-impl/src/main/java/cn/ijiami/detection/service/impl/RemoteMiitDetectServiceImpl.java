package cn.ijiami.detection.service.impl;

import cn.ijiami.detection.VO.CountLawDetectResult;
import cn.ijiami.detection.VO.LawActionDetailVO;
import cn.ijiami.detection.VO.LawDetectDetailVO;
import cn.ijiami.detection.VO.LawDetectResultVO;
import cn.ijiami.detection.VO.detection.privacy.ComplianceVO;
import cn.ijiami.detection.android.client.api.AndroidMiitDetectServiceApi;
import cn.ijiami.detection.android.client.api.IosMiitDetectServiceApi;
import cn.ijiami.detection.query.DeepDetectionFinish;
import cn.ijiami.detection.query.LawActionDetailQuery;
import cn.ijiami.detection.query.LawItemResultDetailQuery;
import cn.ijiami.detection.server.client.base.dto.CountLawDetectResultDTO;
import cn.ijiami.detection.server.client.base.dto.LawDetectResultDTO;
import cn.ijiami.detection.server.client.base.enums.LawResultRiskLevelEnum;
import cn.ijiami.detection.server.client.base.enums.TerminalTypeEnum;
import cn.ijiami.detection.service.api.IMiitDetectService;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import cn.ijiami.privacy.applet.api.AppletMiitDetectServiceApi;
import cn.ijiami.privacy.harmony.client.api.HarmonyMiitDetectServiceApi;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static cn.ijiami.detection.utils.PrivacyLawIdUtils.getTerminalTypeByLawId;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RemoteMiitDetectServiceImpl.java
 * @Description 远程调用法规查询api
 * @createTime 2025年07月02日 11:15:00
 */
@Service
public class RemoteMiitDetectServiceImpl implements IMiitDetectService {

    @Autowired
    private AndroidMiitDetectServiceApi androidMiitDetectServiceApi;

    @Autowired
    private IosMiitDetectServiceApi iosMiitDetectServiceApi;

    @Autowired
    private AppletMiitDetectServiceApi appletMiitDetectServiceApi;

    @Autowired
    private HarmonyMiitDetectServiceApi harmonyMiitDetectServiceApi;

    @Override
    public CountLawDetectResult findLawDetectResultByTaskId(Long lawId, Long taskId) {
        return findLawDetectResultByTaskId(lawId, taskId, null, false);
    }

    private CountLawDetectResult convertCountLawDetectResult(CountLawDetectResultDTO countLawDetectResultDTO) {
        if (countLawDetectResultDTO == null) {
            return null;
        }
        CountLawDetectResult countLawDetectResult = new CountLawDetectResult();
        BeanUtils.copyProperties(countLawDetectResultDTO, countLawDetectResult);
        countLawDetectResult.setLawDetectResult(convertLawDetectResultVO(countLawDetectResultDTO.getLawDetectResult()));
        return countLawDetectResult;
    }

    private LawDetectResultVO convertLawDetectResultVO(LawDetectResultDTO lawDetectResultDTO) {
        if (lawDetectResultDTO == null) {
            return null;
        }
        LawDetectResultVO lawDetectResult = new LawDetectResultVO();
        BeanUtils.copyProperties(lawDetectResultDTO, lawDetectResult);
        if (CollectionUtils.isNotEmpty(lawDetectResultDTO.getNextLaws())) {
            lawDetectResult.setNextLaws(new ArrayList<>());
            lawDetectResultDTO.getNextLaws().forEach(item -> {
                lawDetectResult.getNextLaws().add(convertLawDetectResultVO(item));
            });
        }
        return lawDetectResult;
    }

    @Override
    public CountLawDetectResult findLawDetectResultByTaskId(Long lawId, Long taskId, Integer resultStatus, boolean isBuildReport) {
        TerminalTypeEnum terminalTypeEnum = getTerminalTypeByLawId(lawId.intValue());
        if (terminalTypeEnum == TerminalTypeEnum.ANDROID) {
            return convertCountLawDetectResult(androidMiitDetectServiceApi.findLawDetectResultByTaskId(lawId,
                    taskId, resultStatus, isBuildReport));
        } else if (terminalTypeEnum == TerminalTypeEnum.IOS) {
            return convertCountLawDetectResult(iosMiitDetectServiceApi.findLawDetectResultByTaskId(lawId,
                    taskId, resultStatus, isBuildReport));
        } else if (terminalTypeEnum.isApplet()) {
            return convertCountLawDetectResult(appletMiitDetectServiceApi.findLawDetectResultByTaskId(lawId,
                    taskId, resultStatus, isBuildReport));
        } else if (terminalTypeEnum == TerminalTypeEnum.HARMONY) {
            return convertCountLawDetectResult(harmonyMiitDetectServiceApi.findLawDetectResultByTaskId(lawId,
                    taskId, resultStatus, isBuildReport));
        } else {
            throw new IjiamiRuntimeException("错误的参数");
        }
    }

    @Override
    public CountLawDetectResult findLawDetectResultByTaskId(Long lawId, Long taskId, Integer resultStatus) {
        return findLawDetectResultByTaskId(lawId, taskId, resultStatus, false);
    }

    @Override
    public LawDetectDetailVO findDetailPageByTaskIdAndItemNo(LawItemResultDetailQuery query) {
        return null;
    }

    @Override
    public PageInfo<LawActionDetailVO> findLawActionDetailByPage(LawActionDetailQuery query) {
        return null;
    }

    @Override
    public List<Map<String, Object>> getLawList(Long task) {
        return Collections.emptyList();
    }

    @Override
    public Map<String, LawDetectDetailVO> findAllItemByTaskId(Long taskId, Integer lawId) {
        return Collections.emptyMap();
    }

    @Override
    public void updateComplianceStatus(ComplianceVO compliance) throws IjiamiApplicationException {

    }

    @Override
    public void updateComplianceStatus(Long taskId, List<ComplianceVO> list) {

    }

    @Override
    public void changeLawResultRiskLevel(Long taskId, String itemNo, LawResultRiskLevelEnum level) {

    }

    @Override
    public void updateReviewTaskComplianceStatus(Long taskId) throws IjiamiApplicationException {

    }

    @Override
    public void setDeepDetectionFinish(DeepDetectionFinish finish) {

    }

    @Override
    public LawDetectResultVO findLawDetectResultItem(Long taskId, String itemNo) {
        return null;
    }
}
