package cn.ijiami.detection.service.impl;

import cn.ijiami.detection.VO.CountLawDetectResult;
import cn.ijiami.detection.VO.LawActionDetailVO;
import cn.ijiami.detection.VO.LawDetectDetailVO;
import cn.ijiami.detection.VO.LawDetectResultVO;
import cn.ijiami.detection.VO.detection.privacy.ComplianceVO;
import cn.ijiami.detection.android.client.api.AndroidMiitDetectServiceApi;
import cn.ijiami.detection.android.client.api.IosMiitDetectServiceApi;
import cn.ijiami.detection.query.DeepDetectionFinish;
import cn.ijiami.detection.query.LawActionDetailQuery;
import cn.ijiami.detection.query.LawItemResultDetailQuery;
import cn.ijiami.detection.server.client.base.dto.CountLawDetectResultDTO;
import cn.ijiami.detection.server.client.base.dto.LawActionDetailDTO;
import cn.ijiami.detection.server.client.base.dto.LawDetectDetailDTO;
import cn.ijiami.detection.server.client.base.dto.LawDetectResultDTO;
import cn.ijiami.detection.server.client.base.enums.LawResultRiskLevelEnum;
import cn.ijiami.detection.server.client.base.enums.TerminalTypeEnum;
import cn.ijiami.detection.service.api.IMiitDetectService;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import cn.ijiami.privacy.applet.api.AppletMiitDetectServiceApi;
import cn.ijiami.privacy.harmony.client.api.HarmonyMiitDetectServiceApi;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.ijiami.detection.utils.PrivacyLawIdUtils.getTerminalTypeByLawId;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RemoteMiitDetectServiceImpl.java
 * @Description 远程调用法规查询api，通过Feign调用远程Android、iOS、小程序和鸿蒙检测服务实现法规检测功能
 * @createTime 2025年07月02日 11:15:00
 */
@Slf4j
@Service
public class RemoteMiitDetectServiceImpl implements IMiitDetectService {

    @Autowired
    private AndroidMiitDetectServiceApi androidMiitDetectServiceApi;

    @Autowired
    private IosMiitDetectServiceApi iosMiitDetectServiceApi;

    @Autowired
    private AppletMiitDetectServiceApi appletMiitDetectServiceApi;

    @Autowired
    private HarmonyMiitDetectServiceApi harmonyMiitDetectServiceApi;

    @Override
    public CountLawDetectResult findLawDetectResultByTaskId(Long lawId, Long taskId) {
        return findLawDetectResultByTaskId(lawId, taskId, null, false);
    }

    private CountLawDetectResult convertCountLawDetectResult(CountLawDetectResultDTO countLawDetectResultDTO) {
        if (countLawDetectResultDTO == null) {
            return null;
        }
        CountLawDetectResult countLawDetectResult = new CountLawDetectResult();
        BeanUtils.copyProperties(countLawDetectResultDTO, countLawDetectResult);
        countLawDetectResult.setLawDetectResult(convertLawDetectResultVO(countLawDetectResultDTO.getLawDetectResult()));
        return countLawDetectResult;
    }

    private LawDetectResultVO convertLawDetectResultVO(LawDetectResultDTO lawDetectResultDTO) {
        if (lawDetectResultDTO == null) {
            return null;
        }
        LawDetectResultVO lawDetectResult = new LawDetectResultVO();
        BeanUtils.copyProperties(lawDetectResultDTO, lawDetectResult);
        if (CollectionUtils.isNotEmpty(lawDetectResultDTO.getNextLaws())) {
            lawDetectResult.setNextLaws(new ArrayList<>());
            lawDetectResultDTO.getNextLaws().forEach(item -> {
                lawDetectResult.getNextLaws().add(convertLawDetectResultVO(item));
            });
        }
        return lawDetectResult;
    }

    @Override
    public CountLawDetectResult findLawDetectResultByTaskId(Long lawId, Long taskId, Integer resultStatus, boolean isBuildReport) {
        TerminalTypeEnum terminalTypeEnum = getTerminalTypeByLawId(lawId.intValue());
        if (terminalTypeEnum == TerminalTypeEnum.ANDROID) {
            return convertCountLawDetectResult(androidMiitDetectServiceApi.findLawDetectResultByTaskId(lawId,
                    taskId, resultStatus, isBuildReport));
        } else if (terminalTypeEnum == TerminalTypeEnum.IOS) {
            return convertCountLawDetectResult(iosMiitDetectServiceApi.findLawDetectResultByTaskId(lawId,
                    taskId, resultStatus, isBuildReport));
        } else if (terminalTypeEnum.isApplet()) {
            return convertCountLawDetectResult(appletMiitDetectServiceApi.findLawDetectResultByTaskId(lawId,
                    taskId, resultStatus, isBuildReport));
        } else if (terminalTypeEnum == TerminalTypeEnum.HARMONY) {
            return convertCountLawDetectResult(harmonyMiitDetectServiceApi.findLawDetectResultByTaskId(lawId,
                    taskId, resultStatus, isBuildReport));
        } else {
            throw new IjiamiRuntimeException("错误的参数");
        }
    }

    @Override
    public CountLawDetectResult findLawDetectResultByTaskId(Long lawId, Long taskId, Integer resultStatus) {
        return findLawDetectResultByTaskId(lawId, taskId, resultStatus, false);
    }

    @Override
    public LawDetectDetailVO findDetailPageByTaskIdAndItemNo(LawItemResultDetailQuery query) {
        log.info("远程获取法规检测详情，query：{}", query);
        try {
            // 尝试从各个服务获取法规检测详情，返回第一个非空结果
            LawDetectDetailVO androidResult = null;
            try {
                androidResult = convertLawDetectDetailDTO(androidMiitDetectServiceApi.findDetailPageByTaskIdAndItemNo(convertToLawItemResultDetailParam(query)));
            } catch (Exception e) {
                log.warn("从Android服务获取法规检测详情失败，query：{}", query, e);
            }

            if (androidResult != null) {
                return androidResult;
            }

            LawDetectDetailVO iosResult = null;
            try {
                iosResult = convertLawDetectDetailDTO(iosMiitDetectServiceApi.findDetailPageByTaskIdAndItemNo(convertToLawItemResultDetailParam(query)));
            } catch (Exception e) {
                log.warn("从iOS服务获取法规检测详情失败，query：{}", query, e);
            }

            if (iosResult != null) {
                return iosResult;
            }

            LawDetectDetailVO appletResult = null;
            try {
                appletResult = convertLawDetectDetailDTO(appletMiitDetectServiceApi.findDetailPageByTaskIdAndItemNo(convertToLawItemResultDetailParam(query)));
            } catch (Exception e) {
                log.warn("从小程序服务获取法规检测详情失败，query：{}", query, e);
            }

            if (appletResult != null) {
                return appletResult;
            }

            LawDetectDetailVO harmonyResult = null;
            try {
                harmonyResult = convertLawDetectDetailDTO(harmonyMiitDetectServiceApi.findDetailPageByTaskIdAndItemNo(convertToLawItemResultDetailParam(query)));
            } catch (Exception e) {
                log.warn("从鸿蒙服务获取法规检测详情失败，query：{}", query, e);
            }

            return harmonyResult;
        } catch (Exception e) {
            log.error("远程获取法规检测详情失败，query：{}", query, e);
            return null;
        }
    }

    @Override
    public PageInfo<LawActionDetailVO> findLawActionDetailByPage(LawActionDetailQuery query) {
        log.info("远程分页获取法规行为详情，query：{}", query);
        try {
            // 尝试从各个服务获取法规行为详情，返回第一个非空结果
            PageInfo<LawActionDetailVO> androidResult = null;
            try {
                androidResult = convertLawActionDetailPageInfo(androidMiitDetectServiceApi.findLawActionDetailByPage(convertToLawActionDetailParam(query)));
            } catch (Exception e) {
                log.warn("从Android服务获取法规行为详情失败，query：{}", query, e);
            }

            if (androidResult != null && !androidResult.getList().isEmpty()) {
                return androidResult;
            }

            PageInfo<LawActionDetailVO> iosResult = null;
            try {
                iosResult = convertLawActionDetailPageInfo(iosMiitDetectServiceApi.findLawActionDetailByPage(convertToLawActionDetailParam(query)));
            } catch (Exception e) {
                log.warn("从iOS服务获取法规行为详情失败，query：{}", query, e);
            }

            if (iosResult != null && !iosResult.getList().isEmpty()) {
                return iosResult;
            }

            PageInfo<LawActionDetailVO> appletResult = null;
            try {
                appletResult = convertLawActionDetailPageInfo(appletMiitDetectServiceApi.findLawActionDetailByPage(convertToLawActionDetailParam(query)));
            } catch (Exception e) {
                log.warn("从小程序服务获取法规行为详情失败，query：{}", query, e);
            }

            if (appletResult != null && !appletResult.getList().isEmpty()) {
                return appletResult;
            }

            PageInfo<LawActionDetailVO> harmonyResult = null;
            try {
                harmonyResult = convertLawActionDetailPageInfo(harmonyMiitDetectServiceApi.findLawActionDetailByPage(convertToLawActionDetailParam(query)));
            } catch (Exception e) {
                log.warn("从鸿蒙服务获取法规行为详情失败，query：{}", query, e);
            }

            return harmonyResult != null ? harmonyResult : PageInfo.emptyPageInfo();
        } catch (Exception e) {
            log.error("远程分页获取法规行为详情失败，query：{}", query, e);
            return PageInfo.emptyPageInfo();
        }
    }

    @Override
    public List<Map<String, Object>> getLawList(Long taskId) {
        log.info("远程获取法规列表，taskId：{}", taskId);
        try {
            // 尝试从各个服务获取法规列表，返回第一个非空结果
            List<Map<String, Object>> androidResult = null;
            try {
                androidResult = androidMiitDetectServiceApi.getLawList(taskId);
            } catch (Exception e) {
                log.warn("从Android服务获取法规列表失败，taskId：{}", taskId, e);
            }

            if (androidResult != null && !androidResult.isEmpty()) {
                return androidResult;
            }

            List<Map<String, Object>> iosResult = null;
            try {
                iosResult = iosMiitDetectServiceApi.getLawList(taskId);
            } catch (Exception e) {
                log.warn("从iOS服务获取法规列表失败，taskId：{}", taskId, e);
            }

            if (iosResult != null && !iosResult.isEmpty()) {
                return iosResult;
            }

            List<Map<String, Object>> appletResult = null;
            try {
                appletResult = appletMiitDetectServiceApi.getLawList(taskId);
            } catch (Exception e) {
                log.warn("从小程序服务获取法规列表失败，taskId：{}", taskId, e);
            }

            if (appletResult != null && !appletResult.isEmpty()) {
                return appletResult;
            }

            List<Map<String, Object>> harmonyResult = null;
            try {
                harmonyResult = harmonyMiitDetectServiceApi.getLawList(taskId);
            } catch (Exception e) {
                log.warn("从鸿蒙服务获取法规列表失败，taskId：{}", taskId, e);
            }

            return harmonyResult != null ? harmonyResult : Collections.emptyList();
        } catch (Exception e) {
            log.error("远程获取法规列表失败，taskId：{}", taskId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public Map<String, LawDetectDetailVO> findAllItemByTaskId(Long taskId, Integer lawId) {
        log.info("远程获取任务所有法规检测项，taskId：{}，lawId：{}", taskId, lawId);
        try {
            TerminalTypeEnum terminalTypeEnum = getTerminalTypeByLawId(lawId);
            if (terminalTypeEnum == TerminalTypeEnum.ANDROID) {
                return convertLawDetectDetailMap(androidMiitDetectServiceApi.findAllItemByTaskId(taskId, lawId));
            } else if (terminalTypeEnum == TerminalTypeEnum.IOS) {
                return convertLawDetectDetailMap(iosMiitDetectServiceApi.findAllItemByTaskId(taskId, lawId));
            } else if (terminalTypeEnum.isApplet()) {
                return convertLawDetectDetailMap(appletMiitDetectServiceApi.findAllItemByTaskId(taskId, lawId));
            } else if (terminalTypeEnum == TerminalTypeEnum.HARMONY) {
                return convertLawDetectDetailMap(harmonyMiitDetectServiceApi.findAllItemByTaskId(taskId, lawId));
            } else {
                throw new IjiamiRuntimeException("错误的参数");
            }
        } catch (Exception e) {
            log.error("远程获取任务所有法规检测项失败，taskId：{}，lawId：{}", taskId, lawId, e);
            return Collections.emptyMap();
        }
    }

    @Override
    public void updateComplianceStatus(ComplianceVO compliance) throws IjiamiApplicationException {
        log.info("远程更新合规状态，compliance：{}", compliance);
        try {
            // 同时调用所有服务更新合规状态
            androidMiitDetectServiceApi.updateComplianceStatus(convertToComplianceParam(compliance));
            iosMiitDetectServiceApi.updateComplianceStatus(convertToComplianceParam(compliance));
            appletMiitDetectServiceApi.updateComplianceStatus(convertToComplianceParam(compliance));
            harmonyMiitDetectServiceApi.updateComplianceStatus(convertToComplianceParam(compliance));
        } catch (Exception e) {
            log.error("远程更新合规状态失败，compliance：{}", compliance, e);
            throw new IjiamiApplicationException("远程更新合规状态失败：" + e.getMessage());
        }
    }

    @Override
    public void updateComplianceStatus(Long taskId, List<ComplianceVO> list) {
        log.info("远程批量更新合规状态，taskId：{}，list size：{}", taskId, list != null ? list.size() : 0);
        try {
            // 同时调用所有服务批量更新合规状态
            androidMiitDetectServiceApi.updateComplianceStatus(taskId, convertToComplianceListParam(taskId, list));
            iosMiitDetectServiceApi.updateComplianceStatus(taskId, convertToComplianceListParam(taskId, list));
            appletMiitDetectServiceApi.updateComplianceStatus(taskId, convertToComplianceListParam(taskId, list));
            harmonyMiitDetectServiceApi.updateComplianceStatus(taskId, convertToComplianceListParam(taskId, list));
        } catch (Exception e) {
            log.error("远程批量更新合规状态失败，taskId：{}，list size：{}", taskId, list != null ? list.size() : 0, e);
        }
    }

    @Override
    public void changeLawResultRiskLevel(Long taskId, String itemNo, LawResultRiskLevelEnum level) {
        log.info("远程修改法规结果风险等级，taskId：{}，itemNo：{}，level：{}", taskId, itemNo, level);
        try {
            // 同时调用所有服务修改风险等级
            androidMiitDetectServiceApi.changeLawResultRiskLevel(taskId, itemNo, level);
            iosMiitDetectServiceApi.changeLawResultRiskLevel(taskId, itemNo, level);
            appletMiitDetectServiceApi.changeLawResultRiskLevel(taskId, itemNo, level);
            harmonyMiitDetectServiceApi.changeLawResultRiskLevel(taskId, itemNo, level);
        } catch (Exception e) {
            log.error("远程修改法规结果风险等级失败，taskId：{}，itemNo：{}，level：{}", taskId, itemNo, level, e);
        }
    }

    @Override
    public void updateReviewTaskComplianceStatus(Long taskId) throws IjiamiApplicationException {
        log.info("远程更新审核任务合规状态，taskId：{}", taskId);
        try {
            // 同时调用所有服务更新审核任务合规状态
            androidMiitDetectServiceApi.updateReviewTaskComplianceStatus(taskId);
            iosMiitDetectServiceApi.updateReviewTaskComplianceStatus(taskId);
            appletMiitDetectServiceApi.updateReviewTaskComplianceStatus(taskId);
            harmonyMiitDetectServiceApi.updateReviewTaskComplianceStatus(taskId);
        } catch (Exception e) {
            log.error("远程更新审核任务合规状态失败，taskId：{}", taskId, e);
            throw new IjiamiApplicationException("远程更新审核任务合规状态失败：" + e.getMessage());
        }
    }

    @Override
    public void setDeepDetectionFinish(DeepDetectionFinish finish) {
        log.info("远程设置深度检测完成，finish：{}", finish);
        try {
            // 同时调用所有服务设置深度检测完成
            androidMiitDetectServiceApi.setDeepDetectionFinish(convertToDeepDetectionFinishParam(finish));
            iosMiitDetectServiceApi.setDeepDetectionFinish(convertToDeepDetectionFinishParam(finish));
            appletMiitDetectServiceApi.setDeepDetectionFinish(convertToDeepDetectionFinishParam(finish));
            harmonyMiitDetectServiceApi.setDeepDetectionFinish(convertToDeepDetectionFinishParam(finish));
        } catch (Exception e) {
            log.error("远程设置深度检测完成失败，finish：{}", finish, e);
        }
    }

    @Override
    public LawDetectResultVO findLawDetectResultItem(Long taskId, String itemNo) {
        log.info("远程获取法规检测结果项，taskId：{}，itemNo：{}", taskId, itemNo);
        try {
            // 尝试从各个服务获取法规检测结果项，返回第一个非空结果
            LawDetectResultVO androidResult = null;
            try {
                androidResult = convertLawDetectResultVO(androidMiitDetectServiceApi.findLawDetectResultItem(taskId, itemNo));
            } catch (Exception e) {
                log.warn("从Android服务获取法规检测结果项失败，taskId：{}，itemNo：{}", taskId, itemNo, e);
            }

            if (androidResult != null) {
                return androidResult;
            }

            LawDetectResultVO iosResult = null;
            try {
                iosResult = convertLawDetectResultVO(iosMiitDetectServiceApi.findLawDetectResultItem(taskId, itemNo));
            } catch (Exception e) {
                log.warn("从iOS服务获取法规检测结果项失败，taskId：{}，itemNo：{}", taskId, itemNo, e);
            }

            if (iosResult != null) {
                return iosResult;
            }

            LawDetectResultVO appletResult = null;
            try {
                appletResult = convertLawDetectResultVO(appletMiitDetectServiceApi.findLawDetectResultItem(taskId, itemNo));
            } catch (Exception e) {
                log.warn("从小程序服务获取法规检测结果项失败，taskId：{}，itemNo：{}", taskId, itemNo, e);
            }

            if (appletResult != null) {
                return appletResult;
            }

            LawDetectResultVO harmonyResult = null;
            try {
                harmonyResult = convertLawDetectResultVO(harmonyMiitDetectServiceApi.findLawDetectResultItem(taskId, itemNo));
            } catch (Exception e) {
                log.warn("从鸿蒙服务获取法规检测结果项失败，taskId：{}，itemNo：{}", taskId, itemNo, e);
            }

            return harmonyResult;
        } catch (Exception e) {
            log.error("远程获取法规检测结果项失败，taskId：{}，itemNo：{}", taskId, itemNo, e);
            return null;
        }
    }

    /**
     * 转换LawDetectDetailDTO到LawDetectDetailVO
     */
    private LawDetectDetailVO convertLawDetectDetailDTO(LawDetectDetailDTO dto) {
        if (dto == null) {
            return null;
        }
        LawDetectDetailVO vo = new LawDetectDetailVO();
        BeanUtils.copyProperties(dto, vo);

        // 转换行为详情列表
        if (CollectionUtils.isNotEmpty(dto.getActionDetails())) {
            vo.setActionDetails(convertLawActionDetailDTOList(dto.getActionDetails()));
        }
        if (CollectionUtils.isNotEmpty(dto.getPermissionActions())) {
            vo.setPermissionActions(convertLawActionDetailDTOList(dto.getPermissionActions()));
        }
        if (CollectionUtils.isNotEmpty(dto.getOrdinaryActions())) {
            vo.setOrdinaryActions(convertLawActionDetailDTOList(dto.getOrdinaryActions()));
        }
        if (CollectionUtils.isNotEmpty(dto.getPrivacyTransmission())) {
            vo.setPrivacyTransmission(convertLawActionDetailDTOList(dto.getPrivacyTransmission()));
        }
        if (CollectionUtils.isNotEmpty(dto.getPrivacySharedPrefs())) {
            vo.setPrivacySharedPrefs(convertLawActionDetailDTOList(dto.getPrivacySharedPrefs()));
        }

        return vo;
    }

    /**
     * 转换PageInfo<LawActionDetailDTO>到PageInfo<LawActionDetailVO>
     */
    private PageInfo<LawActionDetailVO> convertLawActionDetailPageInfo(PageInfo<LawActionDetailDTO> pageInfo) {
        if (pageInfo == null) {
            return PageInfo.emptyPageInfo();
        }

        PageInfo<LawActionDetailVO> result = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, result);

        if (pageInfo.getList() != null) {
            result.setList(convertLawActionDetailDTOList(pageInfo.getList()));
        }

        return result;
    }

    /**
     * 转换List<LawActionDetailDTO>到List<LawActionDetailVO>
     */
    private List<LawActionDetailVO> convertLawActionDetailDTOList(List<LawActionDetailDTO> dtoList) {
        if (dtoList == null || dtoList.isEmpty()) {
            return Collections.emptyList();
        }

        return dtoList.stream()
                .map(this::convertLawActionDetailDTO)
                .collect(Collectors.toList());
    }

    /**
     * 转换单个LawActionDetailDTO到LawActionDetailVO
     */
    private LawActionDetailVO convertLawActionDetailDTO(LawActionDetailDTO dto) {
        if (dto == null) {
            return null;
        }

        LawActionDetailVO vo = new LawActionDetailVO();
        BeanUtils.copyProperties(dto, vo);
        return vo;
    }

    /**
     * 转换Map<String, LawDetectDetailDTO>到Map<String, LawDetectDetailVO>
     */
    private Map<String, LawDetectDetailVO> convertLawDetectDetailMap(Map<String, LawDetectDetailDTO> dtoMap) {
        if (dtoMap == null || dtoMap.isEmpty()) {
            return Collections.emptyMap();
        }

        Map<String, LawDetectDetailVO> voMap = new HashMap<>();
        dtoMap.forEach((key, value) -> {
            LawDetectDetailVO vo = convertLawDetectDetailDTO(value);
            if (vo != null) {
                voMap.put(key, vo);
            }
        });

        return voMap;
    }

    /**
     * 转换LawItemResultDetailQuery到LawItemResultDetailParam
     * 由于Query和Param类结构相同，直接返回原对象
     */
    private LawItemResultDetailQuery convertToLawItemResultDetailParam(LawItemResultDetailQuery query) {
        return query;
    }

    /**
     * 转换LawActionDetailQuery到LawActionDetailParam
     * 由于Query和Param类结构相同，直接返回原对象
     */
    private LawActionDetailQuery convertToLawActionDetailParam(LawActionDetailQuery query) {
        return query;
    }

    /**
     * 转换ComplianceVO到ComplianceParam
     * 由于VO和Param类结构相同，直接返回原对象
     */
    private ComplianceVO convertToComplianceParam(ComplianceVO compliance) {
        return compliance;
    }

    /**
     * 转换ComplianceVO列表到ComplianceListParam
     * 由于没有找到具体的ComplianceListParam类定义，直接返回List
     */
    private List<ComplianceVO> convertToComplianceListParam(Long taskId, List<ComplianceVO> list) {
        return list;
    }

    /**
     * 转换DeepDetectionFinish到DeepDetectionFinishParam
     * 由于Query和Param类结构相同，直接返回原对象
     */
    private DeepDetectionFinish convertToDeepDetectionFinishParam(DeepDetectionFinish finish) {
        return finish;
    }

}
