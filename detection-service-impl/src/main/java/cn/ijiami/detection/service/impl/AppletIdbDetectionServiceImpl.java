package cn.ijiami.detection.service.impl;

import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_ANDROID_DEVICE_TYPE;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_ANDROID_STF_TOKEN;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA_ANDROID_MSG_TYPE;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA_ANDROID_OR_APPLET_ACTION_TIME;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA_ANDROID_OR_APPLET_EXECUTOR;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA_ANDROID_OR_APPLET_EXECUTOR_TYPE;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA_ANDROID_OR_APPLET_LOG;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA_ANDROID_OR_APPLET_LOG_ACTION_ID;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA_ANDROID_OR_APPLET_LOG_ACTION_NAME;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA_ANDROID_OR_APPLET_LOG_BEHAVIOR_STAGE;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA_ANDROID_OR_APPLET_LOG_ID;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA_ANDROID_OR_APPLET_LOG_PERSONAL;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA_ANDROID_OR_APPLET_PACKAGE_NAME;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA_ANDROID_TASK_PROGRESS;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DEVICE_HARDWARE_SERIAL;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DEVICE_SERIAL;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_LAW_TYPE;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_PROTOCOL;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_TYPE;
import static cn.ijiami.detection.constant.IdbMsgFieldName.DYNAMIC_TYPE;
import static cn.ijiami.detection.constant.IdbMsgFieldName.NOTIFICATION_ID;
import static cn.ijiami.detection.constant.IdbMsgFieldName.TASK_ID;
import static cn.ijiami.detection.constant.IdbMsgFieldName.TYPE;
import static cn.ijiami.detection.constant.PinfoConstant.DEFAULT_PROGRESS;
import static cn.ijiami.detection.utils.AppletActionUtils.getMethod;
import static cn.ijiami.detection.utils.AppletActionUtils.getStatusCode;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import cn.ijiami.detection.VO.DetectionDeviceVO;
import cn.ijiami.detection.VO.RealTimeBehaviorLog;
import cn.ijiami.detection.VO.RealTimeNetLog;
import cn.ijiami.detection.VO.StfDeviceInfo;
import cn.ijiami.detection.VO.TDetectionConfigVO;
import cn.ijiami.detection.VO.TTaskExtendVO;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.VO.detection.privacy.AlipayAppletAction;
import cn.ijiami.detection.VO.detection.privacy.WechatAppletAction;
import cn.ijiami.detection.VO.detection.privacy.dto.AppletActionBO;
import cn.ijiami.detection.analyzer.AppletBehaviorInfoAction;
import cn.ijiami.detection.analyzer.BehaviorInfoAction;
import cn.ijiami.detection.analyzer.helper.BehaviorActionConvertHelper;
import cn.ijiami.detection.analyzer.parser.AlipayAppletBehaviorInfoParser;
import cn.ijiami.detection.analyzer.parser.WechatAppletBehaviorInfoParser;
import cn.ijiami.detection.bean.DynamicTaskContext;
import cn.ijiami.detection.config.IjiamiCommonProperties;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.dao.TaskDAO;
import cn.ijiami.detection.dao.UserUseDeviceDAO;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.entity.TUserUseDevice;
import cn.ijiami.detection.enums.AndroidDynamicDetectionCmdEnum;
import cn.ijiami.detection.enums.AndroidDynamicLogTypeEnum;
import cn.ijiami.detection.enums.AndroidManualTypeEnum;
import cn.ijiami.detection.enums.AppletActionTypeEnum;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.BroadcastMessageTypeEnum;
import cn.ijiami.detection.enums.DetectionDynamicType;
import cn.ijiami.detection.server.client.base.enums.DynamicAutoStatusEnum;
import cn.ijiami.detection.enums.DynamicCmdDataTypeEnum;
import cn.ijiami.detection.server.client.base.enums.DynamicDeviceTypeEnum;
import cn.ijiami.detection.server.client.base.enums.DynamicLawStatusEnum;
import cn.ijiami.detection.enums.IdbMsgProtocolEnum;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.enums.ReviewStatusEnum;
import cn.ijiami.detection.server.client.base.enums.TerminalTypeEnum;
import cn.ijiami.detection.enums.UserAppletDeviceStatusEnum;
import cn.ijiami.detection.helper.CustomDetectHelper;
import cn.ijiami.detection.mapper.TActionNougatMapper;
import cn.ijiami.detection.mapper.TAssetsMapper;
import cn.ijiami.detection.mapper.TDetectionConfigMapper;
import cn.ijiami.detection.mapper.TPrivacyActionNougatMapper;
import cn.ijiami.detection.mapper.TPrivacyOutsideAddressMapper;
import cn.ijiami.detection.service.DetectionDataService;
import cn.ijiami.detection.service.api.AppletIdbDetectionService;
import cn.ijiami.detection.service.api.CacheService;
import cn.ijiami.detection.service.api.DetectionConfigService;
import cn.ijiami.detection.service.api.DeviceManagerService;
import cn.ijiami.detection.service.api.IDynamicAlipayAppletDetectionService;
import cn.ijiami.detection.service.api.IDynamicWechatAppletDetectionService;
import cn.ijiami.detection.service.api.IPrivacyDetectionService;
import cn.ijiami.detection.service.api.ITaskExtendService;
import cn.ijiami.detection.utils.AppletActionUtils;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.ConstantsUtils;
import cn.ijiami.detection.utils.HarmonyUtils;
import cn.ijiami.detection.utils.IpUtil;
import cn.ijiami.detection.utils.MD5Util;
import cn.ijiami.detection.utils.StfUtils;
import cn.ijiami.framework.common.exception.IjiamiCommandException;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AppletIdbDetectionServiceImpl.java
 * @Description 小程序 idb检测服务
 * @createTime 2023年08月09日 16:49:00
 */
@Slf4j
@Service
public class AppletIdbDetectionServiceImpl extends BaseIdbDetectionServiceImpl<TaskDetailVO> implements AppletIdbDetectionService {

    private static final int BIG_OBJECT_SIZE = 400000 * 16;

    @Autowired
    private TaskDAO taskDAO;

    @Autowired
    private TAssetsMapper assetsMapper;

    @Autowired
    private TActionNougatMapper tActionNougatMapper;

    @Autowired
    private IjiamiCommonProperties commonProperties;

    @Autowired
    private BehaviorActionConvertHelper actionConvertHelper;

    @Autowired
    private BehaviorInfoAction behaviorInfoAction;

    @Autowired
    private CacheService cacheService;
    
    @Autowired
    private DeviceManagerService deviceManagerService;

    @Autowired
    private IpUtil ipUtil;

    @Value("${ijiami.remote.tool.status}")
    private boolean isRemote;

    @Autowired
    private IDynamicWechatAppletDetectionService dynamicWechatAppletDetectionService;

    @Autowired
    private IDynamicAlipayAppletDetectionService dynamicAlipayAppletDetectionService;

    @Autowired
    private TPrivacyOutsideAddressMapper privacyOutsideAddressMapper;

    @Autowired
    private TPrivacyActionNougatMapper privacyActionNougatMapper;

    @Autowired
    private UserUseDeviceDAO userUseDeviceDAO;

    @Autowired
    private IjiamiCommonProperties ijiamiCommonProperties;

    @Autowired
    private DetectionDataService detectionDataService;

    @Autowired
    private IPrivacyDetectionService privacyDetectionService;
    
    @Autowired
    private ITaskExtendService taskExtendService;

    @Override
    public void updateAppletDynamicFromStomp(JSONObject message, String messageStr) {
        try {
            JSONObject cmdData = message.getJSONObject(CMD_DATA);
            int type = cmdData.getInt(CMD_DATA_ANDROID_MSG_TYPE);
            int cmdType = message.getInt(CMD_TYPE);
            // 处理前端发送给idb的消息
            if (message.optString(CMD_PROTOCOL).equals(IdbMsgProtocolEnum.SEND.getName())) {
                handleClientSendMessageToIdb(message);
                return;
            }
            if (cmdType == AndroidDynamicDetectionCmdEnum.RUNNING.value) {
                if (type == AndroidDynamicLogTypeEnum.BEHAVIOR.value) {
                    handleAppletLog(message, cmdData);
                } else if (type == AndroidDynamicLogTypeEnum.NET.value) {
                    handleAppletLog(message, cmdData);
                } else {
                    handleAppletCommand(message, cmdData);
                }
            } else {
                handleAppletCommand(message, cmdData);
            }
        } catch (Exception e) {
            log.error("updateAppletDynamicFromStomp msg:{} error", messageStr, e);
        }
    }

    private void handleAppletLog(JSONObject message, JSONObject cmdData) {
        String cmdDataMsg = cmdData.getString(CMD_DATA_ANDROID_OR_APPLET_LOG);
        long taskId = cmdData.optLong(TASK_ID, Integer.MIN_VALUE);
        if (taskId < 0) {
            log.error("错误的TaskId:{}", taskId);
            return;
        }
        Long interdictedTime = cacheService.getLong(PinfoConstant.INTERDICTED_ACTION + taskId);
        if (Objects.nonNull(interdictedTime)) {
            log.info("日志拦截");
            return;
        }
        if (isReviewTask(taskId)) {
            // 复核任务不发送日志
            log.info("复核任务不发送实时日志消息");
            return;
        }
        DynamicTaskContext taskData = getTaskContext(taskId);
        if (taskData.getTerminalType() == TerminalTypeEnum.WECHAT_APPLET) {
            autoAnalyticalWechatLog(taskId, cmdDataMsg);
        } else if (taskData.getTerminalType() == TerminalTypeEnum.ALIPAY_APPLET) {
            autoAnalyticalAlipayLog(taskId, cmdDataMsg);
        }
    }

    private void handleAppletCommand(JSONObject message, JSONObject cmdData) {
        long taskId = cmdData.optLong(TASK_ID, Integer.MIN_VALUE);
        if (taskId < 0) {
            log.error("错误的TaskId:{}", taskId);
            return;
        }
        try {
            int dynamicType = message.getInt(DYNAMIC_TYPE);
            int cmdType = message.getInt(CMD_TYPE);
            int type = cmdData.getInt(CMD_DATA_ANDROID_MSG_TYPE);
            String cmdDataMsg = cmdData.getString(CMD_DATA_ANDROID_OR_APPLET_LOG);
            TTask tTask = taskMapper.selectByPrimaryKey(taskId);
            if (tTask == null) {
                throw new IjiamiCommandException("检测任务不存在,更新操作失败！");
            }
            boolean isCloudPhoneTask = isFastTask(dynamicType)
                    && Objects.nonNull(tTask.getDynamicDeviceType())
                    && tTask.getDynamicDeviceType() == DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD;
            if (isCloudPhoneTask
                    && isRemote
                    && !CustomDetectHelper.getInstance().isPriorityLocal(tTask.getCreateUserId())) {
                log.info("TaskId:{} 云手机的快速检测消息不进行处理", taskId);
                return;
            }
            int lawType = message.optInt(CMD_LAW_TYPE, -1);
            if (lawType > 0) {
                TaskDetailVO taskDetailVO = findById(tTask.getApkDetectionDetailId(), "taskDetailVO");
                if (taskDetailVO == null) {
                    log.error("任务文档不存在 id={}", tTask.getApkDetectionDetailId());
                    return;
                }
                if (taskDetailVO.getLawTypeCode() == null || !taskDetailVO.getLawTypeCode().contains(String.valueOf(lawType))) {
                    Map<String, Object> paramMap = new HashMap<>();
                    Update update = new Update();
                    paramMap.put("_id", tTask.getApkDetectionDetailId());
                    update.set("lawTypeCode", lawType + ",");
                    update(paramMap, update);
                    getTaskContext(tTask.getTaskId()).setLawType(lawType);
                }
            }
            // 快速检测失败 直接返回
            if (isFastTask(dynamicType)
                    && tTask.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_FAILED) {
                log.info("TaskId:{} 快速检测失败", taskId);
                return;
            }
            // 正常进度
            if (cmdType == AndroidDynamicDetectionCmdEnum.RUNNING.value) {
                handleAppletDynamicRunning(tTask, type, message, cmdData);
            } else if (cmdType == AndroidDynamicDetectionCmdEnum.STOP.value) {
                // 异常进度
                handleAppletDynamicStop(tTask, type, dynamicType, cmdDataMsg);
            } else if (cmdType == AndroidDynamicDetectionCmdEnum.FINISH.value) {
                // 任务完成
                handleAppletTaskFinish(tTask, dynamicType, cmdDataMsg);
            } else if (cmdType == AndroidDynamicDetectionCmdEnum.SCREENSHOT.value) {
                // 截图数据
                saveScreenshotImageData(tTask.getTaskId(), cmdData);
            } else if (cmdType == AndroidDynamicDetectionCmdEnum.STATIC_DETECTION_RESULT.value) {
                // 静态任务完成
                handleAppletTaskFinish(tTask, dynamicType, cmdDataMsg);
            }
        } catch (Exception e) {
            log.error("TaskId:{} 消息处理失败 {}", taskId, e.toString(), e);
        }
    }

    private void handleClientSendMessageToIdb(JSONObject message) {
        JSONObject cmdData = message.getJSONObject(CMD_DATA);
        int cmdType = message.getInt(CMD_TYPE);
        if (cmdType == AndroidDynamicDetectionCmdEnum.STOP.value) {
            Long taskId = cmdData.optLong(TASK_ID);
            DynamicTaskContext taskData = getTaskContext(taskId);
            if (taskData != null) {
                // 如果超时后任务状态还没变，说明前端发送消息给idb中断任务失败，后台进行补偿
                startIdbMessageTimeoutTask(taskId, taskData.getTaskProcessId(), message, task -> {
                    int dynamicType = message.getInt(DYNAMIC_TYPE);
                    handleAppletDynamicStop(task, AndroidManualTypeEnum.USER_INTERRUPTED.value, dynamicType, "idb中断超时，服务器进行中断");
                });
            }
        }
    }

    private boolean isDeepOrFastTask(int dynamicType) {
        return isDeepTask(dynamicType)
                || isFastTask(dynamicType);
    }

    private boolean isStaticTask(int dynamicType) {
        return dynamicType == DetectionDynamicType.ALIPAY_APPLET_STATIC.getValue()
                || dynamicType == DetectionDynamicType.WECHAT_APPLET_STATIC.getValue();
    }

    private boolean isDeepTask(int dynamicType) {
        return dynamicType == DetectionDynamicType.WECHAT_APPLET_DEEP.getValue()
                || dynamicType == DetectionDynamicType.ALIPAY_APPLET_DEEP.getValue();
    }

    private boolean isFastTask(int dynamicType) {
        return dynamicType == DetectionDynamicType.WECHAT_APPLET_FAST.getValue()
                || dynamicType == DetectionDynamicType.ALIPAY_APPLET_FAST.getValue();
    }

    private boolean isLawTask(int dynamicType) {
        return dynamicType == DetectionDynamicType.WECHAT_APPLET_LAW.getValue()
                || dynamicType == DetectionDynamicType.ALIPAY_APPLET_LAW.getValue();
    }

    private void handleAppletDynamicRunning(TTask tTask, int type, JSONObject message, JSONObject cmdData) {
        if (type == AndroidManualTypeEnum.CLEAR_LOG.value) {
            log.info("TaskId:{} idb 清除日志完成", tTask.getTaskId());
            cacheService.delete(PinfoConstant.INTERDICTED_ACTION + tTask.getTaskId());
            return;
        }
        int dynamicType = message.getInt(DYNAMIC_TYPE);
        saveNotification(message.optString(NOTIFICATION_ID, StringUtils.EMPTY), tTask);
        String cmdDataMsg = cmdData.getString(CMD_DATA_ANDROID_OR_APPLET_LOG);
        if (CommonUtil.isAppletLoginApp(cmdDataMsg)) {
            sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_APPLET_WECHAT_NOT_LOGGED_IN, cmdDataMsg, tTask);
            return;
        }
        if (CommonUtil.isAppletLogoutApp(cmdDataMsg)) {
            sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_APPLET_WECHAT_LOGOUT, cmdDataMsg, tTask);
            return;
        }
        if (isFastTask(dynamicType)) {
            handleAppletFastRunning(tTask, message, cmdData);
        } else if (isDeepTask(dynamicType)) {
            handleAppletDeepRunning(tTask, message, cmdData);
        } else if (isLawTask(dynamicType)) {
            if (tTask.isReviewIn()) {
                handleAppletReviewRunning(tTask, message, cmdData);
            } else {
                handleAppletLawRunning(tTask, message, cmdData);
            }
        } else if (isStaticTask(dynamicType)) {
            handleAppletStaticRunning(tTask, message, cmdData);
        }
    }

    /**
     * 快速检测进度消息处理
     *
     * @param tTask
     * @param message
     * @param cmdData
     */
    private void handleAppletFastRunning(TTask tTask, JSONObject message, JSONObject cmdData) {
        // 如果动态检测已经完成，直接返回
        if (tTask.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED) {
            log.info("TaskId:{} 动态检测已经完成", tTask.getTaskId());
            return;
        }
        String cmdDataMsg = cmdData.getString(CMD_DATA_ANDROID_OR_APPLET_LOG);
        Map<String, Object> paramMap = new HashMap<>();
        Update update = new Update();
        paramMap.put("_id", tTask.getApkDetectionDetailId());
        TTask updateTask = new TTask();
        updateTask.setTaskId(tTask.getTaskId());
        if (StringUtils.isNotBlank(cmdDataMsg)) {
            updateTask.setDescription(cmdDataMsg);
            update.set("dynamic_detection_description", cmdDataMsg);
        }
        updateDeviceInfo(updateTask, message, tTask);
        if (Objects.isNull(tTask.getDynamicStarttime()) || tTask.getDynamicStatus() != DynamicAutoStatusEnum.DETECTION_AUTO_IN) {
            updateTask.setDynamicStarttime(new Date());
            updateTask.setDynamicAutoIn();
            update.set("dynamic_detection_status", DynamicAutoStatusEnum.DETECTION_AUTO_IN.getValue());
        }
        int progress = DEFAULT_PROGRESS;
        if (cmdData.containsKey(CMD_DATA_ANDROID_TASK_PROGRESS)) {
            progress = Math.min(cmdData.getInt(CMD_DATA_ANDROID_TASK_PROGRESS), DEFAULT_PROGRESS);
            update.set("dynamicProgress", progress);
        }
        taskDAO.updateTaskStatus(tTask, updateTask, paramMap, update);
        sendDynamicTaskProgressMessage(progress, tTask);
    }

    private void handleAppletDeepRunning(TTask tTask, JSONObject message, JSONObject cmdData) {
        // 如果动态检测已经完成，直接返回
        if (tTask.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED) {
            log.info("TaskId:{} 动态检测已经完成", tTask.getTaskId());
            return;
        }
        int type = cmdData.getInt(CMD_DATA_ANDROID_MSG_TYPE);
        String cmdDataMsg = cmdData.getString(CMD_DATA_ANDROID_OR_APPLET_LOG);

        Map<String, Object> paramMap = new HashMap<>();
        Update update = new Update();
        paramMap.put("_id", tTask.getApkDetectionDetailId());
        TTask updateTask = new TTask();
        updateTask.setTaskId(tTask.getTaskId());
        updateDeviceInfo(updateTask, message, tTask);
        if (Objects.isNull(tTask.getDynamicStarttime())) {
            updateTask.setDynamicStarttime(new Date());
        }
        // 下载apk
        if (type == AndroidManualTypeEnum.DOWNLOAD_APP.value) {
            // 深度/快速检测
            updateTask.setDescription("App下载中");
            updateTask.setDynamicStatus(DynamicAutoStatusEnum.DETECTION_AUTO_DOWNLOAD_IPA);
            update.set("dynamic_detection_description", "App下载中");
            update.set("dynamic_detection_status", DynamicAutoStatusEnum.DETECTION_AUTO_DOWNLOAD_IPA.getValue());

            taskDAO.updateTaskStatus(tTask, updateTask, paramMap, update);
            sendDynamicTaskProgressMessage(0, tTask);
            removeTaskContext(tTask);
        } else if (type == AndroidManualTypeEnum.SCREENSHOT_DATA.value) {
            log.info("截图数据");
        } else if (type == AndroidManualTypeEnum.APP_UNINSTALL_COMPLETE.value) {
            // 任务完成
            handleAppletTaskFinish(tTask, DetectionDynamicType.WECHAT_APPLET_DEEP.getValue(), cmdDataMsg);
        }
        // 其他正常情况
        else {
            if (isDynamicTaskOver(tTask)) {
                return;
            }
            updateTask.setDescription(cmdDataMsg);
            updateTask.setDynamicAutoIn();
            update.set("dynamic_detection_description", cmdDataMsg);
            update.set("dynamic_detection_status", DynamicAutoStatusEnum.DETECTION_AUTO_IN.getValue());
            taskDAO.updateTaskStatus(tTask, updateTask, paramMap, update);
            sendDynamicTaskProgressMessage(DEFAULT_PROGRESS, tTask);
        }
    }

    // 法规检测
    private void handleAppletLawRunning(TTask tTask, JSONObject message, JSONObject cmdData) {
        // 如果动态检测已经完成，直接返回
        if (tTask.getDynamicLawStatus() == DynamicLawStatusEnum.DETECTION_LAW_SUCCEED) {
            log.info("TaskId:{} 法规检测已经完成", tTask.getTaskId());
            return;
        }
        int type = cmdData.getInt(CMD_DATA_ANDROID_MSG_TYPE);
        String cmdDataMsg = cmdData.getString(CMD_DATA_ANDROID_OR_APPLET_LOG);

        Map<String, Object> paramMap = new HashMap<>();
        Update update = new Update();
        paramMap.put("_id", tTask.getApkDetectionDetailId());
        TTask updateTask = new TTask();
        updateTask.setTaskId(tTask.getTaskId());
        updateDeviceInfo(updateTask, message, tTask);
        // 法规检测
        if (Objects.isNull(tTask.getLawStarttime())) {
            updateTask.setLawStarttime(new Date());
        }
        // 下载apk
        if (type == AndroidManualTypeEnum.DOWNLOAD_APP.value) {
            // 深度/快速检测
            String desc = "App下载中";
            updateTask.setDescription(desc);
            updateTask.setDynamicLawStatus(DynamicLawStatusEnum.DETECTION_LAW_DOWNLOAD_IPA);
            update.set("dynamic_law_detection_status", DynamicLawStatusEnum.DETECTION_LAW_DOWNLOAD_IPA.getValue());
            update.set("describe", desc);
            taskDAO.updateTaskStatus(tTask, updateTask, paramMap, update);
            sendDynamicTaskProgressMessage(0, tTask);
            removeTaskContext(tTask);
        } else if (type == AndroidManualTypeEnum.SCREENSHOT_DATA.value) {
            log.info("截图数据");
        } else if (type == AndroidManualTypeEnum.APP_UNINSTALL_COMPLETE.value) {
            // 任务完成
            handleAppletTaskFinish(tTask, DetectionDynamicType.WECHAT_APPLET_LAW.getValue(), cmdDataMsg);
        }
        // 其他正常情况
        else {
            if (isLawTaskOver(tTask)) {
                return;
            }
            updateTask.setDescription(cmdDataMsg);
            updateTask.setDynamicLawIn();
            update.set("dynamic_law_detection_status", DynamicLawStatusEnum.DETECTION_LAW_IN.getValue());
            update.set("describe", cmdDataMsg);
            taskDAO.updateTaskStatus(tTask, updateTask, paramMap, update);
            sendDynamicTaskProgressMessage(DEFAULT_PROGRESS, tTask);
        }
    }

    // 复核检测
    private void handleAppletReviewRunning(TTask tTask, JSONObject message, JSONObject cmdData) {
        int type = cmdData.getInt(CMD_DATA_ANDROID_MSG_TYPE);
        Map<String, Object> paramMap = new HashMap<>();
        Update update = new Update();
        paramMap.put("_id", tTask.getApkDetectionDetailId());
        TTask updateTask = new TTask();
        updateTask.setTaskId(tTask.getTaskId());
        updateDeviceInfo(updateTask, message, tTask);
        // 复核检测
        if (Objects.isNull(tTask.getReviewStarttime())) {
            updateTask.setReviewStarttime(new Date());
        }
        // 下载apk
        if (type == AndroidManualTypeEnum.DOWNLOAD_APP.value) {
            updateTask.setReviewStatus(ReviewStatusEnum.DETECTION_REVIEW_DOWNLOAD_APP);
            taskDAO.updateTaskStatus(tTask, updateTask, paramMap, update);
            removeTaskContext(tTask);
        } else if (type == AndroidManualTypeEnum.SCREENSHOT_DATA.value) {
            log.info("截图数据");
        } else if (type == AndroidManualTypeEnum.APP_UNINSTALL_COMPLETE.value) {
            log.info("任务完成");
        }
        // 其他正常情况
        else {
            if (isReviewTaskOver(tTask)) {
                return;
            }
            updateTask.setReviewStatus(ReviewStatusEnum.DETECTION_REVIEW_IN);
            taskDAO.updateTaskStatus(tTask, updateTask, paramMap, update);
        }
        setReviewTask(tTask.getTaskId());
    }

    private void handleAppletStaticRunning(TTask tTask, JSONObject message, JSONObject cmdData) {
        TaskDetailVO taskDetailVO = findById(tTask.getApkDetectionDetailId(), "taskDetailVO");
        if (taskDetailVO == null) {
            log.error("任务文档不存在 id={}", tTask.getApkDetectionDetailId());
            return;
        }
        int progress = cmdData.optInt("progress");
        // 更新任务信息
        taskDAO.updateStaticProgress(tTask,
                message.optString("deviceSerial"),
                StringUtils.EMPTY,
                progress);
        sendStaticTaskProgressMessage(progress, tTask);
    }

    /**
     * 更新消息传过来的设备信息
     *
     * @param updateTask
     * @param message
     */
    private void updateDeviceInfo(TTask updateTask, JSONObject message, TTask tTask) {
        String deviceSerial = message.optString(CMD_DEVICE_SERIAL);
        String deviceHardwareSerial = message.optString(CMD_DEVICE_HARDWARE_SERIAL);
        String stfToken = message.optString(CMD_ANDROID_STF_TOKEN);
        DynamicDeviceTypeEnum deviceTypeEnum = DynamicDeviceTypeEnum.getItem(message.optInt(CMD_ANDROID_DEVICE_TYPE));
        // 有设备id 云手机
        if (StringUtils.isNotBlank(deviceSerial)) {
            updateTask.setDeviceSerial(deviceSerial);
        }
        if (StringUtils.isNotBlank(deviceHardwareSerial)) {
            updateTask.setDeviceHardwareSerial(deviceHardwareSerial);
        }
        if (StringUtils.isNotBlank(stfToken)) {
            updateTask.setStfToken(stfToken);
        }
        if (Objects.nonNull(deviceTypeEnum)) {
            updateTask.setDynamicDeviceType(deviceTypeEnum);
        }
        
        if (StringUtils.isNotBlank(deviceSerial)) {
        	try {
				deviceManagerService.addDevice(tTask.getTaskId(), deviceSerial, tTask.getTerminalType());
			} catch (Exception e) {
				log.error(e.getMessage());;
			}
        }
        
        //保存使用设备记录
	    log.info("小程序-操作设备保存记录-新设备={}, task.getDeviceSerial={}",deviceSerial, tTask.getDeviceSerial());
	    if(StringUtils.isBlank(tTask.getDeviceSerial()) && StringUtils.isNotBlank(deviceSerial)){
	    	log.info("1操作设备保存记录saveOperateLog={}",deviceSerial );
	    	taskDAO.saveOperateLog(deviceSerial, tTask);
	    }
    }

    private void handleAppletDynamicStop(TTask tTask, int type, int dynamicType, String cmdDataMsg) {
        // 避免截图失败导致任务中断
        if (org.apache.commons.lang3.StringUtils.equals(cmdDataMsg, "截图失败")) {
            return;
        }
        // 深度/快速检测
        if (isDeepOrFastTask(dynamicType)) {
            taskDAO.updateDynamicFailure(tTask, type == AndroidManualTypeEnum.USER_INTERRUPTED.value ? "手动中断" : cmdDataMsg);
        }
        // 法规检测
        else if (isLawTask(dynamicType)) {
            // 复核检测失败
            if (tTask.isReviewIn()) {
                taskDAO.updateReviewFailure(tTask, type == AndroidManualTypeEnum.USER_INTERRUPTED.value ? "手动中断" : cmdDataMsg);
            } else {
                taskDAO.updateLawFailure(tTask, type == AndroidManualTypeEnum.USER_INTERRUPTED.value ? "手动中断" : cmdDataMsg);
            }
        } else if (isStaticTask(dynamicType)) {
            taskDAO.updateStaticFailure(tTask, type == AndroidManualTypeEnum.USER_INTERRUPTED.value ? "手动中断" : cmdDataMsg);
        }
        sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_ERROR, cmdDataMsg, tTask);
    }

    private void handleAppletTaskFinish(TTask task, int dynamicType, String cmdDataMsg) {
        if (dynamicType == DetectionDynamicType.WECHAT_APPLET_DEEP.getValue()) {
            log.info("微信小程序深度检测完成");
            dynamicWechatAppletDetectionService.handleAppletManualFinish(task, dynamicType);
        } else if (dynamicType == DetectionDynamicType.ALIPAY_APPLET_DEEP.getValue()) {
            log.info("支付宝小程序深度检测完成");
            dynamicAlipayAppletDetectionService.handleAppletManualFinish(task, dynamicType);
        } else if (dynamicType == DetectionDynamicType.ALIPAY_APPLET_STATIC.getValue()) {
            dynamicAlipayAppletDetectionService.analysisStatic(task, cmdDataMsg);
            sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_FINISH, StringUtils.EMPTY, task);
            // 有可能动态检测已经完成，调用一次生成默认报告
            privacyDetectionService.buildAndUploadDefaultReport(task.getTaskId());
        } else if (dynamicType == DetectionDynamicType.WECHAT_APPLET_STATIC.getValue()) {
            dynamicWechatAppletDetectionService.analysisStatic(task, cmdDataMsg);
            sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_FINISH, StringUtils.EMPTY, task);
            // 有可能动态检测已经完成，调用一次生成默认报告
            privacyDetectionService.buildAndUploadDefaultReport(task.getTaskId());
        }
    }

    @Override
    public void autoAnalyticalWechatLog(Long taskId, String logText) {
        if (StringUtils.isBlank(logText)) {
            return;
        }
        WechatAppletAction actionLog = WechatAppletBehaviorInfoParser.parserLogStr(logText);
        if (actionLog == null || actionLog.getData() == null) {
            log.info("小程序动态数据 解析出错 {}", logText);
            return;
        }
        AppletActionBO data = actionLog.getData();
        if (data.getReturnData() != null && logText.length() > BIG_OBJECT_SIZE) {
            log.info("小程序动态数据 长度太大 {}", logText.length());
            // 太大的数据不处理
            return;
        }
        if (StringUtils.isEmpty(data.getAppId())) {
            log.error("小程序动态数据AppId为空");
            return;
        }
        if (StringUtils.isEmpty(data.getActionApi())) {
            log.error("小程序动态数据ActionApi为空");
            return;
        }
        if (Objects.isNull(taskId)) {
            taskId = data.getTaskId();
            String token = data.getToken();
            String pwd = MD5Util.encode(data.getTaskId() + data.getTimeStamp().toString(), PinfoConstant.MINI_PROGRAM_TOKEN_SALT);
            if (!pwd.equals(token)) {
                log.error("小程序动态数据token校验不通过");
                return;
            }
        }
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (task == null) {
            log.info("小程序动态数据分析任务不存在TaskId:{}", taskId);
            return;
        }
        DynamicTaskContext taskData = getTaskContext(taskId);
        data.setAppName(taskData.getAppName());
        // 校验appid
        if (checkAppId(taskData.getAppId(), data.getAppId(), task)) {
            return;
        }
        if (data.getActionType() == AppletActionTypeEnum.NET.itemValue()) {
            appletAnalyticalNetwork(data, BehaviorStageEnum.getItem(actionLog.getBehaviorStage()), taskData, task);
        } else if (data.getActionType() == AppletActionTypeEnum.ACTION.itemValue()) {
            appletAnalyticalBehavior(data, BehaviorStageEnum.getItem(actionLog.getBehaviorStage()), taskData, task);
        } else if (data.getActionType() == AppletActionTypeEnum.STORAGE.itemValue()) {
            appletAnalyticalStorage(data, BehaviorStageEnum.getItem(actionLog.getBehaviorStage()), taskData, task);
        }
    }

    @Override
    public void autoAnalyticalAlipayLog(Long taskId, String logText) {
        if (StringUtils.isBlank(logText)) {
            return;
        }
        AlipayAppletAction actionLog = AlipayAppletBehaviorInfoParser.parserLogStr(logText);
        if (actionLog == null || actionLog.getData() == null) {
            log.info("小程序动态数据 解析出错 {}", logText);
            return;
        }
        AppletActionBO data = actionLog.getData();
        if (data.getReturnData() != null && logText.length() > BIG_OBJECT_SIZE) {
            log.info("小程序动态数据 长度太大 {}", logText.length());
            // 太大的数据不处理
            return;
        }
        if (StringUtils.isEmpty(data.getAppId())) {
            log.error("小程序动态数据AppId为空");
            return;
        }
        if (StringUtils.isEmpty(data.getActionApi())) {
            log.error("小程序动态数据ActionApi为空");
            return;
        }
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (task == null) {
            log.info("小程序动态数据分析任务不存在TaskId:{}", taskId);
            return;
        }
        DynamicTaskContext taskData = getTaskContext(taskId);
        data.setAppName(taskData.getAppName());
        // 校验appid
        if (checkAppId(taskData.getAppId(), data.getAppId(), task)) {
            return;
        }
        if (data.getActionType() == AppletActionTypeEnum.NET.itemValue()) {
            appletAnalyticalNetwork(data, BehaviorStageEnum.getItem(actionLog.getBehaviorStage()), taskData, task);
        } else if (data.getActionType() == AppletActionTypeEnum.ACTION.itemValue()) {
            appletAnalyticalBehavior(data, BehaviorStageEnum.getItem(actionLog.getBehaviorStage()), taskData, task);
        } else if (data.getActionType() == AppletActionTypeEnum.STORAGE.itemValue()) {
            appletAnalyticalStorage(data, BehaviorStageEnum.getItem(actionLog.getBehaviorStage()), taskData, task);
        }
    }

    @Override
    protected DynamicTaskContext getTaskContext(Long taskId) {
        DynamicTaskContext taskContext = dynamicTaskContextService.getTaskContext(taskId);
        if (Objects.isNull(taskContext)) {
            TTask task = taskMapper.selectByPrimaryKey(taskId);
            dynamicTaskContextService.createTaskContext(task, StringUtils.EMPTY);
            taskContext = dynamicTaskContextService.getTaskContext(taskId);
            if (Objects.isNull(taskContext)) {
                log.info("小程序动态数据创建失败 TaskId:{}", taskId);
                throw new IjiamiRuntimeException("create DynamicTaskData failure");
            }
        }
        return taskContext;
    }

    //校验当前appid
    private boolean checkAppId(String appId, String nowAppId, TTask task) {
        if (StringUtils.isBlank(appId)) {
            log.info("记录的appId为空，不进行校验");
            return true;
        }
        if (!nowAppId.equals(appId)) {
            String pre = preActionApi(task.getTaskId());
            if (!nowAppId.equals(pre) && cacheService.compareAndSet(actionApiKey(task.getTaskId()), pre, nowAppId, TimeUnit.HOURS.toMillis(2))) {
                //发送提醒消息
                iSendMessageService.sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_APPLET_ID_ERROR, "小程序api与任务不匹配", task);
            }
            return true;
        }
        return false;
    }

    protected String preActionApi(Long taskId) {
        return cacheService.get(actionApiKey(taskId));
    }

    protected String actionApiKey(Long taskId) {
        return PinfoConstant.CACHE_MINI_PROGRAM_APP_ID + taskId;
    }

    //网络分析
    public void appletAnalyticalNetwork(AppletActionBO data, BehaviorStageEnum behaviorStage, DynamicTaskContext taskData, TTask task) {
        //更新版本号
        if (StringUtils.isNotEmpty(data.getAppVersion())) {
            if (task.getTerminalType() == TerminalTypeEnum.WECHAT_APPLET) {
                dynamicWechatAppletDetectionService.updateAppletAssets(task.getAssetsId(), task.getApkDetectionDetailId(),
                        data.getAppVersion(), StringUtils.EMPTY);
            } else if (task.getTerminalType() == TerminalTypeEnum.ALIPAY_APPLET) {
                dynamicAlipayAppletDetectionService.updateAppletAssets(task.getAssetsId(), task.getApkDetectionDetailId(),
                        data.getAppVersion(), StringUtils.EMPTY, StringUtils.EMPTY);
            }
        }
        TPrivacyOutsideAddress tPrivacyOutsideAddress = AppletBehaviorInfoAction.buildNetwork(task.getTaskId(),
                data, getBehaviorStage(behaviorStage, task.getTaskId()), ipUtil);
        if (Objects.nonNull(tPrivacyOutsideAddress)) {
            // 写入数据库
            detectionDataService.insertAppletNetAction(tPrivacyOutsideAddress, taskData, data);
            RealTimeNetLog netLog = buildAppletNetLog(data, tPrivacyOutsideAddress);
            // 删除掉堆栈再发给前端
            netLog.setRequestData("");
            netLog.setResponseData("");
            sendTaskNetLogMessage(JSONObject.fromObject(netLog), task.getTaskId());
        }
    }

    private BehaviorStageEnum getBehaviorStage(BehaviorStageEnum behaviorStage, Long taskId) {
        if (Objects.nonNull(behaviorStage)) {
            return behaviorStage;
        }
        Integer behavior = cacheService.getInt(PinfoConstant.CACHE_MINI_PROGRAM_BEHAVIOR + taskId);
        return Objects.isNull(behavior) ? BehaviorStageEnum.BEHAVIOR_FRONT : BehaviorStageEnum.getItem(behavior);
    }

    private RealTimeNetLog buildAppletNetLog(AppletActionBO data, TPrivacyOutsideAddress outsideAddress) {
        RealTimeNetLog log = new RealTimeNetLog();
        //IP地址
        String city = ipUtil.getAddress(outsideAddress.getIp());
        log.setId(outsideAddress.getId().toString());
        log.setIp(outsideAddress.getIp());
        log.setHost(outsideAddress.getHost());
        log.setCookie(outsideAddress.getCookie());
        //协议类型
        log.setProtocol(outsideAddress.getProtocol());
        //url地址
        log.setUrl(outsideAddress.getUrl());
        //域名
        log.setPort(outsideAddress.getPort());
        // 境内外判断
        log.setOutside(outsideAddress.getOutside());
        // 网络访问数据类型
        log.setRequestMethod(getMethod(data.getInputParam()));
        log.setAddress(city);
        // 解析状态码
        log.setStatusCode(getStatusCode(data.getReturnData()));
        // 请求内容
        log.setRequestData(outsideAddress.getDetailsData());
        // 响应内容
        log.setResponseData(AppletActionUtils.getResponseData(data.getReturnData()));
        // 行为发生时间
        log.setActionTime(data.getTimeStamp());
        // 行为阶段
        log.setBehaviorStage(outsideAddress.getBehaviorStage().getValue());
        return log;
    }

    private RealTimeBehaviorLog buildAppletBehaviorLog(TPrivacyActionNougat privacyActionNougat, TActionNougat actionInfo) {
        RealTimeBehaviorLog logVO = new RealTimeBehaviorLog();
        logVO.setId(privacyActionNougat.getId().toString());
        logVO.setStackInfo(privacyActionNougat.getStackInfo());
        logVO.setDetailsData(privacyActionNougat.getDetailsData());
        logVO.setActionTime(privacyActionNougat.getActionTime().getTime());
        logVO.setTypeName(actionInfo.getActionName());
        logVO.setTypeId(actionInfo.getActionId());
        logVO.setIsPersonal(PrivacyStatusEnum.getItem(actionInfo.getPersonal()));
        logVO.setExecutor(privacyActionNougat.getExecutor());
        logVO.setExecutorType(privacyActionNougat.getExecutorType());
        logVO.setPackageName(privacyActionNougat.getPackageName());
        logVO.setBehaviorStage(privacyActionNougat.getBehaviorStage().getValue());
        return logVO;
    }

    //行为分析
    public void appletAnalyticalBehavior(AppletActionBO data, BehaviorStageEnum behaviorStage, DynamicTaskContext taskData, TTask task) {
        //获取行为
        Optional<TActionNougat> actionNougat = taskData.getActionNougatMap()
                .values()
                .stream()
                .filter(nougat -> StringUtils.equals(nougat.getActionApi(), data.getActionApi()))
                .findFirst();
        if (!actionNougat.isPresent()) {
            log.info("小程序行为不存在:{}", data.getActionApi());
            return;
        }
        //入库
        TPrivacyActionNougat privacyActionNougat = AppletBehaviorInfoAction.buildBehavioral(task.getTaskId(), data,
                getBehaviorStage(behaviorStage, task.getTaskId()), actionNougat.get());
        // 过滤行为
        if (BehaviorInfoAction.filterActionGroupRegex(taskData.getActionFilterGroupRegexList(), privacyActionNougat.getActionId(),
                privacyActionNougat.getStackInfo(), privacyActionNougat.getDetailsData())) {
            return;
        }
        detectionDataService.insertDynamicAction(privacyActionNougat, taskData);
        // 个人信息行为才发送
        if (actionNougat.get().getPersonal() == PrivacyStatusEnum.YES.getValue()) {
            RealTimeBehaviorLog behaviorLog = buildAppletBehaviorLog(privacyActionNougat, actionNougat.get());
            sendTaskDynamicLogMessage(buildDynamicLogMessage(behaviorLog, task.getTaskId()), task.getTaskId());
        }
    }

    protected JSONObject buildDynamicLogMessage(RealTimeBehaviorLog behaviorLog, Long taskId) {
        JSONObject message = new JSONObject();
        JSONObject cmdData = new JSONObject();
        JSONObject msgJson = new JSONObject();
        msgJson.put(CMD_DATA_ANDROID_OR_APPLET_LOG_ACTION_ID, behaviorLog.getTypeId());
        msgJson.put(CMD_DATA_ANDROID_OR_APPLET_ACTION_TIME, behaviorLog.getActionTime());
        msgJson.put(CMD_DATA_ANDROID_OR_APPLET_LOG_ACTION_NAME, behaviorLog.getTypeName());
        msgJson.put(CMD_DATA_ANDROID_OR_APPLET_LOG_PERSONAL, CommonUtil.beanToJson(behaviorLog.getIsPersonal()));
        msgJson.put(CMD_DATA_ANDROID_OR_APPLET_EXECUTOR_TYPE, behaviorLog.getExecutorType());
        msgJson.put(CMD_DATA_ANDROID_OR_APPLET_EXECUTOR, behaviorLog.getExecutor());
        msgJson.put(CMD_DATA_ANDROID_OR_APPLET_PACKAGE_NAME, behaviorLog.getPackageName());
        msgJson.put(CMD_DATA_ANDROID_OR_APPLET_LOG_ID, behaviorLog.getId());
        msgJson.put(CMD_DATA_ANDROID_OR_APPLET_LOG_BEHAVIOR_STAGE, behaviorLog.getBehaviorStage());
        cmdData.put(CMD_DATA_ANDROID_OR_APPLET_LOG, msgJson);
        cmdData.put(TASK_ID, taskId);
        cmdData.put(TYPE, DynamicCmdDataTypeEnum.LOG.getValue());
        message.put(DYNAMIC_TYPE, DetectionDynamicType.WECHAT_APPLET_DEEP.getValue());
        message.put(CMD_DATA, cmdData);
        return message;
    }

    private void appletAnalyticalStorage(AppletActionBO data, BehaviorStageEnum behaviorStage, DynamicTaskContext taskData, TTask task) {
        log.info("小程序储存行为");
    }

    @Autowired
    private TDetectionConfigMapper detectionConfigMapper;
    @Autowired
    private DetectionConfigService detectionConfigService;
    
    @Override
    public DetectionDeviceVO getUserDevice(Long userId, Integer terminalType) {
        DetectionDeviceVO deviceVO = new DetectionDeviceVO();

        if(terminalType == TerminalTypeEnum.HARMONY.getValue()) {
        	StfDeviceInfo device =  HarmonyUtils.findFreeApplyDevice();
        	deviceVO.setLastTimeDeviceId(device.getDeviceSerial());
        	deviceVO.setDeviceIds(new ArrayList<>());
        	return deviceVO;
        } else {
        	Integer interval =  ijiamiCommonProperties.getProperty("applet.device.user.minute.interval")==null ? 60:
                Integer.parseInt(ijiamiCommonProperties.getProperty("applet.device.user.minute.interval"));

	        List<TUserUseDevice> deviceList = userUseDeviceDAO.findAppletUseDevices(
	                Collections.singletonList(TerminalTypeEnum.getItem(terminalType)),
	                Arrays.asList(UserAppletDeviceStatusEnum.USING, UserAppletDeviceStatusEnum.LOGGED_IN), interval);
	        //获取多个平台占用的设备(进行合并以防某些设备漏)
	        List<String> morePlaformDeviceList = deviceManagerService.getAllUseDevice();
	        
	        String deviceSerial = "";
	        if( TerminalTypeEnum.WECHAT_APPLET.getValue() == terminalType && userId != null) {
	            TUserUseDevice device = userUseDeviceDAO.findAppletUseDevicesByUserId(TerminalTypeEnum.WECHAT_APPLET,
	                    Arrays.asList(UserAppletDeviceStatusEnum.USING, UserAppletDeviceStatusEnum.LOGGED_IN), userId);
	            deviceSerial = device == null ? "": device.getDeviceSerial();
	        } else if( TerminalTypeEnum.ALIPAY_APPLET.getValue() == terminalType && userId != null) {
	            TUserUseDevice device = userUseDeviceDAO.findAppletUseDevicesByUserId(TerminalTypeEnum.ALIPAY_APPLET,
	                    Arrays.asList(UserAppletDeviceStatusEnum.USING, UserAppletDeviceStatusEnum.LOGGED_IN), userId);
	            deviceSerial = device == null ? "": device.getDeviceSerial();
	        }
	        
	        // 5.1版本判断用户是否是指定设备
	        if(StringUtils.isBlank(deviceSerial)){
	        	Map<Long, TDetectionConfigVO> detectionConfigMap = detectionConfigService.initDetectionConfigData();
	        	log.info("1版本判断用户{}是否是指定设备={}",userId,detectionConfigMap.get(userId)!=null? detectionConfigMap.get(userId).getAndroidDeviceIps(): "null");
	    		if(detectionConfigMap != null && detectionConfigMap.get(userId) != null && 
	    				StringUtils.isNotBlank(detectionConfigMap.get(userId).getAndroidDeviceIps())){
	    			log.info("2版本判断用户{}是否是指定设备={}", userId, detectionConfigMap.get(userId).getAndroidDeviceIps());
	    			Long taskId = ConstantsUtils.deepDetectionStartTemp.get(userId);
	    	        if(taskId != null){
	    	        	TTaskExtendVO extendVO = taskExtendService.findTaskByTaskId(taskId);
	    	        	if(extendVO != null) {
	    	        		extendVO.setUserId(userId);
	    	        		deviceSerial = checkUseDeviceIsFree(userId, detectionConfigMap, extendVO);
	    	        	}
	    	        }
	    		} 
	        }
	        deviceVO.setLastTimeDeviceId(deviceSerial);
	
	        List<String> userDevicesList = getUserDevicesId(deviceList);
	        if(morePlaformDeviceList != null && morePlaformDeviceList.size()>0) {
	            userDevicesList.addAll(morePlaformDeviceList);
	        }
	
	        //去重
	        if(userDevicesList != null && userDevicesList.size()>0) {
	            Set<String> hashSet = new HashSet<>(userDevicesList);
	            userDevicesList = new ArrayList(hashSet);
	        }
	        
	        //5.1增加指定设备
	        List<String> appointDeviceList = detectionConfigMapper.getAllUserAppointDevice(userId);
			log.info("获取所有用户指定的设备={}", appointDeviceList==null? "null": com.alibaba.fastjson.JSONObject.toJSONString(appointDeviceList));
			if(appointDeviceList !=null && appointDeviceList.size()>0){
				for(String devices: appointDeviceList){
					if(StringUtils.isBlank(devices)){
						continue;
					}
					userDevicesList.addAll(Arrays.asList(devices.split(",")));
				}
			}
	        deviceVO.setDeviceIds(userDevicesList);
        }
        return deviceVO;
    }
    
    /**
     * 判断是否存在指定设备
     * @param tTask
     * @return
     */
    private String checkUseDeviceIsFree(Long createUserId, Map<Long, TDetectionConfigVO> detectionConfigMap, TTaskExtendVO extendVO){
		if(detectionConfigMap != null && detectionConfigMap.get(createUserId) != null && 
				StringUtils.isNotBlank(detectionConfigMap.get(createUserId).getAndroidDeviceIps())){
			String devicesIp[] = detectionConfigMap.get(createUserId).getAndroidDeviceIps().split(",");
			
			if(devicesIp != null && devicesIp.length>0) {
				log.info("createUserId={},深度检测-指定设备情况devicesIp={}",createUserId, devicesIp);
				for(int i=0;i<devicesIp.length;i++){
					try {
						StfDeviceInfo deviceInfo = StfUtils.findStfDeviceIsFree(ijiamiCommonProperties.getProperty("ijiami.stf.url"), ijiamiCommonProperties.getProperty("ijiami.stf.token"), devicesIp[i]);
						if(deviceInfo == null) {
							continue;
						}
						
						log.info("{}深度检测-指定设备情况devicesIp={},task_version={},task_model={},device_version={},device_mode={}",
								extendVO.getTaskId(), devicesIp, extendVO.getVersion(), extendVO.getModel(), deviceInfo.getOsVersion(), deviceInfo.getModel());
						
						//判断任务下发是否带了版本信息
						if(StringUtils.isNotBlank(extendVO.getVersion()) && !deviceInfo.getOsVersion().equals(extendVO.getVersion())) {
							continue;
						}
						//判断任务下发是否带了手机型号
						if(StringUtils.isNotBlank(extendVO.getModel()) && !deviceInfo.getModel().equals(extendVO.getModel())) {
							continue;
						}
						return deviceInfo.getDeviceSerial();
					} catch (Exception e) {
						e.getMessage();
					}
				}
			}
		}
    	return null;
    }

    private static List<String> getUserDevicesId(List<TUserUseDevice> appletUseDevicesList){
        List<String> deviceIds = new ArrayList<>();
        if(appletUseDevicesList == null || appletUseDevicesList.size()==0) {
            return deviceIds;
        }
        for (TUserUseDevice tUserUseDevice : appletUseDevicesList) {
            if (StringUtils.isBlank(tUserUseDevice.getDeviceSerial())) {
                continue;
            }
            deviceIds.add(tUserUseDevice.getDeviceSerial());
        }
        return deviceIds;
    }

    @Override
    public TUserUseDevice findAppletUseDevicesByUserId(Long userId, Integer terminalType) {
        return userUseDeviceDAO.findAppletUseDevicesByUserId(TerminalTypeEnum.getItem(terminalType),
                Arrays.asList(UserAppletDeviceStatusEnum.USING, UserAppletDeviceStatusEnum.LOGGED_IN), userId);
    }

}
