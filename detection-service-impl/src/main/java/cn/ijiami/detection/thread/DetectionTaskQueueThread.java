package cn.ijiami.detection.thread;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;

import cn.hutool.http.HttpUtil;
import cn.ijiami.detection.VO.ApiPushResultVO;
import cn.ijiami.detection.VO.PushProgressVO;
import cn.ijiami.detection.config.IjiamiCommonProperties;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.entity.TTaskQueue;
import cn.ijiami.detection.server.client.base.enums.DetectionStatusEnum;
import cn.ijiami.detection.enums.DetectionTypeEnum;
import cn.ijiami.detection.server.client.base.enums.DynamicAutoStatusEnum;
import cn.ijiami.detection.server.client.base.enums.DynamicDeviceTypeEnum;
import cn.ijiami.detection.server.client.base.enums.PageOrApiOrKafkaEnum;
import cn.ijiami.detection.enums.PushProgressEnum;
import cn.ijiami.detection.job.ApiPushProgressServer;
import cn.ijiami.detection.mapper.TAssetsMapper;
import cn.ijiami.detection.mapper.TTaskQueueMapper;
import cn.ijiami.detection.query.task.TaskCreateQuery;
import cn.ijiami.detection.service.api.IAssetsOfBigDataService;
import cn.ijiami.detection.service.api.IAssetsService;
import cn.ijiami.detection.service.api.ITaskExtendService;
import cn.ijiami.detection.service.api.ITaskService;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.ConstantsUtils;
import cn.ijiami.detection.utils.CrawlerUtils;
import cn.ijiami.detection.utils.HttpUtils;
import cn.ijiami.detection.utils.MD5Util;
import cn.ijiami.detection.utils.OssUtil;
import cn.ijiami.detection.utils.PlatformConstant;
import cn.ijiami.detection.utils.ftp.FtpUtil;
import cn.ijiami.detection.utils.ftp.SFTPUtil;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.file.service.api.IFileService;
import cn.ijiami.framework.file.vo.FileVO;
import cn.ijiami.framework.kit.utils.UuidUtil;
import cn.ijiami.manager.user.entity.User;
import cn.ijiami.manager.user.service.api.IUserService;
import net.sf.json.JSONObject;


public class DetectionTaskQueueThread implements Runnable {
	
	private static final Logger LOG = LoggerFactory.getLogger(DetectionTaskQueueThread.class);
	
	
    private IjiamiCommonProperties commonProperties;
    private TTaskQueueMapper taskQueueMapper;
    private IUserService userService;
    private IFileService defaultService;
    private IAssetsService assetsService;
    private ITaskService taskService;
    private TTaskQueue queue;
    private  ITaskExtendService taskExtendService;
    private RedisTemplate redisTemplate;
    private IAssetsOfBigDataService assetsOfBigDataService;
    private ApiPushProgressServer apiPushProgressServer;
    private TAssetsMapper assetsMapper;
	    
	public static final String APK = "apk";
	public static final String IPA = "ipa";
	
	public DetectionTaskQueueThread(IjiamiCommonProperties commonProperties, TTaskQueueMapper taskQueueMapper,IUserService userService,
			IFileService defaultService,IAssetsService assetsService,ITaskService taskService,TTaskQueue queue,
			ITaskExtendService taskExtendService,RedisTemplate redisTemplate,IAssetsOfBigDataService assetsOfBigDataService,
			ApiPushProgressServer apiPushProgressServer,TAssetsMapper assetsMapper){
		this.commonProperties = commonProperties;
		this.taskQueueMapper = taskQueueMapper;
		this.userService = userService;
		this.defaultService = defaultService;
		this.assetsService = assetsService;
		this.taskService = taskService;
		this.queue = queue;
		this.taskExtendService = taskExtendService;
		this.redisTemplate = redisTemplate;
		this.assetsOfBigDataService=assetsOfBigDataService;
		this.apiPushProgressServer = apiPushProgressServer;
		this.assetsMapper = assetsMapper;
	}

	@Override
	public void run() {
		sycnStartDetection(queue);
//		sycnStartDetection1(queue);
	}
	
	 /**
     * 下载文件,发送检测
     * @param queue
     */
    private boolean sycnStartDetection(TTaskQueue queue){
    	String bussinessId = queue.getBussinessId();
    	String appId = queue.getAppId();
    	String filePath = queue.getDownloadUrl();
    	String callbackUrl = queue.getCallbackUrl();
    	Long queueId = queue.getId();
    	LOG.info("sycnStartDetection发送检测.bussinessId={},appId={},filePath={},callbackUrl={},privacyPolicyPath={}",bussinessId,appId,filePath,callbackUrl, queue.getPrivacyPolicyPath());
        
        String localPath = commonProperties.getFilePath() + "/default/" + UuidUtil.uuid() + ".apk";
        if(filePath.endsWith(".ipa")|| filePath.endsWith(".IPA")){
        	localPath = commonProperties.getFilePath() +  "/default/" + UuidUtil.uuid() + ".ipa";
        }
        
        if(StringUtils.isBlank(bussinessId)) {
        	bussinessId = UUID.randomUUID().toString();
        }
        queue = taskQueueMapper.selectByPrimaryKey(queue.getId());
        if(queue == null) {
        	if(PlatformConstant.queue_map.get(queueId.toString())!=null) {
        		PlatformConstant.queue_map.remove(queueId.toString());
        	}
        	return false;
        }
        if(queue.getQueueStatus()!= 1) {
        	if(PlatformConstant.queue_map.get(queueId.toString())!=null) {
        		PlatformConstant.queue_map.remove(queueId.toString());
        	}
        	return false;
        }
        File file = new File(localPath);
        try {
        	String urlMethod = commonProperties.getProperty("url.upload.method");
        	
        	if(filePath.startsWith("http")) {
        		LOG.info("开始下载localPath={}", localPath);
        		long time1 = System.currentTimeMillis();
        		try {
        			URL url = new URL(filePath);
        	        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        	        // 设置连接超时为10秒，读取超时为30秒
        	        connection.setConnectTimeout(10_000);
        	        connection.setReadTimeout(30_000);
        	        
        	        try (InputStream in = connection.getInputStream(); 
        	             OutputStream out = new FileOutputStream(file)) {
        	            byte[] buffer = new byte[1024];
        	            int bytesRead;
        	            while ((bytesRead = in.read(buffer)) != -1) {
        	                out.write(buffer, 0, bytesRead);
        	            }
        	        } catch (IOException e) {
        	            LOG.error("文件下载失败", e);
        	            String res = CrawlerUtils.downloadFileByWget(localPath, filePath);  //注意科大讯飞的用命令下载会下载不全
        	            LOG.info("指令下载res={}", res);
//        	            if (res == null) {
//        	                System.setProperty("https.protocols", "SSLv2Hello,TLSv1,TLSv1.1,TLSv1.2,SSLv3");
//        	            }
        	        } finally {
        	            connection.disconnect(); // 确保连接关闭
        	        }
				} catch (Exception e) {
					e.getMessage();
					LOG.error("下载异常,开始重新下载{}",e.getMessage());
					FileUtils.copyURLToFile(new URL(filePath), file, 10_000, 30_000);
				}
        		long time2 = System.currentTimeMillis();
        		LOG.info("下载完成={},localPath={},下载耗时={}",filePath,localPath,(time2-time1));
        	}
        	
        	if(!new File(localPath).exists() && StringUtils.isNoneBlank(urlMethod) && urlMethod.equals("tengxunoss")) {
        		
        		LOG.info("开始下载localPath={}", localPath);
        		long time1 = System.currentTimeMillis();
        		OssUtil ossUtil = OssUtil.getInstance();
        		String fileName = null;
        		if(filePath.contains("/")) {
        			fileName = filePath.substring(filePath.lastIndexOf("/")+1,filePath.length());
        		}else {
        			fileName = filePath;
        		}
        		localPath = commonProperties.getFilePath() +  "/default" + File.separator;
        		int s2 =  ossUtil.downFile(localPath, fileName);
        		localPath = localPath+fileName;
        		long time2 = System.currentTimeMillis();
        		file = new File(localPath);
        		LOG.info("{}完成下载localPath={},下载耗时={}",s2, localPath, (time2-time1));
        	}
        	
        	if(!new File(localPath).exists() && StringUtils.isNoneBlank(urlMethod) && urlMethod.equals("ftp")) {
        		FtpUtil ftp = new FtpUtil();
        		String workPahth = filePath.substring(0,filePath.lastIndexOf("/")+1);
                String fileName = filePath.substring(filePath.lastIndexOf("/")+1,filePath.length());
        		ftp.downLoadFile(workPahth, fileName, localPath);
        	}
        	
        	if(!new File(localPath).exists() && StringUtils.isNoneBlank(urlMethod) && urlMethod.equals("sftp")) {
        		String username = this.commonProperties.getProperty("sftp.username");
        		String password = this.commonProperties.getProperty("sftp.password");
        		String port = this.commonProperties.getProperty("sftp.port");
        		String host = this.commonProperties.getProperty("sftp.host");
        		String privateKey = this.commonProperties.getProperty("sftp.privateKey");
        		SFTPUtil sftp = new SFTPUtil(username, password, host, Integer.parseInt(port), privateKey);
        		sftp.login();
        		String work_path = this.commonProperties.getProperty("sftp.work.path");
        		work_path = filePath.substring(0, filePath.lastIndexOf("/") + 1);
             	String fileName2 = filePath.substring(filePath.lastIndexOf("/") + 1, filePath.length());
             	sftp.download(work_path, fileName2, localPath);
             	sftp.logout();
        	}
        	
        	if(!new File(localPath).exists()){
        		String key = "download_queue"+ queue.getId();
                //检测失败超过三次就不会再送检
                Integer count = redisTemplate.opsForValue().get(key)==null? 0: Integer.valueOf(redisTemplate.opsForValue().get(key).toString());
                if(count<3){
                	LOG.info("队列下载失败===queueId={},count={},bussinessId={},url={}" , queue.getId(), count ,queue.getBussinessId(), queue.getDownloadUrl());
            	  	//检测失败记录，超过3次就不用整
        			redisTemplate.opsForValue().set(key, (count+1));
        			if(PlatformConstant.queue_map.get(queueId.toString())!=null) {
                		PlatformConstant.queue_map.remove(queueId.toString());
                	}
            	    return false;
                }
        		PushProgressVO push = new PushProgressVO();
                push.setDetectionType(DetectionTypeEnum.STATIC);
                push.setPushProgress(PushProgressEnum.PUSH_DOWN_FAIL);
                 
                ApiPushResultVO pushVO = new ApiPushResultVO();
     			pushVO.setBussinessId(bussinessId);
     			pushVO.setResultType(1);
     			pushVO.setData(push);
     			pushVO.setMessage("apk下载失败");
     			String result = apiPushProgressServer.pushMessage1(pushVO, callbackUrl, bussinessId);
     			if(result.contains("success") || result.contains("200") || StringUtils.isBlank(callbackUrl)) {
     				queue.setQueueStatus(3);
     				queue.setMessage("apk下载失败");
     				updateQueue(queue);
     			}else{
     				queue.setQueueStatus(3);
     				queue.setMessage("apk下载失败[通知失败]");
     				updateQueue(queue);
     			}
     			if(PlatformConstant.queue_map.get(queueId.toString())!=null) {
            		PlatformConstant.queue_map.remove(queueId.toString());
            	}
        		throw new IjiamiApplicationException("文件下载异常！");
        	}
        	
        	User user= userService.findUserById(queue.getCreateUserId());
        	if(user== null) {
        		user = new User();
                user.setUserId(queue.getCreateUserId()==null?1:queue.getCreateUserId());
        	}
            
            FileVO fileVO = new FileVO();
            // 上传前数据拼装，校验
            beforeUpload(fileVO, file);
            // 执行上传
            uploade(fileVO);
            
            //生成sha1
            String sha1 = "";
//    		try {
//    			long time1 = System.currentTimeMillis();
//    			sha1 = FileSafeCode.getSha1(new File(fileVO.getFilePath()));
//    			long time2 = System.currentTimeMillis();
//    			LOG.info("生成sha1码：{},耗时：{}ms", sha1, (time2-time1));
//    		} catch (OutOfMemoryError e) {
//    			e.printStackTrace();
//    		} catch (NoSuchAlgorithmException e) {
//    			e.printStackTrace();
//    		} catch (IOException e) {
//    			e.printStackTrace();
//    		}
    		
    		TAssets assets = null;
    		String is_upload = commonProperties.getProperty("file.is.upload.fastdfs");
    		//是否需要上传的文件服务器
    		if(StringUtils.isNoneBlank(is_upload) && is_upload.equals("false")) {
    			assets = assetsService.analysisApk(fileVO, false, user, false, filePath);
    		}else{
    			assets = assetsService.analysisApk(fileVO, false, user, false, null);
    		}
    		
            assets.setAppId(appId);
            if(assets.getCreateUserId()==null) {
            	assets.setCreateUserId(queue.getCreateUserId());
            }
            if(StringUtils.isNoneBlank(queue.getPrivacyPolicyPath())) {
            	assets.setPrivacyPolicyPath(queue.getPrivacyPolicyPath());
            }
			//解析完成后调用大数据平台接口获取资产功能类型
			assets=assetsOfBigDataService.getAssetsBigData(assets);
            assets.setSourceFileName(getFileName(queue.getDownloadUrl(),file).trim());
            assetsService.saveOrUpdate(assets);
            
            JSONObject res_json = new JSONObject();
            
            //添加检测任务
            TaskCreateQuery taskCreateQuery = new TaskCreateQuery();
            taskCreateQuery.setUserId(queue.getCreateUserId());
            List<Long> assetsIds = new ArrayList<>();
            assetsIds.add(assets.getId());
            taskCreateQuery.setAssetsIds(assetsIds);
            taskCreateQuery.setDynamicDeviceTypeEnum(DynamicDeviceTypeEnum.getItem(1)); //1 云手机
            taskCreateQuery.setTerminalTypeEnum(assets.getTerminalType());
            taskCreateQuery.setDetectionType(1);
            taskCreateQuery.setBussinessId(bussinessId);
            taskCreateQuery.setCallbackUrl(callbackUrl);
            taskCreateQuery.setVersion(queue.getVersion());
            taskCreateQuery.setModel(queue.getModel());
			taskCreateQuery.setCustomLawsGroupId(queue.getCustomLawsGroupId());
			taskCreateQuery.setActionFilterGroupId(queue.getActionFilterGroupId());
			taskCreateQuery.setLaws(queue.getLaws());
			if(queue.getIsApi()!=null && queue.getIsApi()== PageOrApiOrKafkaEnum.IS_API.getValue()){
				taskCreateQuery.setPageOrApiOrKafkaEnum(PageOrApiOrKafkaEnum.IS_API);
			}else if(queue.getIsApi()!=null && queue.getIsApi()== PageOrApiOrKafkaEnum.IS_KAFKA.getValue()){
				taskCreateQuery.setPageOrApiOrKafkaEnum(PageOrApiOrKafkaEnum.IS_KAFKA);
			}else{
				taskCreateQuery.setPageOrApiOrKafkaEnum(PageOrApiOrKafkaEnum.IS_PAGE);
			}
            
            
            //同个业务ID看看是否存在进行中的检测
            TTask apiTask = taskExtendService.getTaskByBussinessId(bussinessId);
            boolean start = true;
            if(apiTask!=null && (apiTask.getDynamicStatus()==DynamicAutoStatusEnum.DETECTION_AUTO_WAITING  
            		|| apiTask.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_IN
					|| apiTask.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_IOS_STATIC_FUNC_ANALYZE
            		|| apiTask.getTaskTatus() == DetectionStatusEnum.DETECTION_IN
            		|| apiTask.getTaskTatus() == DetectionStatusEnum.DETECTION_NOSTART)) {
            	start = false;
            }
            Long taskId = null;
            if(start){
            	taskId = taskService.startTask(taskCreateQuery);
            }

			res_json.put("md5", assets.getMd5());
			res_json.put("sha1", sha1);
			res_json.put("assets_id", assets.getId());
			res_json.put("pakage", assets.getPakage());
			res_json.put("version", assets.getVersion());
			res_json.put("bussinessId", bussinessId==null? taskId : bussinessId);

			ApiPushResultVO pushVO = new ApiPushResultVO();
			pushVO.setBussinessId(bussinessId);
			pushVO.setResultType(4);
			pushVO.setData(res_json);
			
//            Long taskId = taskService.startTask(taskCreateQuery);
            LOG.info("任务启动成功taskId={}",taskId);
            if(StringUtils.isBlank(callbackUrl)) {
            	queue.setQueueStatus(2);
				queue.setMessage("启动完成");
				updateQueue(queue);
//				apiPushProgressServer.pushMessage1(pushVO, callbackUrl, bussinessId);
        		return true;
        	}
            

			String result = apiPushProgressServer.pushMessage1(pushVO, callbackUrl, bussinessId);
			if(result.contains("success") || result.contains("200")) {
				queue.setQueueStatus(2);
				queue.setMessage("启动完成");
				updateQueue(queue);
			}else{
				queue.setQueueStatus(2);
				queue.setMessage("启动完成[通知失败]");
				updateQueue(queue);
			}
			return true;
        } catch (Exception e) {
            e.getMessage();
			PushProgressVO push = new PushProgressVO();
			push.setDetectionType(DetectionTypeEnum.STATIC);
			push.setPushProgress(PushProgressEnum.PUSH_ERROR);

			ApiPushResultVO pushVO = new ApiPushResultVO();
			pushVO.setBussinessId(bussinessId);
			pushVO.setResultType(1);
			pushVO.setData(push);
			pushVO.setMessage("APK解析异常！"+e.getMessage());
            LOG.error("fileUploadAndDetect.APK解析异常！={}",e.getMessage());
            if(StringUtils.isBlank(callbackUrl) || e.getMessage().contains("JDBC transaction")) {
            	if(e.getMessage().contains("JDBC transaction")) {
            		queue.setQueueStatus(1);
            	}else {
            		queue.setQueueStatus(3);
            	}
				queue.setMessage("APK解析异常！"+e.getMessage());
				updateQueue(queue);
//				apiPushProgressServer.pushMessage1(pushVO, callbackUrl, bussinessId);
        		return false;
        	}
			String result = apiPushProgressServer.pushMessage1(pushVO, callbackUrl, bussinessId);
			if(result.contains("success") || result.contains("200")) {
				queue.setQueueStatus(3);
				queue.setMessage("APK解析异常！"+e.getMessage());
				updateQueue(queue);
			}else{
				queue.setQueueStatus(3);
				queue.setMessage("APK解析异常！[通知失败]"+e.getMessage());
				updateQueue(queue);
			}
        }finally {
        	if(PlatformConstant.queue_map.get(queue.getId().toString())!=null) {
        		PlatformConstant.queue_map.remove(queue.getId().toString());
        	}
        	if(file.exists()){
        		file.delete();
        	}
		}
		return false;
    }
    
    
    private boolean sycnStartDetection1(TTaskQueue queue){
    	String bussinessId = queue.getBussinessId();
    	String appId = queue.getAppId();
    	String filePath = queue.getDownloadUrl();
    	String callbackUrl = queue.getCallbackUrl();
    	Long queueId = queue.getId();
    	LOG.info("sycnStartDetection发送检测.bussinessId={},appId={},filePath={},callbackUrl={}",bussinessId,appId,filePath,callbackUrl);
        
        String localPath = commonProperties.getFilePath() + "/default/" + UuidUtil.uuid() + ".apk";
        if(filePath.contains(".ipa")|| filePath.contains(".IPA")){
        	localPath = commonProperties.getFilePath() +  "/default/" + UuidUtil.uuid() + ".ipa";
        }
        
        if(StringUtils.isBlank(bussinessId)) {
        	bussinessId = UUID.randomUUID().toString();
        }
        queue = taskQueueMapper.selectByPrimaryKey(queue.getId());
        if(queue == null) {
        	if(PlatformConstant.queue_map.get(queueId.toString())!=null) {
        		PlatformConstant.queue_map.remove(queueId.toString());
        	}
        	return false;
        }
        if(queue.getQueueStatus()!= 1) {
        	if(PlatformConstant.queue_map.get(queueId.toString())!=null) {
        		PlatformConstant.queue_map.remove(queueId.toString());
        	}
        	return false;
        }
        File file = new File(localPath);
        try {
        	String urlMethod = commonProperties.getProperty("url.upload.method");
        	
        	if(filePath.startsWith("http")) {
        		LOG.info("开始下载localPath={}", localPath);
        		long time1 = System.currentTimeMillis();
//        		FileUtils.copyURLToFile(new URL(filePath), file);
        		if(!new File(localPath).exists()){
        			HttpUtils.download(filePath, localPath, null);
        		}
        		long time2 = System.currentTimeMillis();
        		LOG.info("下载完成={},localPath={},下载耗时={}",filePath,localPath,(time2-time1));
        	}
        	
        	if(!new File(localPath).exists() && StringUtils.isNoneBlank(urlMethod) && urlMethod.equals("tengxunoss")) {
        		
        		LOG.info("开始下载localPath={}", localPath);
        		long time1 = System.currentTimeMillis();
        		OssUtil ossUtil = OssUtil.getInstance();
        		String fileName = null;
        		if(filePath.contains("/")) {
        			fileName = filePath.substring(filePath.lastIndexOf("/")+1,filePath.length());
        		}else {
        			fileName = filePath;
        		}
        		localPath = commonProperties.getFilePath() +  "/default" + File.separator;
        		int s2 =  ossUtil.downFile(localPath, fileName);
        		localPath = localPath+fileName;
        		long time2 = System.currentTimeMillis();
        		file = new File(localPath);
        		LOG.info("{}完成下载localPath={},下载耗时={}",s2, localPath, (time2-time1));
        	}
        	
        	if(!new File(localPath).exists() && StringUtils.isNoneBlank(urlMethod) && urlMethod.equals("ftp")) {
        		FtpUtil ftp = new FtpUtil();
        		String workPahth = filePath.substring(0,filePath.lastIndexOf("/")+1);
                String fileName = filePath.substring(filePath.lastIndexOf("/")+1,filePath.length());
        		ftp.downLoadFile(workPahth, fileName, localPath);
        	}
        	
        	if(!new File(localPath).exists() && StringUtils.isNoneBlank(urlMethod) && urlMethod.equals("sftp")) {
        		String username = this.commonProperties.getProperty("sftp.username");
        		String password = this.commonProperties.getProperty("sftp.password");
        		String port = this.commonProperties.getProperty("sftp.port");
        		String host = this.commonProperties.getProperty("sftp.host");
        		String privateKey = this.commonProperties.getProperty("sftp.privateKey");
        		SFTPUtil sftp = new SFTPUtil(username, password, host, Integer.parseInt(port), privateKey);
        		sftp.login();
        		String work_path = this.commonProperties.getProperty("sftp.work.path");
        		work_path = filePath.substring(0, filePath.lastIndexOf("/") + 1);
             	String fileName2 = filePath.substring(filePath.lastIndexOf("/") + 1, filePath.length());
             	sftp.download(work_path, fileName2, localPath);
             	sftp.logout();
        	}
        	
        	if(!new File(localPath).exists()){
        		String key = "download_queue"+ queue.getId();
                //检测失败超过三次就不会再送检
                Integer count = redisTemplate.opsForValue().get(key)==null? 0: Integer.valueOf(redisTemplate.opsForValue().get(key).toString());
                if(count<3){
                	LOG.info("队列下载失败===queueId={},count={},bussinessId={},url={}" , queue.getId(), count ,queue.getBussinessId(), queue.getDownloadUrl());
            	  	//检测失败记录，超过3次就不用整
        			redisTemplate.opsForValue().set(key, (count+1));
        			if(PlatformConstant.queue_map.get(queueId.toString())!=null) {
                		PlatformConstant.queue_map.remove(queueId.toString());
                	}
            	    return false;
                }
        		PushProgressVO push = new PushProgressVO();
                push.setDetectionType(DetectionTypeEnum.STATIC);
                push.setPushProgress(PushProgressEnum.PUSH_DOWN_FAIL);
                 
                ApiPushResultVO pushVO = new ApiPushResultVO();
     			pushVO.setBussinessId(bussinessId);
     			pushVO.setResultType(1);
     			pushVO.setData(push);
     			pushVO.setMessage("apk下载失败");
     			String result = pushMessage(pushVO, callbackUrl, bussinessId);
     			if(result.contains("success") || result.contains("200") || StringUtils.isBlank(callbackUrl)) {
     				queue.setQueueStatus(3);
     				queue.setMessage("apk下载失败");
     				updateQueue(queue);
     			}else{
     				queue.setQueueStatus(3);
     				queue.setMessage("apk下载失败[通知失败]");
     				updateQueue(queue);
     			}
     			if(PlatformConstant.queue_map.get(queueId.toString())!=null) {
            		PlatformConstant.queue_map.remove(queueId.toString());
            	}
        		throw new IjiamiApplicationException("文件下载异常！");
        	}
        	
        	User user= userService.findUserById(queue.getCreateUserId());
        	if(user== null) {
        		user = new User();
                user.setUserId(queue.getCreateUserId()==null?1:queue.getCreateUserId());
        	}
            
            FileVO fileVO = new FileVO();
            // 上传前数据拼装，校验
            beforeUpload(fileVO, file);
            // 执行上传
            uploade(fileVO);
            
            //生成sha1
            String sha1 = "";
//    		try {
//    			long time1 = System.currentTimeMillis();
//    			sha1 = FileSafeCode.getSha1(new File(fileVO.getFilePath()));
//    			long time2 = System.currentTimeMillis();
//    			LOG.info("生成sha1码：{},耗时：{}ms", sha1, (time2-time1));
//    		} catch (OutOfMemoryError e) {
//    			e.printStackTrace();
//    		} catch (NoSuchAlgorithmException e) {
//    			e.printStackTrace();
//    		} catch (IOException e) {
//    			e.printStackTrace();
//    		}
    		
    		TAssets assets = null;
    		String is_upload = commonProperties.getProperty("file.is.upload.fastdfs");
    		//是否需要上传的文件服务器
    		if(StringUtils.isNoneBlank(is_upload) && is_upload.equals("false")) {
    			assets = assetsService.analysisApk(fileVO, false, user, false, filePath);
    		}else{
    			assets = assetsService.analysisApk(fileVO, false, user, false, null);
    		}
            assets.setAppId(appId);
            if(assets.getCreateUserId()==null) {
            	assets.setCreateUserId(queue.getCreateUserId());
            }
            if(StringUtils.isNoneBlank(queue.getPrivacyPolicyPath())) {
            	assets.setPrivacyPolicyPath(queue.getPrivacyPolicyPath());
            }
            assetsService.saveOrUpdate(assets);
            
            JSONObject res_json = new JSONObject();
            
            //添加检测任务
            TaskCreateQuery taskCreateQuery = new TaskCreateQuery();
            taskCreateQuery.setUserId(queue.getCreateUserId());
            List<Long> assetsIds = new ArrayList<>();
            assetsIds.add(assets.getId());
            taskCreateQuery.setAssetsIds(assetsIds);
            taskCreateQuery.setDynamicDeviceTypeEnum(DynamicDeviceTypeEnum.getItem(1)); //1 云手机
            taskCreateQuery.setTerminalTypeEnum(assets.getTerminalType());
            taskCreateQuery.setDetectionType(1);
            taskCreateQuery.setBussinessId(bussinessId);
            taskCreateQuery.setCallbackUrl(callbackUrl);
            
            //同个业务ID看看是否存在进行中的检测
            TTask apiTask = taskExtendService.getTaskByBussinessId(bussinessId);
            boolean start = true;
            if(apiTask!=null && (apiTask.getDynamicStatus()==DynamicAutoStatusEnum.DETECTION_AUTO_WAITING  
            		|| apiTask.getDynamicStatus()==DynamicAutoStatusEnum.DETECTION_AUTO_IN
            		|| apiTask.getTaskTatus()==DetectionStatusEnum.DETECTION_IN
            		|| apiTask.getTaskTatus()==DetectionStatusEnum.DETECTION_NOSTART)) {
            	start = false;
            }
            Long taskId = null;
            if(start){
            	taskId = taskService.startTask(taskCreateQuery);
            }
            
//            Long taskId = taskService.startTask(taskCreateQuery);
            LOG.info("任务启动成功taskId={},bussinessId={}",taskId, queue.getBussinessId());
            if(StringUtils.isBlank(callbackUrl)) {
            	queue.setQueueStatus(2);
				queue.setMessage("启动完成");
				updateQueue(queue);
				if(PlatformConstant.queue_map.get(queueId.toString())!=null) {
	        		PlatformConstant.queue_map.remove(queueId.toString());
	        	}
        		return true;
        	}
            res_json.put("md5", assets.getMd5());
            res_json.put("sha1", sha1);
            res_json.put("assets_id", assets.getId());
            res_json.put("pakage", assets.getPakage());
            res_json.put("version", assets.getVersion());
            res_json.put("bussinessId", bussinessId==null?taskId : bussinessId);
            
            ApiPushResultVO pushVO = new ApiPushResultVO();
			pushVO.setBussinessId(bussinessId);
			pushVO.setResultType(4);
			pushVO.setData(res_json);
			String result = pushMessage(pushVO, callbackUrl, bussinessId);
			if(result.contains("success") || result.contains("200")) {
				queue.setQueueStatus(2);
				queue.setMessage("启动完成");
				updateQueue(queue);
			}else{
				queue.setQueueStatus(2);
				queue.setMessage("启动完成[通知失败]");
				updateQueue(queue);
			}
			if(PlatformConstant.queue_map.get(queueId.toString())!=null) {
        		PlatformConstant.queue_map.remove(queueId.toString());
        	}
			
			return true;
        } catch (Exception e) {
            e.getMessage();
            LOG.error("fileUploadAndDetect.APK解析异常！={}",e.getMessage());
            if(StringUtils.isBlank(callbackUrl)) {
            	queue.setQueueStatus(3);
				queue.setMessage("APK解析异常！"+e.getMessage());
				updateQueue(queue);
				if(PlatformConstant.queue_map.get(queueId.toString())!=null) {
	        		PlatformConstant.queue_map.remove(queueId.toString());
	        	}
        		return false;
        	}
            PushProgressVO push = new PushProgressVO();
            push.setDetectionType(DetectionTypeEnum.STATIC);
            push.setPushProgress(PushProgressEnum.PUSH_ERROR);
            
            ApiPushResultVO pushVO = new ApiPushResultVO();
			pushVO.setBussinessId(bussinessId);
			pushVO.setResultType(4);
			pushVO.setData(push);
			pushVO.setMessage("APK解析异常！"+e.getMessage());
			String result = pushMessage(pushVO, callbackUrl, bussinessId);
			
			if(result.contains("success") || result.contains("200")) {
				queue.setQueueStatus(3);
				queue.setMessage("APK解析异常！"+e.getMessage());
				updateQueue(queue);
			}else{
				queue.setQueueStatus(3);
				queue.setMessage("APK解析异常！[通知失败]"+e.getMessage());
				updateQueue(queue);
			}
        }finally {
        	if(PlatformConstant.queue_map.get(queue.getId().toString())!=null) {
        		PlatformConstant.queue_map.remove(queue.getId().toString());
        	}
        	if(file.exists()){
        		file.delete();
        	}
		}
		return false;
    }
    
    // 上传前操作
    private void beforeUpload(FileVO fileVO, File file) throws IOException, IjiamiApplicationException {
        fileVO.setInputStream(new FileInputStream(file));
        fileVO.setFileSize(FileUtils.sizeOf(file));

		String fileName = file.getName().replaceAll(" ", "").trim();
		CommonUtil.checkAppFileExt(fileName);
        String MD5 = MD5Util.getFileMD5(new FileInputStream(file));
        fileName = UUID.randomUUID().toString().replace("_", "") + "_" + MD5
                + fileName.substring(fileName.indexOf("."));
        fileVO.setFileName(fileName);
        Map<String, Object> params = new HashMap<>();
        DecimalFormat df = new DecimalFormat("0.00");
        String parseLong = df.format((double) fileVO.getFileSize() / ConstantsUtils.BYTENUM / ConstantsUtils.BYTENUM);
        params.put("size", parseLong);
        fileVO.setParams(params);
    }
    
    // 执行上传
    private void uploade(FileVO fileVO) throws IjiamiApplicationException, IOException {
        fileVO = defaultService.upload(fileVO);
    }
    

    private void updateQueue(TTaskQueue queue){
    	taskQueueMapper.updateByPrimaryKeySelective(queue);
    }
    
    @Async
    private <T> String pushMessage(T o, String url,String bussinessId) {
    	if(StringUtils.isBlank(url)){
    		return "";
    	}
    	LOG.info("开始推送bussinessId={},URL={},json={}",bussinessId,url,com.alibaba.fastjson.JSONObject.toJSONString(o));
        String response = HttpUtil.post(url, com.alibaba.fastjson.JSONObject.toJSONString(o));
        LOG.info("推送返回结果bussinessId={},pushMessage={}",bussinessId,response);
        
//        String callbackUrl = "http://61.54.25.50:5521/intoappinfo";
//    	String json = "{\"channel\":\"应用宝\",\"apk_pkg\": \"com.tencent.mm1\", \"privacy_url\":\"https://cftweb.3g.qq.com/privacy/privacyPolicy?content_id=e4056cc597f6bf204014e31a1429a36e\"}";

        
        return response;
    }
    
	/**
	 * 比较上传的url是否包含源文件名，不包含则以下载的文件名作为源文件名
	 * @param url
	 * @param file
	 * @return
	 */
    private String getFileName(String url,File file){
    	String sourceFileName="";
    	if(StringUtils.isNotBlank(url) && url.toUpperCase().endsWith(".APK")){
    		int star = url.lastIndexOf("/");
    		int end = url.length();
    		sourceFileName=url.substring(star+1,end);
		}else if(file!=null){
    		sourceFileName=file.getName();
		}
		return sourceFileName;
	}

}
