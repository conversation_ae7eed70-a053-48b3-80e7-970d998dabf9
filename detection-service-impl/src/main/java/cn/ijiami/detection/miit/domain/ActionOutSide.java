package cn.ijiami.detection.miit.domain;

import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

@Data
public class ActionOutSide implements Serializable {

    /**
     * 行为id
     */
    private Long              actionId;

    @ApiModelProperty(value = "行为阶段 授权前行为、前台运行行为、后台运行行为")
    private BehaviorStageEnum behaviorStage;

    @ApiModelProperty(value = "触发时间")
    private Date actionTime;

    @ApiModelProperty(value = "主体为2存储sdkId，json格式")
    private String sdkIds;

    @ApiModelProperty(value = "主体类型 1APP 2SDK")
    private Integer executorType;

    @ApiModelProperty(value = "主体")
    private String executor;

    /**
     * 请求类型
     */
    @ApiModelProperty(value = "请求类型")
    private String method;

    @ApiModelProperty(value = "url地址")
    private String url;

    /**
     * 域名
     */
    @ApiModelProperty(value = "域名")
    private String host;

    /**
     * 端口
     */
    @ApiModelProperty(value = "端口")
    private String port;

    /**
     * cookie
     */
    @ApiModelProperty(value = "Cookie")
    private String cookie;

    /**
     * 协议
     */
    @ApiModelProperty(value = "协议")
    private String protocol;

    /**
     * 请求地址
     */
    @ApiModelProperty(value = "请求地址")
    private String address;


    @ApiModelProperty(value = "是否为境外IP")
    private Integer outside = 0;


    @ApiModelProperty(value = "ip")
    private String ip;

    @ApiModelProperty(value = "包名")
    private String packageName;
}
