package cn.ijiami.detection.miit.point.android.law164;

import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * APP未见向用户告知且未经用户同意，在静默状态下或在后台运行时，存在按照一定频次收集位置信息、IMEI、通讯录、短信、图片等信息的行为，非服务所必需且无合理应用场景，超出与收集个人信息时所声称的目的具有直接或合理关联的范围。
 * <p>
 * <blockquote><pre>
 * 1、有隐私政策
 * 2、同意隐私政策前，检测位置信息、IMEI、通讯录、短信、图片等信息的行为是否超过每秒1次的频率
 * 3、将APP退至后台，检测位置信息、IMEI、通讯录、短信、图片等信息的行为是否超过每秒1次的频率
 *
 * 1、无隐私政策
 * 2、将APP退至后台，检测位置信息、IMEI、通讯录、短信、图片等信息的行为是否超过每秒1次的频率
 *
 * 分两种情况
 * 1、无隐私政策
 * 2、共XXX次（位置信息、IMEI、通讯录、短信、图片行为）
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 「后台行为」
 *
 * 1、隐私政策截图
 * 3、共XXX次（位置信息、IMEI、通讯录、短信、图片行为）
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 「授权前行为」「后台行为」
 * 涉及行为：10002L, 12001L, 12002L, 12003L, 12004L, 12005L, 12006L, 14005L, 14006L, 14007L, 14008L, 14009L, 14010L, 14011L, 14012L, 13001L,
 *                         13002L, 13003L, 13004L, 13005L, 14001L, 14002L, 14003L, 14004L, 21003L, 16004L
 *
 *
 * </pre></blockquote>
 *
 *
 * 场景一：修改
 * 1、没有有隐私政策
 * 2、某一类行为存在固定频率触发（只要存在3次以上即存在风险，时间间隔中间存在其他行为或者其他次数，都不管）
 * 例如：固定3秒触发获取mac地址行为
 * 【精准到秒即可】
 * 【授权前行为】【后台行为】
 *
 * 场景二：修改
 * 1、有隐私政策
 * 2、有关键词
 * 2、某一类行为存在固定频率触发（只要存在3次以上即存在风险，时间间隔中间存在其他行为或者其他次数，都不管）
 * 例如：固定3秒触发获取mac地址行为
 * 【精准到秒即可】
 * 【授权前行为】
 *
 * 场景三：修改
 * 1、有隐私政策
 * 2、没有关键词
 * 2、某一类行为存在固定频率触发（只要存在3次以上即存在风险，时间间隔中间存在其他行为或者其他次数，都不管）
 * 例如：固定3秒触发获取mac地址行为
 * 【精准到秒即可】
 * 【授权前行为】【后台行为】
 *
 *
 * <AUTHOR>
 * @date 2020/12/22 17:42
 **/
@EnableDetectPoint
public class DetectPoint10206 extends AbstractDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        if (!commonDetectInfo.isHasPrivacyPolicy()) {
            return nonePrivacyPolicyContentScene(commonDetectInfo, customDetectInfo);
        }else {
            return havePrivacyPolicyContentScene(commonDetectInfo, customDetectInfo);
        }
    }

    protected DetectResult nonePrivacyPolicyContentScene(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        //授权前行为
        List<ActionAnalyse> grantAnalyses = filterCycleTriggerAction(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GRANT);
        List<ActionAnalyse> analysesList = new ArrayList<>(grantAnalyses);
        //后台行为
        List<ActionAnalyse> groundAnalyses = filterCycleTriggerAction(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GROUND);
        analysesList.addAll(groundAnalyses);
        
        
        DetectResult detectResult = getBaseDetectResult(commonDetectInfo, customDetectInfo);
        // 隐私政策截图截图地址
        detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
        detectResult.setAnalysisResult(analysesList);
        detectResult.setComplianceStatus(analysesList.isEmpty() ? MiitDetectStatusEnum.NON_INVOLVED : MiitDetectStatusEnum.NON_COMPLIANCE);
        
        
        return detectResult;
    }

    protected DetectResult havePrivacyPolicyContentScene(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        //场景2
        //授权前行为
        List<ActionAnalyse> grantAnalyses = filterCycleTriggerAction(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GRANT);
        List<ActionAnalyse> analysesList = new ArrayList<>(grantAnalyses);
        boolean scene2NonCompliance = CollectionUtils.isNotEmpty(grantAnalyses);
        //场景3
        //后台行为
        List<ActionAnalyse> groundAnalyses = filterCycleTriggerAction(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GROUND);
        //筛选出不合规的行为
        List<ActionAnalyse> nonComplianceGroundAnalyses = checkNotComplianceActionExistInFragment(customDetectInfo, getBaseTargetPrivacyPolicyText(commonDetectInfo, customDetectInfo), groundAnalyses);
        analysesList.addAll(nonComplianceGroundAnalyses);
        boolean notExistWords = !buildResultByActionExistWords(commonDetectInfo, customDetectInfo, nonComplianceGroundAnalyses);
        //有固定频率，增强判断
        boolean scene3NonCompliance = CollectionUtils.isNotEmpty(analysesList) && notExistWords;

        DetectResult detectResult = getBaseDetectResult(commonDetectInfo, customDetectInfo);
        // 隐私政策截图截图地址
        detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
        // 有固定频率后看看是否有关键词
        detectResult.setAnalysisResult(analysesList);
        detectResult.setComplianceStatus(scene2NonCompliance || scene3NonCompliance ? MiitDetectStatusEnum.NON_COMPLIANCE : MiitDetectStatusEnum.NON_INVOLVED);
        return detectResult;
    }

}
