package cn.ijiami.detection.miit.point.android.law35273;

import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint200501.java
 * @Description
 * 35273检测
 * 9.7.e
 * 应要求第三方根据本标准相关要求向个人信息主体征得收集个人信息同意，必要时核验其实现的方式；
 * 规则
 * 存在风险
 * 1 SDK在隐私政策授权前收集个人信息。
 * 2 SDK收集的个人信息未在隐私政策中声明。
 */
@EnableDetectPoint
public class DetectPoint290702 extends Law35273DetectPoint {
    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.buildNonInvolved(commonDetectInfo, customDetectInfo);
        // 使用set，避免同一条数据被添加两次
        Set<ActionAnalyse> analysisResult = new HashSet<>();
        List<String> conclusionList = new ArrayList<>();
        List<String> suggestionList = new ArrayList<>();
        // 隐私政策授权前收集检查
        List<ActionAnalyse> grantList = filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GRANT);
        // 隐私政策授权前收集检查不用判断隐私条款
        List<ActionAnalyse> grantCollectList = grantList.stream()
                .filter(ActionAnalyse::getPersonal)
                .filter(a -> ExecutorTypeEnum.SDK.getValue().equals(a.getExecutorType()))
                .collect(Collectors.toList());
        if (!grantCollectList.isEmpty()) {
            List<String> actionNames = grantCollectList
                    .stream()
                    .map(ActionAnalyse::getActionName)
                    .distinct()
                    .collect(Collectors.toList());
            analysisResult.addAll(grantCollectList);
            conclusionList.add(buildSequenceTextFormat("%s在征得用户同意隐私政策前，存在%s等收集个人信息行为。",
                    pluginName(commonDetectInfo), String.join("、", actionNames)));
            suggestionList.add(buildSequenceTextFormat("在征得用户同意隐私政策前，%s不能收集任何个人信息。", pluginName(commonDetectInfo)));
        }
        List<ActionAnalyse> actionAnalyseList = new ArrayList<>();
        // 判断是否SDK收集的个人信息未在隐私政策中声明
        actionAnalyseList.addAll(grantList);
        actionAnalyseList.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_FRONT));
        actionAnalyseList.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GROUND));
        actionAnalyseList.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_EXIT));
        // APP和SDK行为检测
        CheckNonePrivacyActionResult result = checkNonePrivacySdkAction(commonDetectInfo, customDetectInfo, actionAnalyseList);
        detectResult.setPrivacyPolicyFragment(joinFragmentText(commonDetectInfo, result));
        if (!result.isNonInvolved()) {
            conclusionList.addAll(result.getConclusionList());
            suggestionList.addAll(result.getSuggestionList());
            analysisResult.addAll(result.getAnalysisResult());
        }
        // 是否有隐私政策
        // 隐私政策截图截图地址
        detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
        if (!conclusionList.isEmpty()) {
            detectResult.setConclusion(buildSequenceText(conclusionList));
            detectResult.setSuggestion(buildSequenceText(suggestionList));
            detectResult.setAnalysisResult(new ArrayList<>(analysisResult));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
        } else {
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        }
        return detectResult;
    }

}
