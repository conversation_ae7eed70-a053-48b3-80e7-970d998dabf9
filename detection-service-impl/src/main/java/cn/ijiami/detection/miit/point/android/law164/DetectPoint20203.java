package cn.ijiami.detection.miit.point.android.law164;

import java.util.ArrayList;
import java.util.List;

import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import org.springframework.util.CollectionUtils;

import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.helper.PermissionNameHelper;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.kit.MiitActionKit;
import cn.ijiami.detection.miit.kit.MiitWordKit;

/**
 * APP非服务所必需或无合理应用场景，超范围频繁自启动或关联启动第三方APP。
 * <p>
 * <blockquote><pre>
 * 共XXX次（自启动或关联启动的行为）
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 「前台行为」
 *
 * 新增了启动Activity、启动Service行为，根据行为数据的触发时间，按秒统计触发频次，超过1次则为不合规
 * 涉及行为：21004L,21005L
 *
 * </pre></blockquote>
 *
 * <AUTHOR>
 * @date 2020/12/22 17:42
 **/
@EnableDetectPoint
public class DetectPoint20203 extends AbstractDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return buildNonInvolved(commonDetectInfo, customDetectInfo);
    }

    public DetectResult oldDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        List<ActionAnalyse> nameFilterAfter = getNameFilterAfter(commonDetectInfo, customDetectInfo);
        DetectResult detectResult = buildBaseResultByActionAnalyse(commonDetectInfo, customDetectInfo, nameFilterAfter);
        // 隐私政策截图截图地址
        detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
        detectResult.setPrivacyPolicyFragment(null);
        // 统计超过每秒1次的频率的数据
        long count = nameFilterAfter.stream().filter(a -> a.getFrequency() >= 1).count();

        boolean isSell = false;     //自启动
        boolean isrelation = false; //关联

        if(nameFilterAfter.size()>0) {
            for (ActionAnalyse actionAnalyse : nameFilterAfter) {
                if (MiitActionKit.APP_OWN_START.equals(actionAnalyse.getActionName())) {
                    isSell = true;
                }
                if ( MiitActionKit.APP_ASSOCIATE_START.equals(actionAnalyse.getActionName())) {
                    isrelation = true;
                }
            }
        }

        detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        if(nameFilterAfter==null || nameFilterAfter.size()==0) {
            return detectResult;
        }

        String privacyPolicyText = commonDetectInfo.getPrivacyPolicyContent();
        //自启动
        boolean isSellExist = MiitWordKit.checkTextMeetTheRegex(privacyPolicyText, PermissionNameHelper.SELLFILTERSTARTUP);
        //关联启动
        boolean isExist = MiitWordKit.checkTextMeetTheRegex(privacyPolicyText, PermissionNameHelper.FILTERSTARTUP);
        //行为有自启动和关联启动情况 隐私文本就要包含 “自启动”、“关联启动”
        if(isSell ==true && isrelation==true) {
            if(isSellExist==false && isExist==false && count > 0) {
                detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
            }
            return detectResult;
        }
        //行为有自启情况 隐私文本就要包含 “自启动”
        if(isSell==true && isrelation==false) {
            if(isSellExist==false && count > 0 ) {
                detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
            }
            return detectResult;
        }

        //行为有关联启动情况 隐私文本就要包含 “关联启动”
        if(isSell==false && isrelation==true) {
            if(isExist==false && count > 0) {
                detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
            }
            return detectResult;
        }

        // 扩展判断
//       detectResult.setComplianceStatus(count == 0 ? MiitDetectStatusEnum.NON_INVOLVED : MiitDetectStatusEnum.NON_COMPLIANCE);
//        detectResult.setComplianceStatus(count > 0 || actionCount>0 ?  MiitDetectStatusEnum.NON_COMPLIANCE: MiitDetectStatusEnum.NON_INVOLVED);
        return detectResult;
    }

}
