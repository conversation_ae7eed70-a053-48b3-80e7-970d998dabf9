package cn.ijiami.detection.miit.domain;

import cn.ijiami.detection.VO.ActionFrequencyVO;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 检测项分析结果数据
 *
 * <AUTHOR>
 * @date 2020-12-21 21:07
 */
@EqualsAndHashCode
public class ActionAnalyse implements Serializable {

    private static final long serialVersionUID = -364402826612404253L;

    /**
     * 行为id
     */
    private Long              actionId;
    /**
     * 行为名称
     */
    private String            actionName;
    /**
     * 行为触发时间
     */
    private Date              actionTime;
    /**
     * 时间戳：秒为单位
     */
    private Long              actionTimeMillis;
    /**
     * 主体类型 1.APP 2.SDK
     */
    private Integer           executorType;
    /**
     * 主体名称
     */
    private String            executor;
    /**
     * 包名
     */
    private String            packageName;
    /**
     * 行为名称
     */
    private String            actionPermission;
    /**
     * 权限简写
     */
    private String            actionPermissionAlias;
    /**
     * 是否敏感
     */
    private Boolean           sensitive;
    /**
     * 个人信息相关
     */
    private Boolean           personal;
    /**
     * 频次，每秒钟多少次
     */
    private Integer           frequency;
    /**
     * 行为阶段
     */
    private BehaviorStageEnum behaviorStage;
    /**
     * sdkIds
     */
    private String            sdkIds;

    /**
     * 某个行为ID固定频率
     */
    private ActionFrequencyVO actionFrequency;

    /**
     * 详细数据
     */
    private String detailsData;


    /**
     * 调用栈
     */
    private String stackInfo;

    public Long getDataId() {
        return dataId;
    }

    public void setDataId(Long dataId) {
        this.dataId = dataId;
    }

    private Long dataId;

    public String getDetailsData() {
        return detailsData;
    }

    public void setDetailsData(String detailsData) {
        this.detailsData = detailsData;
    }
    

    public ActionFrequencyVO getActionFrequency() {
		return actionFrequency;
	}

	public void setActionFrequency(ActionFrequencyVO actionFrequency) {
		this.actionFrequency = actionFrequency;
	}

	public Long getActionId() {
        return actionId;
    }

    public void setActionId(Long actionId) {
        this.actionId = actionId;
    }

    public String getActionName() {
        return actionName;
    }

    public void setActionName(String actionName) {
        this.actionName = actionName;
    }

    public Date getActionTime() {
        return actionTime;
    }

    public void setActionTime(Date actionTime) {
        this.actionTime = actionTime;
    }

    public Long getActionTimeMillis() {
        return actionTimeMillis;
    }

    public void setActionTimeMillis(Long actionTimeMillis) {
        this.actionTimeMillis = actionTimeMillis;
    }

    public Integer getExecutorType() {
        return executorType;
    }

    public void setExecutorType(Integer executorType) {
        this.executorType = executorType;
    }

    public String getExecutor() {
        return executor;
    }

    public void setExecutor(String executor) {
        this.executor = executor;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getActionPermission() {
        return actionPermission;
    }

    public void setActionPermission(String actionPermission) {
        this.actionPermission = actionPermission;
    }

    public String getActionPermissionAlias() {
        return actionPermissionAlias;
    }

    public void setActionPermissionAlias(String actionPermissionAlias) {
        this.actionPermissionAlias = actionPermissionAlias;
    }

    public Boolean getSensitive() {
        return sensitive;
    }

    public void setSensitive(Boolean sensitive) {
        this.sensitive = sensitive;
    }

    public Boolean getPersonal() {
        return personal;
    }

    public void setPersonal(Boolean personal) {
        this.personal = personal;
    }

    public Integer getFrequency() {
        return frequency;
    }

    public void setFrequency(Integer frequency) {
        this.frequency = frequency;
    }

    public BehaviorStageEnum getBehaviorStage() {
        return behaviorStage;
    }

    public void setBehaviorStage(BehaviorStageEnum behaviorStage) {
        this.behaviorStage = behaviorStage;
    }

    public String getSdkIds() {
        return sdkIds;
    }

    public void setSdkIds(String sdkIds) {
        this.sdkIds = sdkIds;
    }

    public void setStackInfo(String stackInfo) {
        this.stackInfo = stackInfo;
    }

    public String getStackInfo() {
        return this.stackInfo;
    }
}
