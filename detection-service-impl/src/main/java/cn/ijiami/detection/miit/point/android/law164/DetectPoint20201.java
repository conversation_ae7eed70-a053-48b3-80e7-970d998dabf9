package cn.ijiami.detection.miit.point.android.law164;

import java.util.ArrayList;
import java.util.List;

import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import org.springframework.util.CollectionUtils;

import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.helper.PermissionNameHelper;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.kit.MiitActionKit;
import cn.ijiami.detection.miit.kit.MiitWordKit;

/**
 * APP未向用户明示未经用户同意，且无合理的使用场景，存在频繁自启动或关联启动的行为。
 * <p>
 * <blockquote><pre>
 * 1、无隐私政策
 * 2、共XXX次（自启动或关联启动的行为）
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 「前台行为」
 *
 * 1、隐私政策截图
 * 2、共XXX次（自启动或关联启动的行为）
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 「授权前行为」
 *
 * 分两种情况：
 * 一、无隐私政策
 *    启动Activity、启动Service行为，根据行为数据的触发时间，按秒统计行为触发频次，超过1次则为不合规
 * 二、有隐私政策
 *    静默安装前，有启动Activity、启动Service行为，根据行为数据的触发时间，按秒统计触发频次，超过1次则为不合规
 *
 * 涉及行为：21004L,21005L
 *
 * 20210318补充逻辑：涉及点（20201、20202、20203）：
 *      退出阶段：关联启动、自启动都判断
 *      其他阶段：只关心关联启动
 *      
 *      这两个行为的条件下（21004、21005）应用关联启动：
 *      1、details为空 或者 details不包含包名
 *      2、details等于/
 * </pre></blockquote>
 *
 * <AUTHOR>
 * @date 2020/12/22 17:42
 **/
@EnableDetectPoint
public class DetectPoint20201 extends AbstractDetectPoint {
	
	private static int LEN = 1200;

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
    	
    	DetectResult detectResult = this.buildEmptyConclusionNonInvolved(commonDetectInfo, customDetectInfo);
    	// 不存在隐私政策,不涉及此项
        if (commonDetectInfo.nonHasPrivacyPolicy() || commonDetectInfo.getPrivacyPolicyContent().length()<LEN) {
            return detectResult;
        }
    	
        List<ActionAnalyse> nameFilterAfter = getNameFilterAfter(commonDetectInfo, customDetectInfo);
        detectResult = buildBaseResultByActionAnalyse(commonDetectInfo, customDetectInfo, nameFilterAfter);
        // 隐私政策截图截图地址
        detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
        detectResult.setPrivacyPolicyFragment(null);
        // 统计超过每秒1次的频率的数据
        long count = nameFilterAfter.stream().filter(a -> a.getFrequency() >= 1).count();
        // 扩展判断
        long actionCount = nameFilterAfter.stream().filter(this::isAutoBoot).count();
        
        
        boolean isSell = false;     //自启动
        boolean isrelation = false; //关联
        
        if(nameFilterAfter != null && nameFilterAfter.size()>0) {
        	for (ActionAnalyse actionAnalyse : nameFilterAfter) {
        		if (MiitActionKit.APP_OWN_START.equals(actionAnalyse.getActionName())) {
        			isSell = true;
                }
        		if ( MiitActionKit.APP_ASSOCIATE_START.equals(actionAnalyse.getActionName())) {
        			isrelation = true;
                }
			}
        }
        
        detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        if(nameFilterAfter==null || nameFilterAfter.size()==0) {
        	return detectResult;
        }
        
        String privacyPolicyText = commonDetectInfo.getPrivacyPolicyContent();
        //自启动
        boolean isSellExist = MiitWordKit.checkTextMeetTheRegex(privacyPolicyText, PermissionNameHelper.SELLFILTERSTARTUP);
        //关联启动
        boolean isExist = MiitWordKit.checkTextMeetTheRegex(privacyPolicyText, PermissionNameHelper.FILTERSTARTUP);
        //行为有自启动和关联启动情况 隐私文本就要包含 “自启动”、“关联启动”
        if(isSell ==true && isrelation==true) {
        	if(isSellExist==false && isExist==false && count > 0 && actionCount>0) {
        		 detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
        	}
        	return detectResult;
        }
       //行为有自启情况 隐私文本就要包含 “自启动”
        if(isSell==true && isrelation==false) {
        	if(isSellExist==false && count > 0 && actionCount>0) {
       		 	detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
        	}
        	return detectResult;
        }
        //行为有关联启动情况 隐私文本就要包含 “关联启动”
        if(isSell==false && isrelation==true) {
        	if(isExist==false && count > 0 && actionCount>0) {
          		 detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
           	}
        	return detectResult;
        }
        return detectResult;
    }

    @Override
    protected List<ActionAnalyse> getNameFilterAfter(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        // 其他阶段行为
//        BehaviorStageEnum behaviorStage = commonDetectInfo.isHasPrivacyPolicy() ? BehaviorStageEnum.BEHAVIOR_GRANT : BehaviorStageEnum.BEHAVIOR_FRONT;
        List<ActionAnalyse> otherActionAnalyses = this.filterAndCountActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GRANT);
        List<ActionAnalyse> otherNameFilterAfter = this.filterByAppAssociateStart(otherActionAnalyses, customDetectInfo);
        
        
        List<ActionAnalyse> nameFilterAfter = new ArrayList<>();
        
        //20240903 增加获取前台行为数据
    	List<ActionAnalyse> frontActionAnalyses = this.filterAndCountActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_FRONT);
        List<ActionAnalyse> frontNameFilterAfter = this.filterByAppAssociateStart(frontActionAnalyses, customDetectInfo);
        if (!CollectionUtils.isEmpty(frontNameFilterAfter)) {
            nameFilterAfter.addAll(frontNameFilterAfter);
        }
        
        // 应用退出阶段行为
        List<ActionAnalyse> exitActionAnalyses = this.filterAndCountActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_EXIT);
        List<ActionAnalyse> exitNameFilterAfter = this.filterByAppAssociateAndOwnStart(exitActionAnalyses, customDetectInfo);
        // 目标行为数据
        
        if (!CollectionUtils.isEmpty(otherNameFilterAfter)) {
            nameFilterAfter.addAll(otherNameFilterAfter);
        }
        if (!CollectionUtils.isEmpty(exitNameFilterAfter)) {
            nameFilterAfter.addAll(exitNameFilterAfter);
        }
        return nameFilterAfter;
    }

    protected boolean isAutoBoot(ActionAnalyse analyse) {
        return analyse.getActionId() == 32001;
    }
}
