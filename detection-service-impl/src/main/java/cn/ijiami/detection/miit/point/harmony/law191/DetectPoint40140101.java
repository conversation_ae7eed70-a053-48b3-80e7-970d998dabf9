package cn.ijiami.detection.miit.point.harmony.law191;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.point.android.law191.DetectPoint140101;

/**
 * 收集的个人信息类型或打开的可收集个人信息权限与现有业务功能无关；
 * 检测规则
 * 发现风险：
 * 1、启动应用之后，连续弹出权限授权窗口（除了存储、电话）
 *
 * 数据展现
 * 发现风险：
 * 【无关隐私政策】
 * 1、启动应用之后，连续弹出权限授权窗口（除了存储、电话）（遍历流程截图，仅权限图片）
 *
 * 检测结论
 * 发现风险：
 * 1、打开的可收集个人信息权限与现有业务功能无关
 * 未发现风险：
 * 1、该检测项未发现风险
 */
@EnableDetectPoint
public class DetectPoint40140101 extends DetectPoint140101 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }
}
