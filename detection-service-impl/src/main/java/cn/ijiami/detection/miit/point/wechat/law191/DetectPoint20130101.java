package cn.ijiami.detection.miit.point.wechat.law191;

import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.entity.TApplyPermission;
import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.enums.ResultDataLogBoTypeEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.*;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;
import cn.ijiami.detection.miit.point.android.law191.DetectPoint130101;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 征得用户同意前就开始收集个人信息或打开可收集个人信息的权限；
 *
 * 未发现风险：
 * 1、隐私政策授权页面提供明确同意、拒绝按钮
 * 2、隐私政策授权前无触发收集个人信息
 * 3、隐私政策授权前无申请打开个人信息权限
 * 4、隐私政策授权前无出现cookie传输个人信息
 * 发现风险：
 * 1、隐私政策授权未提供明确同意、拒绝按钮
 * 2、隐私政策授权前触发收集个人信息
 * 3、隐私政策授权前申请打开个人信息权限
 * 4、隐私政策授权前出现cookie传输个人信息
 * 5、无隐私政策
 */
@EnableDetectPoint
public class DetectPoint20130101 extends DetectPoint130101 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }

}
