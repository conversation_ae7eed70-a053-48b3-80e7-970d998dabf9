package cn.ijiami.detection.miit.point.android.law41391;

import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint10203;
import cn.ijiami.detection.miit.point.android.law191.DetectPoint140401;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350603.java
 * @Description
 * 三方SDK是否存在超范围申请使用个人信息权限
 * 3. App是否存在超频次获取个人信息行为
 * 判断规则：
 * e)应对SDK申请使用权限进行审核，确保其申请的权限具有明确、合理的使用目的，并监督SDK的个人信息收集行为是否超出约定或用户同意的范围。
 * 发现风险：
 * APP未见向用户明示SDK的收集使用规则，未经用户同意，SDK存在收集通【取表：行为-关键词中个人信息列的名称，多项时，用“、”隔开】等信息的行为，
 * 非服务所必需且无合理应用场景，超出与收集个人信息时所声称的目的具有直接或合理关联的范围。
 */
@EnableDetectPoint
public class DetectPoint350603 extends PrivacyUiDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        // JZ.END(未测试)
        DetectResult detectResult;
        //授权前行为
        if (commonDetectInfo.isHasPrivacyPolicy()) {
            List<ActionAnalyse> actionAnalyses = new ArrayList<>();
            //场景1 有隐私政策   隐私政策文本中没有行为关键词
            List<ActionAnalyse> frontActionAnalyses = this.filterAndCountActionByStageAndExe(commonDetectInfo, customDetectInfo,
                    BehaviorStageEnum.BEHAVIOR_FRONT, ExecutorTypeEnum.SDK);
            //筛选出不合规的行为
            List<ActionAnalyse> notComplianceFrontActionAnalyses = checkNotComplianceActionExistInFragment(customDetectInfo,
                    getBaseTargetPrivacyPolicyText(commonDetectInfo, customDetectInfo), frontActionAnalyses);
            // 存在隐私政策，判断增强
            boolean notExistWords = buildResultByActionExistWords(commonDetectInfo, customDetectInfo, notComplianceFrontActionAnalyses);
            if (!notExistWords) {
                actionAnalyses.addAll(notComplianceFrontActionAnalyses);
            }
            //场景2 授权前行为-隐私政策文本中行为有关键词
            List<ActionAnalyse> grantActionAnalyses = this.filterAndCountActionByStageAndExe(commonDetectInfo, customDetectInfo,
                    BehaviorStageEnum.BEHAVIOR_GRANT, ExecutorTypeEnum.SDK);
            if (!grantActionAnalyses.isEmpty()){
                actionAnalyses.addAll(grantActionAnalyses);
            }
            detectResult = buildBaseResultByActionAnalyse(commonDetectInfo, customDetectInfo, actionAnalyses);
            buildResultPrivacyPolicyFragmentText(commonDetectInfo, customDetectInfo, actionAnalyses, detectResult);

        } else {
            List<ActionAnalyse> actionAnalyses = this.filterAndCountActionByStageAndExe(commonDetectInfo,
                    customDetectInfo, BehaviorStageEnum.BEHAVIOR_FRONT, ExecutorTypeEnum.SDK);
            detectResult = buildBaseResultByActionAnalyse(commonDetectInfo, customDetectInfo, actionAnalyses);
        }
        if (detectResult.getComplianceStatus() == MiitDetectStatusEnum.NON_COMPLIANCE) {
            detectResult.setConclusion(buildSequenceTextFormat(
                    "%s未见向用户明示SDK的收集使用规则，未经用户同意，SDK存在收集通%s等信息的行为，非服务所必需且无合理应用场景，超出与收集个人信息时所声称的目的具有直接或合理关联的范围。",
                    executor(commonDetectInfo), getActionTypeNames(customDetectInfo, detectResult.getAnalysisResult())));
        } else {
            detectResult.setConclusion("该检测项未发现风险");
        }
        detectResult.setPrivacyPolicyFragment(null);
        return detectResult;
    }

}
