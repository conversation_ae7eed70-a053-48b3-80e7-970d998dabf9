package cn.ijiami.detection.miit.point.android.law164;

import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;

import java.util.List;

/**
 * APP未见向用户明示分享的第三方名称、目的及个人信息类型，用户同意隐私政策后，存在将IMEI/设备MAC地址/软件安装列表等个人信息发送给友盟/极光/个推等第三方SDK的行为。
 * <p>
 * <blockquote><pre>
 * 1、隐私政策截图
 * 2、隐私政策中检测不到「IMEI/设备MAC地址/软件安装列表」等关键词——无数据
 * 3、共XXX次（IMEI/设备MAC地址/软件安装列表行为）
 * 触发时间 （推送）SDK 主体名称 个人信息行为名称 频次；
 * 触发时间 （推送） 主体名称 个人信息行为名称 频次；
 *
 * 3中的行为，要在2中全部出现
 *
 * 「前台行为」
 * </pre></blockquote>
 *
 *
 *场景一：新增
 * 1、有隐私政策
 * 2、有关键词
 * 3、（推送）SDK 触发了行为
 * 【授权前行为】
 *
 * <AUTHOR>
 * @date 2020/12/22 17:42
 **/
@EnableDetectPoint
public class DetectPoint10302 extends AbstractDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        // JZ.END(未测试)
        // 不存在隐私政策,不涉及此项
        if (commonDetectInfo.nonHasPrivacyPolicy()) {
            return this.buildNonInvolved(commonDetectInfo, customDetectInfo);
        }
        List<ActionAnalyse> actionAnalyses =
                this.filterAndCountActionByStageAndExe(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GRANT, ExecutorTypeEnum.SDK);
        // 推送类sdk，个人信息相关的名称
        List<ActionAnalyse> pushSdkAction = this.filterPersonalAndPushFromResult(commonDetectInfo, actionAnalyses);
        boolean notExisWords = !buildResultByActionExistWords(commonDetectInfo, customDetectInfo, pushSdkAction);
        // 返回，推送类sdk在隐私政策中是否全部存在
        DetectResult detectResult = this.buildResultByActionAnalyseThenKeyWords(commonDetectInfo, customDetectInfo, pushSdkAction);
        detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        if(pushSdkAction != null && pushSdkAction.size()>0 && notExisWords) {
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
        }
        return detectResult;
    }

}
