package cn.ijiami.detection.miit.point.android.law164;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;

/**
 * APP以隐私政策弹窗的形式向用户明示收集使用规则，但未见清晰明示APP收集设备MAC地址、软件安装列表等的目的方式范围，用户同意隐私政策后，存在收集设备MAC地址、软件安装列表的行为。
 * <blockquote><pre>
 * 1、隐私政策截图
 * 2、隐私政策中检测不到「MAC地址、软件安装列表」等关键词——无数据
 * 3、共XXX次（MAC地址、软件安装列表）
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 *
 * 1、判断有隐私政策
 * 2、非静默安装状态下，判断是否有触发收集设备MAC地址、软件安装列表的行为，如果有以上任何一种行为的触发，再关键词匹配隐私政策文本中，是否有该行为的说明，如果没有，则违规，有则合规
 * 「前台行为」
 *
 * 涉及行为：24009L,28005L
 * </pre></blockquote>
 *
 * <AUTHOR>
 * @date 2020-12-18 15:31
 */
@EnableDetectPoint
public class DetectPoint10103 extends AbstractDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        // JZ.END(未测试)
        // 不存在隐私政策,不涉及此项
        if (commonDetectInfo.nonHasPrivacyPolicy()) {
            return this.buildNonInvolved(commonDetectInfo, customDetectInfo);
        }
        // 统计并提取行为数据
        List<ActionAnalyse> actionAnalyses = this.filterAndCountActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_FRONT);
        Set<String> decideRuleKeys = new HashSet<>();
        Map<Long, String> map = customDetectInfo.getActionWithKeyRegex();
        if(actionAnalyses != null) {
        	for (ActionAnalyse action : actionAnalyses) {
        		decideRuleKeys.add(map.get(action.getActionId()));
			}
        }
        String privacyPolicyFragmentText = MiitWordKit.defaultKeywordExtractionFromContent(commonDetectInfo.getPrivacyPolicyContent(), decideRuleKeys);
        DetectResult detectResult = this.buildResultByActionAnalyseThenKeyWords(commonDetectInfo, customDetectInfo, actionAnalyses);
        detectResult.setPrivacyPolicyFragment(privacyPolicyFragmentText);
        return detectResult;
    }

}
