package cn.ijiami.detection.miit.enums;

import com.fasterxml.jackson.annotation.JsonCreator;

import cn.ijiami.framework.common.enums.BaseValueEnum;

/**
 * 工业和信息化部 164号问状态枚举
 *
 * <AUTHOR>
 * @date 2020/12/21 17:05
 **/
public enum MiitDetectStatusEnum implements BaseValueEnum {
    /**
     * 合规
     */
    COMPLIANCE(0, "合规"),
    /**
     * 不合规
     */
    NON_COMPLIANCE(1, "不合规"),
    /**
     * 不涉及
     */
    NON_INVOLVED(2, "不涉及");

    private int value;

    private String name;

    MiitDetectStatusEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    @JsonCreator
    public static MiitDetectStatusEnum getItem(int value) {
        for (MiitDetectStatusEnum item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }

    @Override
    public int itemValue() {

        return value;
    }

    @Override
    public String itemName() {

        return name;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
