package cn.ijiami.detection.miit.interfaces;

import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;

/**
 * 检测具体实现
 *
 * <AUTHOR>
 * @date 2020/12/18 15:34
 **/
public interface IDetection {

    /**
     * 执行检测任务
     *
     * @param commonDetectInfo 公共参数
     * @param customDetectInfo 自定义参数
     * @return
     */
    DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo);
}
