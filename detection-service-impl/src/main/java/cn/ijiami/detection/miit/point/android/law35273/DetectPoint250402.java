package cn.ijiami.detection.miit.point.android.law35273;

import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint250402.java
 * @Description
 * 35273检测
 * 5.4.a
 * 收集个人敏感信息前，应征得个人信息主体的明示同意，并应确保个人信息主体的明示同意是其在完全知情的基础上自主给出的、具体的、清晰明确的意愿表示；
 *
 * 判断规则
 * 发现风险
 * 1 用户首次注册、登陆时，未提供勾选框或默认勾选。
 * 2 申请个人信息相关权限时未同步告知其使用目的。
 * 3 未提供隐私政策。
 */
@EnableDetectPoint
public class DetectPoint250402 extends Law35273DetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.buildNonInvolved(commonDetectInfo, customDetectInfo);
        Set<String> screenshots = new HashSet<>();
        detectResult.setScreenshots(screenshots);
        if (commonDetectInfo.nonHasPrivacyPolicy()) {
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
            detectResult.setConclusion(buildSequenceText(nonePrivacyDetailConclusion(commonDetectInfo)));
            detectResult.setSuggestion(buildSequenceText(nonePrivacyDetailSuggestion(commonDetectInfo)));
            return detectResult;
        }
        List<String> conclusionList = new ArrayList<>();
        List<String> suggestionList = new ArrayList<>();
        checkPrivacyPolicySelectedByDefault(commonDetectInfo, customDetectInfo, detectResult);
        if (detectResult.getComplianceStatus() == MiitDetectStatusEnum.NON_COMPLIANCE) {
            conclusionList.add(String.format("%s在征求用户同意隐私政策环节，设置为默认勾选。", executor(commonDetectInfo)));
            suggestionList.add(String.format("%s在征求用户同意隐私政策环节，提供勾选按钮，不能设置为默认勾选，必须有用户明确点击选择环节。", executor(commonDetectInfo)));
        }
        CheckPermissionPopupResult result = permissionPopupCheck(commonDetectInfo);
        result.getImageLogsList().forEach(resultDataLogBO -> addNoInvolvedImage(commonDetectInfo, detectResult, resultDataLogBO));
        if (!result.isNonInvolved()) {
            conclusionList.add(permissionNameConclusion(commonDetectInfo, result));
            suggestionList.add(String.format("%s在申请个人信息相关系统权限前，详细说明权限的使用目的。", executor(commonDetectInfo)));
        }
        if (!conclusionList.isEmpty()) {
            detectResult.setConclusion(buildSequenceText(conclusionList));
            detectResult.setSuggestion(buildSequenceText(suggestionList));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
        } else {
            detectResult.setConclusion(buildSequenceText("该检测项未发现风险"));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        }
        return detectResult;
    }

    protected String permissionNameConclusion(CommonDetectInfo commonDetectInfo, CheckPermissionPopupResult result) {
        return String.format("%s申请%s等个人信息相关权限时，未同步告知使用目的。",
                executor(commonDetectInfo), String.join("、", result.getPermissionNameList()));
    }

}
