package cn.ijiami.detection.miit.point.android.law41391;

import cn.ijiami.detection.VO.CheckList;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import cn.ijiami.detection.miit.point.android.LawJudgmentHelper;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;
import cn.ijiami.detection.miit.point.android.law191.DetectPoint120101;
import cn.ijiami.detection.miit.point.android.law191.DetectPoint140201;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static cn.ijiami.detection.miit.constants.DetectPointIdentifyItems.POINT_120101;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350203.java
 * @Description
 * App收集的个人信息是否满足告知同意要求
 * 3. 公开的收集使用规则是否完整
 * 判断规则：
 * f)不应通过捆绑不同类型服务、捆绑基本业务功能和扩展业务功能、批量申请授权等方式，诱导、强迫用户一次性同意个人信息收集请求；
 * 发现风险：
 * 【无隐私政策】
 * 1、未检测到隐私政策
 * 【有隐私政策】
 * 1、隐私中政策中未对【SDK名称列表】进行说明。
 */
@EnableDetectPoint
public class DetectPoint350203 extends PrivacyUiDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.buildNonInvolved(commonDetectInfo, customDetectInfo);
        List<String> conclusionList = new ArrayList<>();
        List<String> suggestionList = new ArrayList<>();
        if (commonDetectInfo.nonHasPrivacyPolicy()) {
            conclusionList.add("未检测到隐私政策");
        } else {
            // 隐私政策截图截图地址
            detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
            // 进行语义识别
            StringBuilder privacyPolicyFragment = new StringBuilder();
            if (commonDetectInfo.isNlpSuccess() && commonDetectInfo.getNlpResponse().isMatchIdentifyItem(POINT_120101.items)) {
                conclusionList.add("隐私政策中包含（等、例如）");
                suggestionList.add("建议在隐私政策中补充详细的描述");
                privacyPolicyFragment.append(commonDetectInfo.getNlpResponse().getIdentifyFragment(POINT_120101.items));
            }
            // 隐私政策截图截图地址
            detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
            // 找出sdk触发的个人行为
            PrivacyUiDetectPoint.CheckSdkNameResult checkSdkNameResult = checkAllBehaviorStageSdkName(commonDetectInfo, customDetectInfo);
            privacyPolicyFragment.append(checkSdkNameResult.getPrivacyPolicyFragment());
            if (!checkSdkNameResult.isNonInvolved()) {
                conclusionList.add(String.format("隐私中政策中未对%s进行说明",
                        String.join("、", checkSdkNameResult.getNoMatchSdkNameList())));
                suggestionList.add(String.format("%s公开的收集使用规则不完整，请在隐私政策中补充对%s的描述。",
                        executor(commonDetectInfo), String.join("、", checkSdkNameResult.getNoMatchSdkNameList())));
            }
            detectResult.setPrivacyPolicyFragment(privacyPolicyFragment.toString());
            detectResult.setAnalysisResult(checkSdkNameResult.getAnalysisResult());
        }
        // 没有触发个人信息行为的SDK，不涉及
        setDetectResultByConclusion(commonDetectInfo, detectResult, conclusionList, suggestionList);
        return detectResult;
    }

}
