package cn.ijiami.detection.miit.point.ios.law41391;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.law191.DetectPoint130801;
import cn.ijiami.detection.miit.point.android.law41391.DetectPoint350703;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350703.java
 * @Description
 * 其他要求
 * 3. App是否存在欺骗误导用户提供业务功能无关的个人信息或权限
 * 判断规则：
 * e)不应通过积分、奖励、优惠、红包等方式，欺骗误导用户提供与App业务功能无关的个人信息或权限；
 * 发现风险：
 * App是否存在欺骗误导用户提供业务功能无关的个人信息或权限
 */
@EnableDetectPoint
public class DetectPoint10350703 extends DetectPoint350703 {

}
