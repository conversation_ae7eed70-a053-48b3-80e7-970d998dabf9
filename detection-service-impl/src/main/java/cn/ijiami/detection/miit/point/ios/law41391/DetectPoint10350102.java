package cn.ijiami.detection.miit.point.ios.law41391;

import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.law35273.Law35273DetectPoint;
import cn.ijiami.detection.miit.point.android.law41391.DetectPoint350102;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350102.java
 * @Description
 * App收集的个人信息是否满足最小必要性
 * 2. 收集的个人信息是否为实现处理目的所必要的最小范围
 * 判断规则
 * b)收集的个人信息应限于实现处理目的所必要的最小范围；
 * 注1：范围通常涉及收集个人信息的类型、频率、数量、精度等。
 * 发现风险
 * 无隐私政策：
 * 1、在APP中通过弹窗、文本链接、附件、常见问题（FAQs）等界面或形式展示隐私政策。
 * 2、请在隐私政策中详细描述收集个人信息的目的、方式、范围，并且在同意隐私政策后再获取相关个人信息。
 * 有隐私政策：
 * 1、请不要收集非必要的个人信息，必要个人信息，调整到同意隐私政策后获取。
 * 2、APP在前台静默运行时，如无合理的业务场景，不能收集个人信息。
 * 3、APP在前台运行时，收集个人信息的频率不能超过1次/秒。
 */
@EnableDetectPoint
public class DetectPoint10350102 extends DetectPoint350102 {

}
