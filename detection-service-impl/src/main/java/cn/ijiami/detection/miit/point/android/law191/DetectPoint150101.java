package cn.ijiami.detection.miit.point.android.law191;

import cn.ijiami.detection.VO.CheckList;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import cn.ijiami.detection.utils.SdkUtils;
import cn.ijiami.detection.utils.StreamUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.ijiami.detection.utils.SdkUtils.actionKeyWordInSdk;

/**
 * 既未经用户同意，也未做匿名化处理，App客户端直接向第三方提供个人信息，包括通过客户端嵌入的第三方代码、插件等方式向第三方提供个人信息；
 *
 * 发现风险：
 * 1、隐私政策授权前，SDK触发个人信息行为
 * 2、SDK获取的个人信息行为未在隐私政策找到相应关键词
 * 3、触发个人信息SDK未在隐私政策说明
 */
@Slf4j
@EnableDetectPoint
public class DetectPoint150101 extends AbstractDetectPoint {
    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.getBaseDetectResult(commonDetectInfo, customDetectInfo);
        // 使用set，避免同一条数据被添加两次
        Set<ActionAnalyse> analysisResult = new HashSet<>();
        List<String> conclusionList = new ArrayList<>();
        List<String> suggestionList = new ArrayList<>();
        List<ActionAnalyse> allCollectList = new ArrayList<>();
        // 各个阶段的行为
        List<ActionAnalyse> allList = new ArrayList<>();
        // 隐私政策授权前收集检查
        List<ActionAnalyse> grantList = filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GRANT);
        // 隐私政策授权前收集检查不用判断隐私条款
        List<ActionAnalyse> grantCollectList = grantList.stream()
                .filter(ActionAnalyse::getPersonal)
                .filter(a -> ExecutorTypeEnum.SDK.getValue().equals(a.getExecutorType()))
                .collect(Collectors.toList());
        if (!grantCollectList.isEmpty()) {
            Set<String> sdkNames = grantCollectList
                    .stream()
                    .flatMap(a -> Arrays.stream(a.getExecutor().split(",")))
                    .collect(Collectors.toSet());
            analysisResult.addAll(grantCollectList);
            conclusionList.add(buildSequenceTextFormat("%s在隐私政策授权前存在收集个人信息行为", String.join("、", sdkNames)));
            suggestionList.add(buildSequenceTextFormat("建议调整隐私政策授权前，%s获取的个人信息行为", pluginName(commonDetectInfo)));
            // 提取片段
            detectResult.setPrivacyPolicyFragment(
                    MiitWordKit.defaultKeywordExtractionFromContent(
                            commonDetectInfo.getPrivacyPolicyContent(),
                            sdkNames.stream().map(SdkUtils::removeSdkSuffix).collect(Collectors.toSet())));
        }
        allList.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_FRONT));
        allList.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GROUND));
        allList.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_EXIT));
        // 是否有隐私政策
        if (commonDetectInfo.isHasPrivacyPolicy()) {
            // 隐私政策截图截图地址
            detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
            // 授权申请行为
            Map<Long, String> decideRules = customDetectInfo.getActionWithKeyRegex();
            Set<String> actionRules = new HashSet<>();
            // SDK获取的个人信息行为未在隐私政策找到相应关键词
            allCollectList.addAll(allList.stream()
                    .filter(ActionAnalyse::getPersonal)
                    .filter(a -> ExecutorTypeEnum.SDK.getValue().equals(a.getExecutorType()))
                    .filter(a -> {
                        String keyWord = decideRules.get(a.getActionId());
                        if (StringUtils.isBlank(keyWord)) {
                            return false;
                        }
                        actionRules.add(keyWord);
                        return notInSdkCheckList(keyWord, commonDetectInfo) && notInPrivacyPolicy(keyWord, commonDetectInfo);
                    })
                    .collect(Collectors.toList()));
            if (!allCollectList.isEmpty()) {
                String actionNames = allCollectList.stream()
                        .map(ActionAnalyse::getActionName)
                        .distinct()
                        .collect(Collectors.joining("、"));
                conclusionList.add(String.format("%s未在隐私中说明", actionNames));
                suggestionList.add(String.format("请在隐私政策中对%s进行详细描述", actionNames));
                analysisResult.addAll(allCollectList);
            }
            List<ActionAnalyse> sdkAnalyseList = allList.stream()
                    .filter(ActionAnalyse::getPersonal)
                    .filter(a -> ExecutorTypeEnum.SDK.getValue().equals(a.getExecutorType()))
                    .collect(Collectors.toList());
            checkSdkName(commonDetectInfo, customDetectInfo, detectResult, sdkAnalyseList, actionRules,
                    conclusionList, suggestionList, analysisResult);
        } else {
            allCollectList.addAll(allList.stream()
                    .filter(ActionAnalyse::getPersonal)
                    .filter(a -> ExecutorTypeEnum.SDK.getValue().equals(a.getExecutorType()))
                    .filter(StreamUtils.distinctByKey(ActionAnalyse::getActionId))
                    .collect(Collectors.toList()));
            String actionNames = allCollectList
                    .stream()
                    .map(ActionAnalyse::getActionName)
                    .distinct()
                    .collect(Collectors.joining("、"));
            if (!actionNames.isEmpty()) {
                conclusionList.add(String.format("%s未检测到隐私政策，存在%s收集", actionNames, pluginName(commonDetectInfo)));
                suggestionList.add(String.format("请在隐私政策中对%s进行详细描述", actionNames));
                analysisResult.addAll(allCollectList);
            }
        }
        if (!conclusionList.isEmpty()) {
            detectResult.setConclusion(buildSequenceText(conclusionList));
            detectResult.setSuggestion(buildSequenceText(suggestionList));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
            detectResult.setAnalysisResult(new ArrayList<>(analysisResult));
        } else {
            detectResult.setConclusion(buildSequenceText("该检测项未发现风险"));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        }
        return detectResult;
    }

    private boolean notInSdkCheckList(String keyWord, CommonDetectInfo commonDetectInfo) {
        if (Objects.isNull(commonDetectInfo.getThirdPartySharingChecklist())) {
            return false;
        }
        List<CheckList.Row> rowList = commonDetectInfo.getThirdPartySharingChecklist().getRowList();
        if (CollectionUtils.isNotEmpty(rowList)) {
            Optional<CheckList.Row> optional = rowList.stream().filter(row -> actionKeyWordInSdk(keyWord, row)).findFirst();
            if (optional.isPresent()) {
                return true;
            }
        }
        return !Pattern.compile(keyWord).matcher(commonDetectInfo.getThirdPartySharingChecklist().getFullText()).find();
    }

    private void checkSdkName(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo, DetectResult detectResult,
                              List<ActionAnalyse> allList, Set<String> privacyPolicyKeyword,
                              List<String> conclusionList, List<String> suggestionList, Set<ActionAnalyse> analysisResult) {
        List<ActionAnalyse> involvedAnalyseList = new ArrayList<>();
        Set<String> checkListTextKeyword = new HashSet<>();
        Set<String> fragmentList = new HashSet<>();
        Set<String> noMatchNameList = new HashSet<>();
        for (ActionAnalyse analyse : allList) {
            String[] sdkNames = analyse.getExecutor().split(",");
            Map<Long, String> decideRules = customDetectInfo.getActionWithKeyRegex();
            for (String sdkName : sdkNames) {
                // 优先匹配第三方sdk清单的数据
                Optional<CheckList.Row> sdkRow = findCheckListBySdkName(commonDetectInfo, sdkName);
                if (sdkRow.isPresent()) {
                    String keyWord = decideRules.get(analyse.getActionId());
                    // sdk行为在第三方sdk清单中声明
                    if (actionKeyWordInSdk(keyWord, sdkRow.get())) {
                        // 把第三方sdk转成文字加到隐私片段的展示数据中
                        fragmentList.add(sdkRow.get().privacyPolicyFragment());
                    } else {
                        involvedAnalyseList.add(analyse);
                        noMatchNameList.add(sdkName);
                    }
                } else if (isSdkNameInCheckListText(commonDetectInfo, sdkName)) {
                    String keyWord = decideRules.get(analyse.getActionId());
                    if (StringUtils.isNotBlank(keyWord)) {
                        // sdk行为在第三方sdk清单中声明
                        if (Pattern.compile(keyWord).matcher(commonDetectInfo.getThirdPartySharingChecklist().getFullText()).find()) {
                            checkListTextKeyword.add(keyWord);
                        } else {
                            involvedAnalyseList.add(analyse);
                            noMatchNameList.add(sdkName);
                        }
                    }
                } else if (isSdkNameInPolicyContent(commonDetectInfo, sdkName)) {
                    String keyWord = decideRules.get(analyse.getActionId());
                    if (StringUtils.isNotBlank(keyWord)) {
                        // sdk行为在隐私政策中
                        if (Pattern.compile(keyWord).matcher(commonDetectInfo.getPrivacyPolicyContent()).find()) {
                            privacyPolicyKeyword.add(keyWord);
                        } else {
                            involvedAnalyseList.add(analyse);
                            noMatchNameList.add(sdkName);
                        }
                    }
                } else {
                    // sdk没在隐私政策中
                    involvedAnalyseList.add(analyse);
                    noMatchNameList.add(sdkName);
                }
            }
        }
        detectResult.setPrivacyPolicyFragment(buildFragment(commonDetectInfo, fragmentList, checkListTextKeyword, privacyPolicyKeyword));
        if (!noMatchNameList.isEmpty()) {
            String sdkNames = String.join("、", noMatchNameList);
            conclusionList.add(String.format("%s存在收集个人信息行为，未在隐私中说明", sdkNames));
            suggestionList.add(String.format("请在隐私政策中对%s进行详细描述", sdkNames));
            analysisResult.addAll(involvedAnalyseList);
        }
    }

}
