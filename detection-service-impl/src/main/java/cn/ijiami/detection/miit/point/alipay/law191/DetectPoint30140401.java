package cn.ijiami.detection.miit.point.alipay.law191;

import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import cn.ijiami.detection.miit.point.wechat.base.AppletDetectPoint140401;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 收集个人信息的频度等超出业务功能实际需要；
 *
 * 发现风险：
 * 1、前台持续、固定频率，同一个主体，触发同一个人信息行为
 * 2、后台、App退出阶段触发个人信息行为（除了定位行为）
 */
@EnableDetectPoint
public class DetectPoint30140401 extends AppletDetectPoint140401 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }
}