package cn.ijiami.detection.miit.point.android.law164;

import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import org.tensorflow.op.linalg.Det;

import java.util.ArrayList;
import java.util.List;

/**
 * APP未见向用户告知且未经用户同意，在静默状态下或在后台运行时，存在收集通讯录、短信、通话记录、相机等信息的行为，非服务所必需且无合理应用场景，超出与收集个人信息时所声称的目的具有直接或合理关联的范围。
 * <p>
 * <blockquote><pre>
 * 1、无隐私政策
 * 2、共XXX次（通讯录、短信、通话记录、相机行为）
 * 触发时间 SDK 主体名称 个人信息行为名称 频次；
 * 触发时间 SDK 主体名称 个人信息行为名称 频次；
 * 「后台行为」
 *
 * 1、隐私政策截图
 * 2、共XXX次（通讯录、短信、通话记录、相机行为）
 * 触发时间 SDK 主体名称 个人信息行为名称 频次；
 * 触发时间 SDK 主体名称 个人信息行为名称 频次；
 * 「授权前行为」「后台行为」
 *
 * 涉及行为：
 * </pre></blockquote>
 * 
 * 
 * 场景一：修改
 * 1、没有有隐私政策
 * 2、SDK触发了行为
 * 【授权前行为】【后台行为】
 * 
 * 场景二：修改
 * 1、有隐私政策
 * 2、有关键词
 * 2、SDK触发了行为
 * 【授权前行为】
 * 
 * 场景三：修改
 * 1、有隐私政策
 * 2、没有关键词
 * 2、SDK触发了行为
 * 【授权前行为】【后台行为】
 *
 * <AUTHOR>
 * @date 2020/12/22 17:42
 **/
@EnableDetectPoint
public class DetectPoint10207 extends AbstractDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return buildNonInvolved(commonDetectInfo, customDetectInfo);
    }

    private DetectResult oldDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        List<ActionAnalyse> actionAnalyses = new ArrayList<>();

        DetectResult detectResult = null;
        if(!commonDetectInfo.isHasPrivacyPolicy()) {
            // 获取后台行为数据
            List<ActionAnalyse> groundAction =
                    this.filterAndCountActionByStageAndExe(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GROUND, ExecutorTypeEnum.SDK);
            actionAnalyses.addAll(groundAction);
            // 授权前行为数据
            List<ActionAnalyse> grantAction =
                    this.filterAndCountActionByStageAndExe(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GRANT, ExecutorTypeEnum.SDK);
            actionAnalyses.addAll(grantAction);

            detectResult = buildBaseResultByActionAnalyse(commonDetectInfo, customDetectInfo, actionAnalyses);
            buildResultPrivacyPolicyFragmentText(commonDetectInfo, customDetectInfo, actionAnalyses, detectResult);
        }else {

            boolean scene1 = true; //场景1
            boolean scene2 = true; //场景2

            // 场景2  授权前行为数据 有隐私政策 有关键词 有行为
            List<ActionAnalyse> grantAction = this.filterAndCountActionByStageAndExe(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GRANT, ExecutorTypeEnum.SDK);
            if(grantAction!= null && grantAction.size()>0) {
                actionAnalyses.addAll(grantAction);
                scene1 = false;
            }
            //场景3 有隐私政策  没有关键词
            List<ActionAnalyse> groundAction = this.filterAndCountActionByStageAndExe(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GROUND, ExecutorTypeEnum.SDK);
            //筛选出不合规的行为
            groundAction = checkNotComplianceActionExistInFragment(customDetectInfo, getBaseTargetPrivacyPolicyText(commonDetectInfo, customDetectInfo), groundAction);
            actionAnalyses.addAll(groundAction);
            boolean isExistWords = buildResultByActionExistWords(commonDetectInfo, customDetectInfo, groundAction);
            if(actionAnalyses!= null && actionAnalyses.size()>0 && isExistWords==false) {
                scene2 = false;
            }

            detectResult = buildBaseResultByActionAnalyse(commonDetectInfo, customDetectInfo, actionAnalyses);
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
            if(scene1 == false || scene2 == false) {
                detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
            }
            buildResultPrivacyPolicyFragmentText(commonDetectInfo, customDetectInfo, actionAnalyses, detectResult);
        }
        // 隐私政策截图截图地址
        detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
        detectResult.setPrivacyPolicyFragment(null);
        return detectResult;
    }

}
