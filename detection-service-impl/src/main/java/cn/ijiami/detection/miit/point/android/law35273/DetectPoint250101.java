package cn.ijiami.detection.miit.point.android.law35273;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;

import com.google.common.collect.Sets;

import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.kit.MiitWordKit;

/**
 * <AUTHOR>
 * @version 1.0.0
 * 24356789-@ClassName DetectPoint200501.java
 * @Description
 * 35273检测
 * 5.1.b
 * 个人信息控制者不应隐瞒产品或服务所具有的收集个人信息的功能
 *
 * 判断规则
 * 发现风险
 * 【有隐私政策】
 * 1 使用cookie及其同类技术收集个人信息，未在隐私政策中声明。
 * 2 第三方SDK收集的个人信息与隐私政策不一致
 * 3 APP收集的个人信息与隐私政策不一致
 * 【无隐私政策】
 * APP或者SDK有收集个人信息
 * @createTime 2022年03月24日 17:32:00
 */
@EnableDetectPoint
public class DetectPoint250101 extends Law35273DetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.buildNonInvolved(commonDetectInfo, customDetectInfo);
        List<String> conclusionList = new ArrayList<>();
        List<String> suggestionList = new ArrayList<>();
        // 隐私政策截图截图地址
        detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
        // 找出所有行为
        List<ActionAnalyse> actionAnalyseList = new ArrayList<>();
        actionAnalyseList.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GRANT));
        actionAnalyseList.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_FRONT));
        actionAnalyseList.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GROUND));
        actionAnalyseList.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_EXIT));
        // 未声明的sdk行为名
        List<ActionAnalyse> resultList = new ArrayList<>();
        CheckNonePrivacyActionResult checkSdkAction = checkNonePrivacySdkAction(commonDetectInfo, customDetectInfo, actionAnalyseList);
        if (!checkSdkAction.isNonInvolved()) {
            conclusionList.addAll(checkSdkAction.getConclusionList());
            if (commonDetectInfo.isHasPrivacyPolicy()) {
                suggestionList.addAll(checkSdkAction.getSuggestionList());
            }
            resultList.addAll(checkSdkAction.getAnalysisResult());
        }
        CheckNonePrivacyActionResult checkAppAction = checkNonePrivacyAppAction(commonDetectInfo, customDetectInfo, actionAnalyseList);
        if (!checkAppAction.isNonInvolved()) {
            resultList.addAll(checkAppAction.getAnalysisResult());
            conclusionList.addAll(checkAppAction.getConclusionList());
            if (commonDetectInfo.isHasPrivacyPolicy()) {
                suggestionList.addAll(checkAppAction.getSuggestionList());
            }
        }
        detectResult.setPrivacyPolicyFragment(joinFragmentText(commonDetectInfo, checkSdkAction, checkAppAction));
        List<TPrivacySensitiveWord> sensitiveWordList = new ArrayList<>();
        sensitiveWordList.addAll(getSensitiveWords(commonDetectInfo, BehaviorStageEnum.BEHAVIOR_GRANT));
        sensitiveWordList.addAll(getSensitiveWords(commonDetectInfo, BehaviorStageEnum.BEHAVIOR_FRONT));
        sensitiveWordList.addAll(getSensitiveWords(commonDetectInfo, BehaviorStageEnum.BEHAVIOR_GROUND));
        sensitiveWordList.addAll(getSensitiveWords(commonDetectInfo, BehaviorStageEnum.BEHAVIOR_EXIT));
        // 出现cookie传输个人信息
        List<TPrivacySensitiveWord> sensitiveCookieList = sensitiveWordList.stream()
                .filter(TPrivacySensitiveWord::isCookieWord)
                .collect(Collectors.toList());
        if (!sensitiveCookieList.isEmpty()) {
            if (commonDetectInfo.isHasPrivacyPolicy()) {
                suggestionList.add("请在隐私政策中详细说明使用cookie及其同类技术收集的个人信息。");
            } else {
                String privacyPolicyFragmentText = MiitWordKit.defaultKeywordExtractionFromContent(
                        commonDetectInfo.getPrivacyPolicyContent(),
                        Sets.newHashSet("(?i)COOKIE"));
                if (StringUtils.isNotBlank(privacyPolicyFragmentText)) {
                    detectResult.setPrivacyPolicyFragment(privacyPolicyFragmentText);
                    conclusionList.add("使用cookie及其同类技术收集个人信息，未在隐私政策中声明。");
                }
            }
        }
        // 没有隐私政策时的建议
        if (commonDetectInfo.nonHasPrivacyPolicy()) {
            suggestionList.add(nonePrivacyDetailSuggestion(commonDetectInfo));
            suggestionList.add("请在隐私政策中详细描述收集个人信息的目的、方式、范围，并且在同意隐私政策后再获取相关个人信息。");
        }
        // 没有触发个人信息行为的SDK，不涉及
        if (conclusionList.isEmpty()) {
            detectResult.setConclusion(buildSequenceText("该检测项未发现风险"));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        } else {
            detectResult.setConclusion(buildSequenceText(conclusionList));
            detectResult.setSensitiveWordResult(sensitiveCookieList.stream().map(this::buildActionNetwork).collect(Collectors.toList()));
            detectResult.setAnalysisResult(resultList);
            detectResult.setSuggestion(buildSequenceText(suggestionList));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
        }
        return detectResult;
    }

}
