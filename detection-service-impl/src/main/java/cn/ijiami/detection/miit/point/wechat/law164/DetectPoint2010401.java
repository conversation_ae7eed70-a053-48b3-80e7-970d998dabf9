package cn.ijiami.detection.miit.point.wechat.law164;

import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.*;
import cn.ijiami.detection.miit.enums.MiitDataTypeEnum;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.kit.MiitActionKit;
import cn.ijiami.detection.miit.kit.MiitLogKit;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint10401;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.util.*;

/**
 * APP的YYY页面或功能存在定向推送功能，但隐私政策未见向用户告知，将收集的用户个人信息用于定向推送、精准营销。
 * <p>
 * <blockquote><pre>
 * 1、隐私政策截图
 * 2、隐私政策中检测到「定向推送、个性化展示、精准营销」等关键词语句
 * 3、共XXX次（个人信息行为）
 * 触发时间 （推送）SDK 主体名称 个人信息行为名称 频次；
 * 触发时间 （推送） 主体名称 个人信息行为名称 频次；
 * 「前台行为」
 *
 * 1、判断第三方SDK检测结果中，是否有检测出推送类SDK，如果有，则进一步根据关键字，匹配隐私政策文本中，是否匹配出定向推送、精准营销等功能的描述，如果没有，则违规，有则合规
 * </pre></blockquote>
 * 2、界面上有关键词，但是隐私政策里没有，判断为违规
 *
 * <AUTHOR>
 * @date 2020/12/22 17:42
 **/
@EnableDetectPoint
public class DetectPoint2010401 extends DetectPoint10401 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }

}
