package cn.ijiami.detection.miit.point.wechat.law164;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint10101;

/**
 * APP未见向用户明示个人信息收集使用的目的、方式和范围，未经用户同意，存在收集IMEI、设备MAC地址和软件安装列表、通讯录和短信的行为。
 * <blockquote><pre>
 * 1、无隐私政策
 * 2、共XXX次（IMEI、设备MAC地址和软件安装列表、通讯录和短信）
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 「前台行为」
 *
 * 1、判断无隐私政策
 * 2、判断是否有触发收集IMEI、设备MAC地址和软件安装列表、通讯录和短信的行为，如果有以上任何一种行为的触发，则判断违规，否则合规
 *
 * 涉及行为:10002L, 24009L, 28005L, 14009L, 14010L, 14011L, 14012L, 13001L, 13002L, 13003L, 13004L, 13005L,14001L,14002L,14003L,14004L
 * </pre></blockquote>
 *
 * <AUTHOR>
 * @date 2020-12-18 15:31
 */
@EnableDetectPoint
public class DetectPoint2010101 extends DetectPoint10101 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }
}
