package cn.ijiami.detection.miit.point.android.law41391;

import cn.ijiami.detection.VO.CheckList;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import cn.ijiami.detection.miit.point.android.LawJudgmentHelper;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;
import cn.ijiami.detection.miit.point.android.law191.DetectPoint120101;
import cn.ijiami.detection.miit.point.android.law191.DetectPoint140401;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350601.java
 * @Description
 * App是否存在违规嵌入使用第三方SDK情况
 * 1. 是否明确同三方SDK的责任与义务
 * 判断规则：
 * c)应与第三方SDK明确双方的个人信息处理规则和保护责任，包括：
 * 1)SDK收集个人信息的目的、方式、范围；
 * 2)SDK申请的系统权限和申请目的；
 * 3)SDK收集个人信息的保存期限、停止嵌入后的个人信息处理方式；
 * 4)个人信息安全责任和保护措施；
 * 5)SDK是否存在热更新机制；
 * 6)SDK是否存在自启动、关联启动；
 * 7)SDK收集个人信息是否涉及向境外提供；
 * 8)SDK协助App响应用户个人信息权利请求的措施。
 * 发现风险：
 * 【无隐私政策】
 * 1、未检测到隐私政策
 * 【有隐私政策】
 * 1、未明确同三方SDK的责任与义务，隐私中政策中未对【SDK名称列表】进行说明。
 */
@EnableDetectPoint
public class DetectPoint350601 extends PrivacyUiDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.buildNonInvolved(commonDetectInfo, customDetectInfo);
        List<String> conclusionList = new ArrayList<>();
        List<String> suggestionList = new ArrayList<>();
        if (commonDetectInfo.nonHasPrivacyPolicy()) {
            conclusionList.add("未检测到隐私政策");
        } else {
            // 隐私政策截图截图地址
            detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
            // 找出sdk触发的个人行为
            CheckSdkNameResult result = checkAllBehaviorStageSdkName(commonDetectInfo, customDetectInfo);
            if (!result.isNonInvolved()) {
                conclusionList.add(String.format("未明确同三方SDK的责任与义务，隐私中政策中未对%s进行说明。", result.getNoMatchSdkNameList()));
                suggestionList.add(String.format("建议在隐私政策中明确同%s的责任与义务。", result.getNoMatchSdkNameList()));
            }
            detectResult.setAnalysisResult(result.getAnalysisResult());
            detectResult.setPrivacyPolicyFragment(result.getPrivacyPolicyFragment());
        }
        // 没有触发个人信息行为的SDK，不涉及
        setDetectResultByConclusion(commonDetectInfo, detectResult, conclusionList, suggestionList);
        return detectResult;
    }

}
