package cn.ijiami.detection.miit.point.android.law191;

import cn.ijiami.detection.VO.CheckList;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import cn.ijiami.detection.miit.point.android.LawJudgmentHelper;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static cn.ijiami.detection.miit.constants.DetectPointIdentifyItems.POINT_120101;

/**
 * 未逐一列出App(包括委托的第三方或嵌入的第三方代码、插件)收集使用个人信息的目的、方式、范围等；
 *
 * 未发现风险：
 * 1、隐私政策中不包含（等、例如、设备信息）
 * 2、触发个人信息行为的SDK全部在隐私政策中说明
 * 发现风险：
 * 1、隐私政策中包含（等、例如、设备信息）
 * 2、触发个人信息行为的SDK存在1个及以上未在隐私政策说明
 */
@EnableDetectPoint
public class DetectPoint120101 extends PrivacyUiDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.buildNonInvolved(commonDetectInfo, customDetectInfo);
        List<String> conclusionList = new ArrayList<>();
        List<String> suggestionList = new ArrayList<>();
        if (commonDetectInfo.nonHasPrivacyPolicy()) {
            conclusionList.add("未检测到隐私政策");
        }
        // 隐私政策截图截图地址
        detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
        // 进行语义识别
        StringBuilder privacyPolicyFragment = new StringBuilder();
        if (commonDetectInfo.isNlpSuccess() && commonDetectInfo.getNlpResponse().isMatchIdentifyItem(POINT_120101.items)) {
            conclusionList.add("隐私政策中包含（等、例如）");
            suggestionList.add("建议在隐私政策中补充详细的描述");
            privacyPolicyFragment.append(commonDetectInfo.getNlpResponse().getIdentifyFragment(POINT_120101.items));
        }
        // 找出所有行为
        // 找出sdk触发的个人行为
        CheckSdkNameResult checkSdkNameResult = checkAllBehaviorStageSdkName(commonDetectInfo, customDetectInfo);
        if (!checkSdkNameResult.isNonInvolved()) {
            conclusionList.add(String.format("隐私中政策中未对%s进行说明", String.join("、", checkSdkNameResult.getNoMatchSdkNameList())));
            suggestionList.add(String.format("请在隐私政策中补充对%s的描述", String.join("、", checkSdkNameResult.getNoMatchSdkNameList())));
        }
        privacyPolicyFragment.append(checkSdkNameResult.getPrivacyPolicyFragment());
        detectResult.setPrivacyPolicyFragment(privacyPolicyFragment.toString());
        // 没有触发个人信息行为的SDK，不涉及
        if (conclusionList.isEmpty()) {
            detectResult.setConclusion(buildSequenceText("该检测项未发现风险"));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        } else {
            detectResult.setConclusion(buildSequenceText(conclusionList));
            if (suggestionList.isEmpty()) {
                suggestionList.add(getDefaultSuggestion(commonDetectInfo));
            }
            detectResult.setAnalysisResult(checkSdkNameResult.getAnalysisResult());
            detectResult.setSuggestion(buildSequenceText(suggestionList));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
        }
        return detectResult;
    }

}
