package cn.ijiami.detection.miit.point.android.law164;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.similarity.EditDistance;
import org.apache.commons.text.similarity.JaccardDistance;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;

import cn.ijiami.detection.DTO.ocr.RecognizeData;
import cn.ijiami.detection.enums.ResultDataLogBoTypeEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.domain.ResultDataLogIosDetailsBO;
import cn.ijiami.detection.miit.enums.MiitDataTypeEnum;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import cn.ijiami.detection.utils.CommonUtil;
import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.text.similarity.EditDistance;
import org.apache.commons.text.similarity.JaccardDistance;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * APP 欺骗误导强迫用户点击跳转，具体场景包括但不限于以下方式：
 * <p>
 * <blockquote><pre>
 *
 * a） APP 以欺骗、误导或者强迫等方式向用户提供互联网信息服务或者产品；
 *
 * b） 未以显著方式明示或未经用户主动选择同意，APP 信息窗口页面，存在跳转、使用第三方的行为；
 *
 * c） APP 信息窗口页面，跳转、使用第三方时，存在欺骗误导强迫用户跳转的文字、图片或视频链接；
 *
 * d） APP 信息窗口通过用户“摇一摇”等交互动作触发页面或第三方应用跳转的，未清晰明示用户需要执行的触发动作及交互预期，或通过设置高灵敏度降低交互动作判定阈值，造成误导、强迫式跳转。
 *
 * 注：触发用户跳转的交互动作可参照如设备加速度不小于 15m/s2，转动角度不小于 35°，操作时间不少于 3s，或同时考虑加速度值与方向、转动角度的方式，或与前述单一触发条件等效的其他参数设置，确保用户在走路、乘车、拾起放下移动智能终端等日常生活中，非用户主动触发跳转的情况下，不会出现误导、强迫跳转。
 *
 * </pre></blockquote>
 *
 * <AUTHOR>
 * @date 2023/05/26 17:42
 **/
@Slf4j
@EnableDetectPoint
public class DetectPoint50101 extends AbstractDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        // 检查是否有摇一摇跳转
        List<ResultDataLogBO> shakeTypeList = commonDetectInfo.getResultDataLogs()
                .stream()
                .filter(resultDataLogBO -> resultDataLogBO.getType() == ResultDataLogBoTypeEnum.SHAKE.type)
                .collect(Collectors.toList());
        DetectResult detectResult = getBaseDetectResult(commonDetectInfo, customDetectInfo);
        List<ResultDataLogBO> jumpList = new ArrayList<>();
        List<String> paramsList = new ArrayList<>();
        isShakeJump(commonDetectInfo, shakeTypeList, jumpList, paramsList);
        jumpList.forEach(ui -> {
            addNoComplianceImage(commonDetectInfo, detectResult, ui);
        });

        detectResult.setComplianceStatus(jumpList.isEmpty() ? MiitDetectStatusEnum.NON_INVOLVED : MiitDetectStatusEnum.NON_COMPLIANCE);
        StringBuilder conclusionStr = new StringBuilder(getLawName(commonDetectInfo));
        if (!paramsList.isEmpty()) {
            conclusionStr.append("\n行为参数：\n").append(buildSequenceText(paramsList));
        }
        detectResult.setConclusion(conclusionStr.toString());
        return detectResult;
    }

    private void isShakeJump(CommonDetectInfo commonDetectInfo, List<ResultDataLogBO> uiList, List<ResultDataLogBO> shakeUiList, List<String> paramsList) {
        for (int i = 0; i < uiList.size(); i++) {
            ResultDataLogBO resultDataLogBO = uiList.get(i);
            if(resultDataLogBO == null) {
            	continue;
            }
            if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_BEFORE_SHAKE_THE_PHONE.getValue()) {
                for (int j = i + 1; j < uiList.size(); j++) {
                    // 判断摇一摇后是否跳转到新界面
                    try {
						isJump(commonDetectInfo, resultDataLogBO, uiList.get(j), shakeUiList, paramsList);
					} catch (Exception e) {
                        log.info("跳转出错 " + e.getMessage());
					}
                }
            }
        }
    }

    private String getUIText(CommonDetectInfo commonDetectInfo, ResultDataLogBO resultDataLogBO) {
        if (Objects.isNull(resultDataLogBO.getUiDumpResult())) {
            return "";
        }
        String uiText = resultDataLogBO.getUiDumpResult().getFullText();
        if (StringUtils.isEmpty(uiText)) {
            try {
				String absolutePath = commonDetectInfo.getFilePath() + File.separator + resultDataLogBO.getImgPath();
				if(!new File(absolutePath).exists()) {
					return "";
				}
				List<RecognizeData> ocrList = commonDetectInfo.getOcrService().extractText(absolutePath);
                return ocrList.stream()
				        .flatMap(data -> data.getData().stream().map(RecognizeData.DataDTO::getText))
				        .collect(Collectors.joining());
			} catch (Exception e) {
				log.info("ocr调用出错 " + e.getMessage());
				return "";
			}
        }
        return uiText;
    }

    private void isJump(CommonDetectInfo commonDetectInfo, ResultDataLogBO beforeUI, ResultDataLogBO afterUI,
                        List<ResultDataLogBO> shakeUiList, List<String> paramsList) {
        if (afterUI.getDataTag() == MiitDataTypeEnum.UI_AFTER_SHAKE_THE_PHONE.getValue()) {
            // Jaccard 计算文本相似度
            EditDistance<Double> distance = new JaccardDistance();
            String beforeUIText = getUIText(commonDetectInfo, beforeUI);
            String afterUIText = getUIText(commonDetectInfo, afterUI);
            if (StringUtils.isEmpty(beforeUIText) || StringUtils.isEmpty(afterUIText)) {
                return;
            }
            double similarity = distance.apply(beforeUIText, afterUIText);
            // 过滤xml是否存在包名，如果存在说明不是跳转的页面
            //20241020增加获取不到xml内容为空情况情况判断
            if(beforeUI.getUiDumpResult().getUiComponentList()!= null && 
            		((org.apache.commons.lang.StringUtils.isNotBlank(beforeUI.getXmlPath()) && beforeUI.getUiDumpResult().getUiComponentList().size()==0) || 
            				JSONObject.toJSONString(beforeUI.getUiDumpResult().getUiComponentList()).contains(commonDetectInfo.getApkPackageName())) &&
            		afterUI.getUiDumpResult().getUiComponentList()!= null && 
            		((org.apache.commons.lang.StringUtils.isNotBlank(afterUI.getXmlPath()) && afterUI.getUiDumpResult().getUiComponentList().size()==0) ||  
            				JSONObject.toJSONString(afterUI.getUiDumpResult().getUiComponentList()).contains(commonDetectInfo.getApkPackageName()))) {
            	return;
            }
            // 相似度小于一定值，认为摇一摇后页面跳转了
            if (similarity > 0.4) {
                shakeUiList.add(beforeUI);
                shakeUiList.add(afterUI);
            }
            ResultDataLogIosDetailsBO beforeDetailsBO = CommonUtil.jsonToBean(beforeUI.getDetails(), new TypeReference<ResultDataLogIosDetailsBO>() {
            });
            if (beforeDetailsBO != null && !StringUtils.isEmpty(beforeDetailsBO.getShakeArg()) && !paramsList.contains(beforeDetailsBO.getShakeArg())) {
                paramsList.add(beforeDetailsBO.getShakeArg());
            }
            ResultDataLogIosDetailsBO afterDetailsBO = CommonUtil.jsonToBean(afterUI.getDetails(), new TypeReference<ResultDataLogIosDetailsBO>() {
            });
            if (afterDetailsBO != null && !StringUtils.isEmpty(afterDetailsBO.getShakeArg()) && !paramsList.contains(afterDetailsBO.getShakeArg())) {
                paramsList.add(afterDetailsBO.getShakeArg());
            }
        }
    }

}
