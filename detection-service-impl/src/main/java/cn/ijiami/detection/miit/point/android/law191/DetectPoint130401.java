package cn.ijiami.detection.miit.point.android.law191;

import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.*;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;
import cn.ijiami.detection.utils.StreamUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 实际收集的个人信息或打开的可收集个人信息权限超出用户授权范围；
 *
 * 未发现风险：
 * 1、触发的个人信息行为，在隐私政策中获取到关键文本
 * 2、申请打开的个人信息权限，在隐私政策中获取到关键文本
 * 发现风险：
 * 【有隐私政策】
 * 1、触发的个人信息行为，在隐私政策中未获取到关键文本
 * 2、申请打开的个人信息权限，在隐私政策中未获取到关键文本
 * 【无隐私政策】
 * 存在个人信息行为即有风险
 */
@EnableDetectPoint
public class DetectPoint130401 extends PrivacyUiDetectPoint {

    private final static String SUGGESTION = "建议将收集的个人信息或收集个人信息的权限在你隐私政策中详细说明其使用目的";

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        // 找出所有行为
        List<ActionAnalyse> actionAnalyseList = new ArrayList<>();
        actionAnalyseList.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_FRONT));
        actionAnalyseList.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GROUND));
        actionAnalyseList.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_EXIT));
        // 不存在隐私政策, 不合规
        if (commonDetectInfo.nonHasPrivacyPolicy()) {
            DetectResult detectResult = this.buildNonInvolved(commonDetectInfo, customDetectInfo);
            List<String> conclusionList = new ArrayList<>();
            List<ActionAnalyse> collectActionList = actionAnalyseList.stream()
                    .filter(ActionAnalyse::getPersonal)
                    .filter(StreamUtils.distinctByKey(ActionAnalyse::getActionId))
                    .collect(Collectors.toList());
            if (!collectActionList.isEmpty()) {
                conclusionList.add("未检测到隐私政策，存在收集" + collectActionList.stream()
                        .map(ActionAnalyse::getActionName).distinct().collect(Collectors.joining("、")));
                detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
                detectResult.setConclusion(buildSequenceText(conclusionList));
                detectResult.setAnalysisResult(collectActionList);
                detectResult.setSuggestion(buildSequenceText(SUGGESTION));
            }
            return detectResult;
        } else {
            return hasPrivacyPolicyCheck(commonDetectInfo, customDetectInfo, actionAnalyseList);
        }
    }

    private DetectResult hasPrivacyPolicyCheck(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo, List<ActionAnalyse> actionAnalyseList) {
        DetectResult detectResult = this.buildNonInvolved(commonDetectInfo, customDetectInfo);
        // 隐私政策截图截图地址
        detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());

        List<String> conclusionList = new ArrayList<>();
        List<String> collectActionList = notInPrivacyPolicyAction(commonDetectInfo, customDetectInfo, detectResult, actionAnalyseList);
        if (!collectActionList.isEmpty()) {
            conclusionList.add(String.join("、", collectActionList) + "未在隐私中说明");
        }

        List<String> applyActionNameList = notInPrivacyPolicyPermission(commonDetectInfo, detectResult);
        if (!applyActionNameList.isEmpty()) {
            conclusionList.add(String.join("、", applyActionNameList) + "未在隐私中说明");
        }
        if (!conclusionList.isEmpty()) {
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
            detectResult.setConclusion(buildSequenceText(conclusionList));
            detectResult.setSuggestion(buildSequenceText(SUGGESTION));
        }
        return detectResult;
    }

}
