package cn.ijiami.detection.miit.point.android.law35273;

import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import cn.ijiami.detection.entity.TPrivacySharedPrefs;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint200501.java
 * @Description
 * 35273检测
 * 6.3.a
 * a)传输和存储个人敏感信息时，应采用加密等安全措施；
 * 注：采用密码技术时宜遵循密码管理相关国家标准
 *
 * 判断规则
 * 1 明文传输个人信息
 * 2 明文存储个人信息
 */
@EnableDetectPoint
public class DetectPoint260301 extends Law35273DetectPoint {
    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.buildNonInvolved(commonDetectInfo, customDetectInfo);
        List<String> conclusionList = new ArrayList<>();
        List<String> suggestionList = new ArrayList<>();

        List<TPrivacySensitiveWord> sensitiveWordList = new ArrayList<>();
        sensitiveWordList.addAll(getSensitiveWords(commonDetectInfo, BehaviorStageEnum.BEHAVIOR_GRANT));
        sensitiveWordList.addAll(getSensitiveWords(commonDetectInfo, BehaviorStageEnum.BEHAVIOR_FRONT));
        sensitiveWordList.addAll(getSensitiveWords(commonDetectInfo, BehaviorStageEnum.BEHAVIOR_GROUND));
        sensitiveWordList.addAll(getSensitiveWords(commonDetectInfo, BehaviorStageEnum.BEHAVIOR_EXIT));
        if (!sensitiveWordList.isEmpty()) {
            conclusionList.add(String.format("%s或者%s存在明文传输%s等个人信息行为。",
                    executor(commonDetectInfo),
                    pluginName(commonDetectInfo),
                    sensitiveWordList.stream().map(TPrivacySensitiveWord::getName).distinct().collect(Collectors.joining("、"))));
            suggestionList.add("请对个人信息进行加密传输。");
        }

        List<TPrivacySharedPrefs> sensitivePrefsList = new ArrayList<>();
        sensitivePrefsList.addAll(getSensitivePrefs(commonDetectInfo, BehaviorStageEnum.BEHAVIOR_GRANT));
        sensitivePrefsList.addAll(getSensitivePrefs(commonDetectInfo, BehaviorStageEnum.BEHAVIOR_FRONT));
        sensitivePrefsList.addAll(getSensitivePrefs(commonDetectInfo, BehaviorStageEnum.BEHAVIOR_GROUND));
        sensitivePrefsList.addAll(getSensitivePrefs(commonDetectInfo, BehaviorStageEnum.BEHAVIOR_EXIT));
        if (!sensitivePrefsList.isEmpty()) {
            conclusionList.add(String.format("%s或者%s存在明文存储%s等个人信息行为。",
                    executor(commonDetectInfo),
                    pluginName(commonDetectInfo),
                    sensitivePrefsList.stream().map(TPrivacySharedPrefs::getName).distinct().collect(Collectors.joining("、"))));
            suggestionList.add("请对个人信息进行加密存储。");
        }

        if (conclusionList.isEmpty()) {
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
            detectResult.setConclusion(buildSequenceText("该检测项未发现风险"));
        } else {
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
            detectResult.setSensitiveWordResult(getActionNetworkList(sensitiveWordList));
            detectResult.setSensitivePrefsResult(getActionPrefsList(sensitivePrefsList));
            detectResult.setConclusion(buildSequenceText(conclusionList));
            detectResult.setSuggestion(buildSequenceText(suggestionList));
        }
        return detectResult;
    }
}
