package cn.ijiami.detection.miit.point.ios.law35273;

import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import cn.ijiami.detection.entity.TPrivacySharedPrefs;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.law35273.DetectPoint260301;
import cn.ijiami.detection.miit.point.android.law35273.Law35273DetectPoint;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint200501.java
 * @Description
 * 35273检测
 * 6.3.a
 * a)传输和存储个人敏感信息时，应采用加密等安全措施；
 * 注：采用密码技术时宜遵循密码管理相关国家标准
 *
 * 判断规则
 * 1 明文传输个人信息
 * 2 明文存储个人信息
 */
@EnableDetectPoint
public class DetectPoint10260301 extends DetectPoint260301 {

}
