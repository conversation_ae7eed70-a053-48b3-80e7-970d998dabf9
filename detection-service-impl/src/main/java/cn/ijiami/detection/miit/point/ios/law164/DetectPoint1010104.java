package cn.ijiami.detection.miit.point.ios.law164;

import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint10104;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * APP未见向用户明示SDK收集使用个人信息的目的、方式和范围，未经用户同意，SDK存在收集IMEI、设备MAC地址和软件安装列表、通讯录和短信的行为。
 * <blockquote><pre>
 * 1、无隐私政策
 * 2、共XXX次（IMEI、设备MAC地址和软件安装列表、通讯录和短信）
 * 触发时间 SDK 主体名称 个人信息行为名称 频次；
 *
 * 「前台行为」
 * 1、判断无隐私政策
 * 2、判断是否有触收集IMEI、设备MAC地址和软件安装列表、通讯录和短信的行为且触发主体是sdk，如果有任何一种行为触发，则判定违规，否在合规
 *
 * 涉及行为: 110025, 110024, 110027, 110026, 110035, 110036, 110047, 100001, 110051, 100003, 100002, 100005, 100004, 100007,
 * 100006, 100010, 100013, 100014, 100017, 100016, 110005, 100021, 110004, 110007, 110006, 110008
 * </pre></blockquote>
 */
@EnableDetectPoint
public class DetectPoint1010104 extends DetectPoint10104 {
    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        // JZ.END(基本完成)
        DetectResult detectResult = this.buildNonInvolved(commonDetectInfo, customDetectInfo);
        // 存在隐私政策,不涉及此项
        if (commonDetectInfo.isHasPrivacyPolicy()) {
            return detectResult;
        }

        // 统计并提取行为数据
        List<ActionAnalyse> actionAnalyses =
                this.filterAndCountActionByStageAndExe(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_FRONT, ExecutorTypeEnum.SDK);
        detectResult = buildBaseResultByActionAnalyse(commonDetectInfo, customDetectInfo, actionAnalyses);
        detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
        String privacyPolicyFragmentText = MiitWordKit.defaultKeywordExtractionFromContent(
                commonDetectInfo.getPrivacyPolicyContent(),
                customDetectInfo.getDecideRuleKeys());
        detectResult.setPrivacyPolicyFragment(privacyPolicyFragmentText);
        return detectResult;
    }
}
