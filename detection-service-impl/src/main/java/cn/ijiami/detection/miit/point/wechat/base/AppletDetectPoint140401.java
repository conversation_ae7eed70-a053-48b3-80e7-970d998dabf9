package cn.ijiami.detection.miit.point.wechat.base;

import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import cn.ijiami.detection.miit.point.android.LawJudgmentHelper;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AppletDetectPoint140401.java
 * @Description
 * 收集个人信息的频度等超出业务功能实际需要；
 *  *
 *  * 发现风险：
 *  * 1、前台持续、固定频率，同一个主体，触发同一个人信息行为
 *  * 2、后台、App退出阶段触发个人信息行为（除了定位行为）
 * @createTime 2024年06月20日 17:53:00
 */
public class AppletDetectPoint140401 extends AbstractDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = getBaseDetectResult(commonDetectInfo, customDetectInfo);
        // 设置要检查的个人信息id
        LawJudgmentHelper.setAllPersonalDecideRuleActionIds(commonDetectInfo, customDetectInfo);
        // 前台行为, 是否有固定触发
        List<ActionAnalyse> cycleList = filterCycleTriggerAction(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_FRONT);

        // 是否有个人隐私触发
        List<ActionAnalyse> groundAndExitActionAnalyseList = new ArrayList<>();
        groundAndExitActionAnalyseList.addAll(filterCycleTriggerAction(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GROUND));
        groundAndExitActionAnalyseList.addAll(filterCycleTriggerAction(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_EXIT));
        // 定位权限不在判断范围内
        List<Long> locationActionIdList = commonDetectInfo.getActionNougatMap()
                .values()
                .stream()
                .filter(n -> StringUtils.containsAny(n.getActionName(), "定位", "位置", "纬度", "经度"))
                .map(TActionNougat::getActionId)
                .collect(Collectors.toList());
        List<ActionAnalyse> personalList = groundAndExitActionAnalyseList
                .stream()
                .filter(ActionAnalyse::getPersonal)
                .filter(a -> !locationActionIdList.contains(a.getActionId()))
                .collect(Collectors.toList());
        List<String> conclusionList = new ArrayList<>();
        Set<ActionAnalyse> actionAnalyseList = new HashSet<>();
        actionAnalyseList.addAll(personalList);
        actionAnalyseList.addAll(cycleList);
        if (!actionAnalyseList.isEmpty()) {
            String actionNames = actionAnalyseList
                    .stream()
                    .map(ActionAnalyse::getActionName)
                    .distinct()
                    .collect(Collectors.joining("、"));
            conclusionList.add(String.format("小程序存在以固定频次收集%s", actionNames));
        }
        if (!conclusionList.isEmpty()) {
            detectResult.setAnalysisResult(new ArrayList<>(actionAnalyseList));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
            detectResult.setConclusion(buildSequenceText(conclusionList));
            detectResult.setSuggestion(buildSequenceText("建议在调整非必要收集个人信息场景的收集规则"));
        } else {
            detectResult.setConclusion(buildSequenceText("该检测项未发现风险"));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        }
        return detectResult;
    }
}
