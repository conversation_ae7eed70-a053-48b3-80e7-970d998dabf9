package cn.ijiami.detection.miit.point.ios.law164;

import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint10201;

import java.util.ArrayList;
import java.util.List;

/**
 * APP未见向用户告知且未经用户同意，在YYY功能中，存在收集通讯录、短信、通话记录、相机等信息的行为，非服务所必需且无合理应用场景，超出与收集个人信息时所声称的目的具有直接或合理关联的范围。
 * <blockquote><pre>
 * 1、无隐私政策
 * 2、共XXX次（通讯录、短信、通话记录、相机）
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 「前台行为」
 *
 * 1、隐私政策截图
 * 2、共XXX次（通讯录、短信、通话记录、相机）
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 「授权前行为」
 *
 * 分两种情况：
 * 一、无隐私政策
 *      1、判断无隐私政策
 *      2、判断自动化遍历状态下，是否有触发通讯录、短信、通话记录、相机等信息的行为，如果有，则违规，否则不涉及
 * 二、有隐私政策
 *      1、判断有隐私政策
 *      2、判断静默安装状态下，是否有触发通讯录、短信、通话记录、相机等信息的行为，如果有，则违规，否则不涉及"
 *
 * 涉及行为（旧）删除:10002L, 24009L, 28005L, 14009L, 14010L, 14011L, 14012L, 13001L, 13002L, 13003L, 13004L, 13005L,14001L,14002L,14003L,14004L
 * 
 *  新行为关联：14009L、14010L、14011L、14012L、13001L、13002L、13003L、13004L、13005L、14001L、14002L、14003L、14004L、14005L、14006L、14007L、14008L、21002L、15001L
 *       
 *       
 *       
 * </pre></blockquote>
 * 
 * 三、有隐私政策  20210408
 *  场景一：
 *   1、判断有隐私政策
 *   2、触发了该项判断依据中罗列的行为【前台行为】
 *   3、隐私政策文本中没有匹配到行为关键词，属于违规
 *  场景二：
 *   1、判断有隐私政策
 *   2、触发了该项判断依据中罗列的行为【授权前行为】
 *   3、隐私政策文本中即使能匹配出行为关键词，一样违规
 **/
@EnableDetectPoint
public class DetectPoint1010201 extends DetectPoint10201 {

	@Override
	public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
		return super.doDetect(commonDetectInfo, customDetectInfo);
	}
}
