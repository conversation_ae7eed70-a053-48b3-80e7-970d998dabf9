package cn.ijiami.detection.miit.point.ios.law164;

import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * APP在运行时，未见向用户告知且未经用户同意，存在每30s读取一次位置信息，非服务所必需且无合理应用场景，超出实现产品或服务的业务功能所必需的最低频率。
 * <blockquote><pre>
 * 1、无隐私政策
 * 2、共XXX次（地理位置行为）
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 「前台行为」
 * 1、隐私政策截图
 * 2、共XXX次（地理位置行为）
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 「授权前行为」
 *
 * 分两种情况：
 * 一、无隐私政策
 * 1、判断无隐私政策
 * 2、判断自动化遍历状态下，是否有触发获取位置信息的行为，如果无，则合规，如果有，则再根据触发时间戳，计算触发的时间间隔，如果时间间隔等于小于30s，则违规，否则不涉及
 * 二、有隐私政策
 * 1、判断有隐私政策
 * 2、判断静默安装状态下，是否有触发获取位置信息的行为，如果无，则合规，如果有，则再根据触发时间戳，计算触发的时间间隔，如果时间间隔等于小于30s，则违规，否则不涉及
 *
 *
 *20210408
 *分两种情况：
 *一、无隐私政策(删除)
 *     1、判断无隐私政策
 *     2、判断自动化遍历状态下，是否有触发获取位置信息的行为，如果无，则合规，如果有，则再根据触发时间戳，计算触发的时间间隔，如果时间间隔等于小于30s，则违规，否则合规
 *二、有隐私政策(删除)
 *     1、判断有隐私政策
 *     2、判断静默安装状态下，是否有触发获取位置信息的行为，如果无，则合规，如果有，则再根据触发时间戳，计算触发的时间间隔，如果时间间隔等于小于30s，则违规，否则合规
 *
 *新增判断：
 *一、无隐私政策
 *     1、判断无隐私政策
 *     2、判断自动化遍历状态下，是否有触发获取位置信息的行为，如果无，则合规，如果有，则再根据触发时间戳，计算触发的时间间隔 某一类行为，在固定频率（1s 、30s、60s）
 *       触发超过3次以上【授权前行为】、【前台行为】、【后台行为】
 *
 *二、有隐私政策
 *    场景一：
 *     1、判断有隐私政策
 *     2、隐私政策中有行为关键词
 *     3、判断静默安装状态下，是否有触发获取位置信息的行为，如果无，则合规，如果有，则再根据触发时间戳，计算触发的时间间隔 某一类行为，在固定频率（1s 、30s、60s）
 *       触发超过3次以上【授权前行为】、【后台行为】
 *    场景二：
 *     1、判断有隐私政策
 *     2、隐私政策中没有行为关键词
 *    3、判断静默安装状态下，是否有触发获取位置信息的行为，如果无，则合规，如果有，则再根据触发时间戳，计算触发的时间间隔 某一类行为，在固定频率（1s 、30s、60s）
 *      触发超过3次以上【授权前行为】【前台行为】【后台行为】
 *
 * 涉及行为：100004L, 100005L, 100006L, 100007L
 * </pre></blockquote>
 **/
@EnableDetectPoint
public class DetectPoint1010202 extends AbstractDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        // JZ.END(基本完成)
        boolean hasPrivacyPolicy = commonDetectInfo.isHasPrivacyPolicy();
        DetectResult detectResult = getBaseDetectResult(commonDetectInfo, customDetectInfo);
        //无隐私政策情况
        if(!hasPrivacyPolicy) {
            List<ActionAnalyse> analyesList = new ArrayList<>();
            // 授权前行为, 是否有固定触发
            List<ActionAnalyse> grantAnalyses = filterCycleTriggerAction(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GRANT);
            // 前台行为, 是否有固定触发
            List<ActionAnalyse> frontAnalyses = filterCycleTriggerAction(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_FRONT);
            //后台行为
            List<ActionAnalyse> groundAnalyses = filterCycleTriggerAction(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GROUND);

            analyesList.addAll(grantAnalyses);
            analyesList.addAll(frontAnalyses);
            analyesList.addAll(groundAnalyses);
            detectResult.setAnalysisResult(analyesList);
            detectResult.setComplianceStatus(analyesList.isEmpty() ? MiitDetectStatusEnum.NON_INVOLVED : MiitDetectStatusEnum.NON_COMPLIANCE);
        }else {
            List<ActionAnalyse> analyesList = new ArrayList<>();

            boolean scene1 = true; //场景1
            boolean scene2 = true; //场景2

            //场景1  判断有隐私政策 有行为  有关键词  有频率
            //授权前行为
            List<ActionAnalyse> grantAnalyses = filterCycleTriggerAction(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GRANT);
            if (CollectionUtils.isNotEmpty(grantAnalyses)) {
                scene1 = false;
                analyesList.addAll(grantAnalyses);
            }
            //后台行为
            List<ActionAnalyse> groundAnalyses = filterCycleTriggerAction(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GROUND);
            //筛选出不合规的行为
            groundAnalyses = checkNotComplianceActionExistInFragment(customDetectInfo, getBaseTargetPrivacyPolicyText(commonDetectInfo, customDetectInfo), groundAnalyses);
            analyesList.addAll(groundAnalyses);

            detectResult = buildBaseResultByActionAnalyse(commonDetectInfo, customDetectInfo, analyesList);
            //有固定频率情况后，增强判断关键词
            boolean notExisWords1 = buildResultByActionExistWords(commonDetectInfo, customDetectInfo, groundAnalyses);
            if (CollectionUtils.isNotEmpty(groundAnalyses) && notExisWords1) {
                scene1 = false;
            }
            //场景2  判断有隐私政策 有行为 无关键词  有频率
            // 前台行为
            List<ActionAnalyse> frontAnalyses = filterCycleTriggerAction(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_FRONT);
            //筛选出不合规的行为
            frontAnalyses = checkNotComplianceActionExistInFragment(customDetectInfo, getBaseTargetPrivacyPolicyText(commonDetectInfo, customDetectInfo), frontAnalyses);
            analyesList.addAll(frontAnalyses);
            boolean notExisWords2 = buildResultByActionExistWords(commonDetectInfo, customDetectInfo, frontAnalyses);
            //存在固定频率，增强判断
            if (CollectionUtils.isNotEmpty(frontAnalyses) && !notExisWords2) {
                scene2 = false;
            }
            detectResult.setAnalysisResult(analyesList);
            detectResult.setComplianceStatus(!scene1 || !scene2 ? MiitDetectStatusEnum.NON_COMPLIANCE : MiitDetectStatusEnum.NON_INVOLVED);
        }
        detectResult.setPrivacyPolicyFragment(null);
        // 隐私政策截图截图地址
        detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
        return detectResult;
    }
}
