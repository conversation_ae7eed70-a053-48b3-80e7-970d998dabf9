package cn.ijiami.detection.miit.point.android.law35273;

import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.TApplyPermission;
import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.ResultDataLogBoTypeEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.*;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint200501.java
 * @Description
 * 35273检测
 * 5.4.a
 * 收集个人信息，应向个人信息主体告知收集、使用个人信息的目的、方式和范围，并获得个人信息主体的授权同意；
 * 注1：如产品或服务仅提供一项收集、使用个人信息的业务功能时，个人信息控制者可通过隐私政策的形式，实现向个人信息主体的告知；
 * 产品或服务提供多项收集、使用个人信息的业务功能的，除隐私政策外，个人信息控制者宜在实际开始收集特定个人信息时，向个人信息主体提供收集、
 * 使用该个人信息的目的、方式和范围，以便个人信息主体在作出具体的授权同意前，能充分考虑对其的具体影响。
 * 注2：符合本标准5.3和a）要求的实现方法，可参考附录C
 *
 * 判断规则
 * 发现风险
 * 【有隐私政策】
 * 1 APP未提供明确同意或者拒绝按钮
 * 2 APP在隐私政策授权前开始收集个人信息。
 * 3 APP在隐私政策授权前使用cookie传输个人信息。
 * 4 APP在隐私政策授权前申请打开个人信息权限。
 * 5 APP或者SDK收集的个人信息与隐私政策不一致。
 * 【没有隐私政策】
 * 存在个人信息行为就有风险
 */
@EnableDetectPoint
public class DetectPoint250401 extends Law35273DetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.getBaseDetectResult(commonDetectInfo, customDetectInfo);
        List<String> conclusionList = new ArrayList<>();
        List<String> suggestionList = new ArrayList<>();
        // 隐私政策授权前收集检查
        List<ActionAnalyse> grantCollectActionList = filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GRANT)
                .stream()
                .filter(ActionAnalyse::getPersonal)
                .collect(Collectors.toList());
        List<ActionAnalyse> afterPrivacyActionList = new ArrayList<>();
        afterPrivacyActionList.addAll(grantCollectActionList);
        afterPrivacyActionList.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_FRONT));
        afterPrivacyActionList.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GROUND));
        afterPrivacyActionList.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_EXIT));
        if (commonDetectInfo.isHasPrivacyPolicy()) {
            havePrivacyPolicyAgreeOrRefuseButton(commonDetectInfo, customDetectInfo, detectResult);
            if (detectResult.getComplianceStatus() == MiitDetectStatusEnum.NON_COMPLIANCE) {
                conclusionList.add(String.format("%s在征得用户同意隐私政策前，未提供明确同意或者拒绝按钮。", executor(commonDetectInfo)));
                suggestionList.add(String.format("%s在征得用户同意隐私政策前，需要提供明确同意或者拒绝按钮。", executor(commonDetectInfo)));
            }
            if (!grantCollectActionList.isEmpty()) {
                conclusionList.add(String.format("%s在征得用户同意隐私政策前，存在%s等收集个人信息行为。",
                        executor(commonDetectInfo),
                        grantCollectActionList.stream().map(ActionAnalyse::getActionName).distinct().collect(Collectors.joining("、"))));
                suggestionList.add(String.format("%s在征得用户同意隐私政策前，不能存在任何收集个人信息行为。", executor(commonDetectInfo)));
            }
            checkBeforePrivacy(commonDetectInfo, detectResult, conclusionList, suggestionList);
            // 出现cookie传输个人信息
            List<TPrivacySensitiveWord> sensitiveWordList = new ArrayList<>(getSensitiveWords(commonDetectInfo, BehaviorStageEnum.BEHAVIOR_GRANT));
            // 出现cookie传输个人信息
            List<TPrivacySensitiveWord> sensitiveCookieList = sensitiveWordList.stream()
                    .filter(s -> org.apache.commons.lang3.StringUtils.isNotBlank(s.getCookie()) && !s.getCookie().equals(PinfoConstant.DETAILS_EMPTY))
                    .collect(Collectors.toList());
            if (!sensitiveCookieList.isEmpty()) {
                conclusionList.add(String.format("%s在征得用户同意隐私政策前，存在使用cookie传输个人信息行为。", executor(commonDetectInfo)));
                suggestionList.add(String.format("%s在征得用户同意隐私政策前，不能使用cookie传输个人信息。", executor(commonDetectInfo)));
            }
            CheckNonePrivacyActionResult checkSdkAction = checkNonePrivacySdkAction(commonDetectInfo, customDetectInfo, afterPrivacyActionList);
            if (!checkSdkAction.isNonInvolved()) {
                conclusionList.addAll(checkSdkAction.getConclusionList());
                suggestionList.addAll(checkSdkAction.getSuggestionList());
                grantCollectActionList.addAll(checkSdkAction.getAnalysisResult());
            }
            CheckNonePrivacyActionResult checkAppAction = checkNonePrivacyAppAction(commonDetectInfo, customDetectInfo, afterPrivacyActionList);
            if (!checkAppAction.isNonInvolved()) {
                conclusionList.addAll(checkAppAction.getConclusionList());
                suggestionList.addAll(checkAppAction.getSuggestionList());
                grantCollectActionList.addAll(checkAppAction.getAnalysisResult());
            }
            detectResult.setPrivacyPolicyFragment(joinFragmentText(commonDetectInfo, checkSdkAction, checkAppAction));
            if (!conclusionList.isEmpty() || !sensitiveCookieList.isEmpty()) {
                sortScreenshot(detectResult);
                detectResult.setConclusion(buildSequenceText(conclusionList));
                detectResult.setSuggestion(buildSequenceText(suggestionList));
                detectResult.setAnalysisResult(grantCollectActionList);
                detectResult.setSensitiveWordResult(getActionNetworkList(sensitiveCookieList));
                detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
            } else {
                detectResult.setConclusion(buildSequenceText("该检测项未发现风险"));
                detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
            }
        } else {
            // 存在个人信息行为就有风险
            grantCollectActionList.addAll(afterPrivacyActionList
                    .stream()
                    .filter(ActionAnalyse::getPersonal)
                    .collect(Collectors.toList()));
            if (!grantCollectActionList.isEmpty()) {
                conclusionList.add(String.format("未检测到隐私政策，存在收集%s等收集个人信息行为。",
                        grantCollectActionList.stream().map(ActionAnalyse::getActionName).distinct().collect(Collectors.joining("、"))));
                suggestionList.add(String.format("%s中隐私政策通过弹窗、文本链接、附件、常见问题（FAQs）等界面或形式展示", executor(commonDetectInfo)));
            }
            if (!conclusionList.isEmpty()) {
                sortScreenshot(detectResult);
                detectResult.setConclusion(buildSequenceText(conclusionList));
                detectResult.setSuggestion(buildSequenceText(suggestionList));
                detectResult.setAnalysisResult(grantCollectActionList);
                detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
            } else {
                detectResult.setConclusion(buildSequenceText("该检测项未发现风险"));
                detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
            }
        }
        return detectResult;
    }

    private void checkBeforePrivacy(CommonDetectInfo commonDetectInfo, DetectResult detectResult,
                                    List<String> conclusionList, List<String> suggestionList) {
        // 隐私政策授权前申请检查，只检查正常的遍历流程，其他的流程
        Set<String> applyActionNameList = new HashSet<>();
        List<ResultDataLogBO> commonUiList = commonDetectInfo.getResultDataLogs().stream()
                .filter(resultDataLogBO -> resultDataLogBO.getType() == ResultDataLogBoTypeEnum.COMMON.type)
                .collect(Collectors.toList());
        // 遍历界面
        for (ResultDataLogBO resultDataLogBO : commonUiList) {
            if (isPolicyPopup(resultDataLogBO)) {
                break;
            }
            if (isPermissionPopup(commonDetectInfo.getTerminalTypeEnum(), resultDataLogBO)) {
                TApplyPermission permission = findApplyPermissionRegex(commonDetectInfo, resultDataLogBO.getUiDumpResult());
                if (!applyActionNameList.contains(permission.getApplyName())) {
                    // 申请个人信息权限界面
                    addNoComplianceImage(commonDetectInfo, detectResult, resultDataLogBO);
                    applyActionNameList.add(permission.getApplyName());
                }
            }
        }
        if (!applyActionNameList.isEmpty()) {
            conclusionList.add(String.format("%s在征得用户同意隐私政策前，申请打开%s等个人信息权限。",
                    executor(commonDetectInfo), String.join("、", applyActionNameList)));
            suggestionList.add(String.format("%s在征得用户同意隐私政策前，不能申请打开个人信息相关权限。", executor(commonDetectInfo)));
        }
    }

}
