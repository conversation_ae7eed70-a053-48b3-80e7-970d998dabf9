package cn.ijiami.detection.miit.point.android.law35273;

import cn.ijiami.detection.VO.CheckList;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.ijiami.detection.utils.SdkUtils.actionKeyWordInSdk;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName Law35273DetectPoint.java
 * @Description 35273特有的一些检测方法
 * @createTime 2022年03月30日 18:05:00
 */
public abstract class Law35273DetectPoint extends PrivacyUiDetectPoint {

    /**
     * 检查所有阶段的行为是否有不在隐私政策内的行为
     * @param commonDetectInfo
     */
    protected CheckNonePrivacyActionResult checkAllStageNonePrivacyAction(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        // 行为收集检查
        List<ActionAnalyse> privacyActionList = new ArrayList<>();
        privacyActionList.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GRANT));
        privacyActionList.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_FRONT));
        privacyActionList.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GROUND));
        privacyActionList.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_EXIT));
        // APP和SDK行为权限检测
        CheckNonePrivacyActionResult result = new CheckNonePrivacyActionResult();
        result.setNonInvolved(true);
        CheckNonePrivacyActionResult checkSdkResult = checkNonePrivacySdkAction(commonDetectInfo, customDetectInfo, privacyActionList);
        result.getPrivacyPolicyFragmentKeyword().addAll(checkSdkResult.getPrivacyPolicyFragmentKeyword());
        result.getCheckListRowInfo().addAll(checkSdkResult.getCheckListRowInfo());
        result.getCheckListTextKeyword().addAll(checkSdkResult.getCheckListTextKeyword());
        if (!checkSdkResult.isNonInvolved()) {
            result.getConclusionList().addAll(checkSdkResult.getConclusionList());
            result.getSuggestionList().addAll(checkSdkResult.getSuggestionList());
            result.getAnalysisResult().addAll(checkSdkResult.getAnalysisResult());
            result.getImageLogsList().addAll(checkSdkResult.getImageLogsList());
            result.setNonInvolved(checkSdkResult.isNonInvolved());
        }
        CheckNonePrivacyActionResult checkAppResult = checkNonePrivacyAppAction(commonDetectInfo, customDetectInfo, privacyActionList);
        result.getPrivacyPolicyFragmentKeyword().addAll(checkAppResult.getPrivacyPolicyFragmentKeyword());
        if (!checkAppResult.isNonInvolved()) {
            result.getConclusionList().addAll(checkAppResult.getConclusionList());
            result.getSuggestionList().addAll(checkAppResult.getSuggestionList());
            result.getAnalysisResult().addAll(checkAppResult.getAnalysisResult());
            result.getImageLogsList().addAll(checkAppResult.getImageLogsList());
            result.setNonInvolved(checkAppResult.isNonInvolved());
        }
        return result;
    }

    /**
     * 检查是否有不在隐私政策内的SDK行为
     * @param commonDetectInfo
     * @param actionAnalyseList
     */
    protected CheckNonePrivacyActionResult checkNonePrivacySdkAction(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo, List<ActionAnalyse> actionAnalyseList) {
        CheckNonePrivacyActionResult result = new CheckNonePrivacyActionResult();
        Map<String, List<ActionAnalyse>> sdkMap = new HashMap<>();
        List<ActionAnalyse> sdkAnalyseList = actionAnalyseList.stream()
                .filter(ActionAnalyse::getPersonal)
                .filter(analyse -> ExecutorTypeEnum.SDK.getValue().equals(analyse.getExecutorType()))
                .collect(Collectors.toList());
        // 授权申请行为
        Map<Long, String> decideRules = customDetectInfo.getActionWithKeyRegex();
        sdkAnalyseList.forEach(analyse -> {
            String[] sdkNames = analyse.getExecutor().split(",");
            for (String sdkName:sdkNames) {
                // 优先匹配第三方sdk清单的数据
                Optional<CheckList.Row> sdkRow = findCheckListBySdkName(commonDetectInfo, sdkName);
                if (sdkRow.isPresent()) {
                    String keyWord = decideRules.get(analyse.getActionId());
                    // sdk行为没在第三方sdk清单中声明
                    if (actionKeyWordInSdk(keyWord, sdkRow.get())) {
                        // 把第三方sdk转成文字加到隐私片段的展示数据中
                        result.getCheckListRowInfo().add(sdkRow.get().privacyPolicyFragment());
                    } else {
                        List<ActionAnalyse> actionList = sdkMap.computeIfAbsent(sdkName, k -> new ArrayList<>());
                        actionList.add(analyse);
                    }
                    continue;
                }
                Optional<String> checkListKeyword = findKeywordInCheckListText(commonDetectInfo, sdkName);
                if (checkListKeyword.isPresent()) {
                    // 如果第三方sdk清单没有解析出表格数据，则匹配完整的文本
                    result.getCheckListRowInfo().add(checkListKeyword.get());
                    String keyWord = decideRules.get(analyse.getActionId());
                    if (StringUtils.isNotBlank(keyWord)) {
                        if (Pattern.compile(keyWord).matcher(commonDetectInfo.getThirdPartySharingChecklist().getFullText()).find()) {
                            result.getCheckListRowInfo().add(keyWord);
                        } else {
                            // sdk行为没在第三方sdk清单文本中
                            List<ActionAnalyse> actionList = sdkMap.computeIfAbsent(sdkName, k -> new ArrayList<>());
                            actionList.add(analyse);
                        }
                    }
                    continue;
                }
                Optional<String> policyKeyword = findKeywordInPolicyContent(commonDetectInfo, sdkName);
                if (policyKeyword.isPresent()) {
                    result.getPrivacyPolicyFragmentKeyword().add(policyKeyword.get());
                    String keyWord = decideRules.get(analyse.getActionId());
                    if (StringUtils.isNotBlank(keyWord)) {
                        if (Pattern.compile(keyWord).matcher(commonDetectInfo.getPrivacyPolicyContent()).find()) {
                            result.getPrivacyPolicyFragmentKeyword().add(keyWord);
                        } else {
                            // sdk行为没在隐私政策中
                            List<ActionAnalyse> actionList = sdkMap.computeIfAbsent(sdkName, k -> new ArrayList<>());
                            actionList.add(analyse);
                        }
                    }
                } else {
                    // sdk没在隐私政策中
                    List<ActionAnalyse> actionList = sdkMap.computeIfAbsent(sdkName, k -> new ArrayList<>());
                    actionList.add(analyse);
                }
            }
        });
        if (!sdkMap.isEmpty()) {
            String conclusion = sdkMap
                    .entrySet()
                    .stream()
                    .map(entry ->
                        String.format("• %s存在%s等收集个人信息行为",
                            entry.getKey(),
                            entry.getValue().stream().map(ActionAnalyse::getActionName).distinct().collect(Collectors.joining("、"))))
                    .collect(Collectors.joining("\n"));
            String pluginName = pluginName(commonDetectInfo);
            if (commonDetectInfo.nonHasPrivacyPolicy()) {
                result.getConclusionList().add(String.format("未提供隐私政策，%s:\n%s。", pluginName, conclusion));
            } else {
                result.getConclusionList().add(String.format("%s:\n%s，未在隐私中说明。", pluginName, conclusion));
            }
            result.getSuggestionList().add(String.format("请在隐私政策中声明收集个人信息的全部%s，以及%s收集的个人信息。", pluginName, pluginName));
            result.getAnalysisResult().addAll(sdkMap.values().stream().flatMap(List::stream).collect(Collectors.toList()));
            result.setNonInvolved(false);
        } else {
            result.setNonInvolved(true);
        }
        return result;
    }

    /**
     * 检查是否有不在隐私政策内的APP行为
     * @param commonDetectInfo
     * @param actionAnalyseList
     */
    protected CheckNonePrivacyActionResult checkNonePrivacyAppAction(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo, List<ActionAnalyse> actionAnalyseList) {
        CheckNonePrivacyActionResult result = new CheckNonePrivacyActionResult();
        // 授权申请行为
        Map<Long, String> decideRules = customDetectInfo.getActionWithKeyRegex();
        Set<String> actionRules = new HashSet<>();
        // 未声明的app行为名
        List<ActionAnalyse> noMatchAppActionList = actionAnalyseList.stream()
                .filter(ActionAnalyse::getPersonal)
                .filter(a -> ExecutorTypeEnum.APP.getValue().equals(a.getExecutorType()))
                .filter(a -> {
                    String keyWord = decideRules.get(a.getActionId());
                    if (StringUtils.isNotBlank(keyWord) && StringUtils.isNotBlank(commonDetectInfo.getPrivacyPolicyContent())) {
                        actionRules.add(keyWord);
                        return !Pattern.compile(keyWord).matcher(commonDetectInfo.getPrivacyPolicyContent()).find();
                    } else {
                        // 没有关键词，可以认为不在隐私政策文本内
                        return true;
                    }
                })
                .collect(Collectors.toList());
        // 提取隐私文本
        result.getPrivacyPolicyFragmentKeyword().addAll(actionRules);
        if (!noMatchAppActionList.isEmpty()) {
            String format = commonDetectInfo.nonHasPrivacyPolicy() ? "未提供隐私政策，" + executor(commonDetectInfo) + "存在%s等收集个人信息行为。" :
                    executor(commonDetectInfo) + "存在%s等收集个人信息行为，未在隐私政策中声明。";
            result.getConclusionList().add(String.format(format,
                    noMatchAppActionList.stream().map(ActionAnalyse::getActionName).distinct().collect(Collectors.joining("、"))));
            result.getSuggestionList().add("请在隐私政策中声明" + executor(commonDetectInfo) + "收集的个人信息。");
            result.getAnalysisResult().addAll(noMatchAppActionList);
            result.setNonInvolved(false);
        } else {
            result.setNonInvolved(true);
        }
        return result;
    }

    protected String joinFragmentText(CommonDetectInfo commonDetectInfo, CheckNonePrivacyActionResult... checkActionResults) {
        if (checkActionResults.length == 1) {
            return StringUtils.join(checkActionResults[0].getCheckListRowInfo(), "\n")
                    + MiitWordKit.defaultKeywordExtractionFromContent(commonDetectInfo.getPrivacyPolicyContent(), checkActionResults[0].getPrivacyPolicyFragmentKeyword());
        } else {
            Set<String> fragment = new HashSet<>();
            Set<String> privacyPolicyKeyword = new HashSet<>();
            Set<String> checkListTextKeyword = new HashSet<>();
            for (CheckNonePrivacyActionResult result : checkActionResults) {
                fragment.addAll(result.getCheckListRowInfo());
                privacyPolicyKeyword.addAll(result.getPrivacyPolicyFragmentKeyword());
                checkListTextKeyword.addAll(result.getCheckListTextKeyword());
            }
            return buildFragment(commonDetectInfo, fragment, checkListTextKeyword, privacyPolicyKeyword);
        }
    }

    protected void checkNonePrivacyPolicyActionAnalyse(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo,
                                                       List<ActionAnalyse> resultList, List<String> conclusionList, List<String> suggestionList) {
        String executorName = executor(commonDetectInfo);
        List<ActionAnalyse> grantAnalyse = filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GRANT);
        grantAnalyse.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_FRONT));
        grantAnalyse.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GROUND));
        grantAnalyse.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_EXIT));
        List<ActionAnalyse> collectActionList = grantAnalyse.stream()
                .filter(ActionAnalyse::getPersonal)
                .collect(Collectors.toList());
        if (!collectActionList.isEmpty()) {
            resultList.addAll(collectActionList);
            conclusionList.add(String.format("%s未提供隐私政策，存在%s等收集个人信息的行为。", executorName, joinActionNames(collectActionList)));
            suggestionList.add(nonePrivacyDetailSuggestion(commonDetectInfo));
            suggestionList.add("请在隐私政策中详细描述收集个人信息的目的、方式、范围，并且在同意隐私政策后再获取相关个人信息。");
        }
    }

    protected void checkHasPrivacyPolicyActionAnalyse(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo,
                                                      List<ActionAnalyse> resultList, List<String> conclusionList, List<String> suggestionList) {
        // 隐私政策授权前收集检查
        String executorName = executor(commonDetectInfo);
        List<ActionAnalyse> grantAnalyse = filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GRANT);
        List<ActionAnalyse> collectActionList = grantAnalyse.stream()
                .filter(ActionAnalyse::getPersonal)
                .collect(Collectors.toList());
        if (!collectActionList.isEmpty()) {
            resultList.addAll(collectActionList);
            conclusionList.add(String.format("在同意隐私政策前，存在%s等收集个人信息的行为。", joinActionNames(collectActionList)));
            suggestionList.add("请不要收集非必要的个人信息，必要个人信息，调整到同意隐私政策后获取。");
        }
        // APP在前台静默运行时, 是否有固定触发
        List<ActionAnalyse> grantCycleList = filterAndCountActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GRANT)
                .stream()
                .filter(actionAnalyse -> actionAnalyse.getFrequency() != null && actionAnalyse.getFrequency() > 1)
                .collect(Collectors.toList());
        if (!grantCycleList.isEmpty()) {
            resultList.addAll(grantCycleList);
            conclusionList.add(String.format(
                    "%s在前台静默运行时，存在%s等收集个人信息行为，收集个人信息的频率超出实现产品或服务的业务功能所必需的最低合理频率。",
                    executorName, joinActionNames(grantCycleList)));
            suggestionList.add(String.format("%s在前台静默运行时，如无合理的业务场景，不能收集个人信息。", executorName));
        }
        List<ActionAnalyse> groundCycleList = filterAndCountActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GROUND)
                .stream()
                .filter(actionAnalyse -> actionAnalyse.getFrequency() != null && actionAnalyse.getFrequency() > 1)
                .collect(Collectors.toList());
        if (!groundCycleList.isEmpty()) {
            resultList.addAll(groundCycleList);
            conclusionList.add(String.format(
                    "%s在后台运行时，存在%s等收集个人信息行为，收集个人信息的频率超出实现产品或服务的业务功能所必需的最低合理频率。",
                    executorName, joinActionNames(groundCycleList)));
            suggestionList.add(String.format("%s在后台运行时，收集个人信息的频率超出实现产品或服务的业务功能所必需的最低合理频率。", executorName));
        }
        List<ActionAnalyse> frontCycleList = filterAndCountActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_FRONT)
                .stream()
                .filter(actionAnalyse -> actionAnalyse.getFrequency() != null && actionAnalyse.getFrequency() > 1)
                .collect(Collectors.toList());
        if (!frontCycleList.isEmpty()) {
            resultList.addAll(frontCycleList);
            conclusionList.add(String.format(
                    "%s在前台运行时，收集%s的频率超过1次/秒，收集个人信息的频率超出实现产品或服务的业务功能所必需的最低合理频率。",
                    executorName, joinActionNames(frontCycleList)));
            suggestionList.add(String.format("%s在前台运行时，收集个人信息的频率不能超过1次/秒。", executorName));
        }
    }

    protected String joinActionNames(List<ActionAnalyse> actionAnalyses) {
        return actionAnalyses.stream()
                .map(ActionAnalyse::getActionName)
                .distinct()
                .collect(Collectors.joining("、"));
    }

}
