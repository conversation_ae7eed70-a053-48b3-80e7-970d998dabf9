package cn.ijiami.detection.miit.point.ios.law164;

import cn.ijiami.detection.enums.ResultDataLogBoTypeEnum;
import cn.ijiami.detection.helper.PermissionNameHelper;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.enums.MiitIosAppRestartEnum;
import cn.ijiami.detection.miit.enums.MiitUITypeEnum;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint20106;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.ijiami.detection.utils.CommonUtil.strAndContains;

/**
 * APP在用户明确拒绝通讯录/定位/短信/录音/相机/XXX等权限申请后，重新运行时，仍向用户弹窗申请开启与当前服务场景无关的权限，影响用户正常使用。
 * <p>
 * <blockquote><pre>
 * 	截图
 * 	检测结果会返回判断标识和截图，判断拒绝权限授权弹窗后重新启动应用，应用是否仍弹出重复窗口，如果是，则违规，否则合规
 * </pre></blockquote>
 *
 * <AUTHOR>
 * @date 2020/12/22 17:42
 **/
@EnableDetectPoint
public class DetectPoint1020106 extends DetectPoint20106 {
    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }

    /**
     * 重写重启后是否有弹窗的判断逻辑，ios的流程与android的不一样
     * @param commonDetectInfo
     * @param detectResult
     * @param ignorePermissionNames
     */
    @Override
    protected void checkRestartApplyPermission(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo, DetectResult detectResult, List<String> ignorePermissionNames) {
        // 遍历界面，找出拒绝阶段的数据
        List<ResultDataLogBO> rejectList = commonDetectInfo.getResultDataLogs().stream()
                .filter(resultDataLogBO -> resultDataLogBO.getUiDumpResult() != null
                        && resultDataLogBO.getType() == ResultDataLogBoTypeEnum.PERMISSION_REJECT.type)
                .collect(Collectors.toList());
        for (int index = 0; index < rejectList.size(); index++) {
            //权限拒绝页面
            ResultDataLogBO refuseResult = rejectList.get(index);
            if (refuseResult.getUiDumpResult().getUiType() == MiitUITypeEnum.DISAGREE_PERMISSION.getValue()) {
                // 申请权限页面(获取权限文本)
                String applyName = findApplyPermissionRegex(commonDetectInfo, refuseResult.getUiDumpResult()).getApplyName();
                if (ignorePermissionNames.stream().anyMatch(applyName::contains)) {
                    continue;
                }
                // ios的2阶段可能是在最后，所以从0开始去判断其它阶段是否有弹窗
                for (int reApplyIndex = index + 1; reApplyIndex < rejectList.size(); reApplyIndex++) {
                    // 再次申请权限页面
                    ResultDataLogBO reApplyResult = rejectList.get(reApplyIndex);
                    // 找出当前遍历模式中重启后的界面
                    if (Objects.isNull(reApplyResult.getIosAppRestart())
                            || reApplyResult.getIosAppRestart() != MiitIosAppRestartEnum.COMMON.itemValue()) {
                        continue;
                    }
                    String uiText = reApplyResult.getUiDumpResult().getPermissionText();
                    // ios的重启弹窗，是打开一个开发者自定义的Dialog，引导用户去设置里打开权限，所以要通过文本去判断
                    if (!strAndContains(uiText, "打开", "前往", "开启")) {
                        continue;
                    }
                    String reApplyName = findApplyPermissionRegex(commonDetectInfo, reApplyResult.getUiDumpResult()).getApplyName();
                    // 频繁申请权限(重启应用后)
                    if (StringUtils.equals(reApplyName, applyName)) {
                        addNoComplianceImage(commonDetectInfo, detectResult, refuseResult);
                        addNoComplianceImage(commonDetectInfo, detectResult, reApplyResult);
                        break;
                    }
                }
            }
        }
    }
}
