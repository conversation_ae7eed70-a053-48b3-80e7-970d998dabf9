package cn.ijiami.detection.miit.point.harmony.law41391;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.point.android.law41391.DetectPoint350203;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350203.java
 * @Description
 * App收集的个人信息是否满足告知同意要求
 * 3. 公开的收集使用规则是否完整
 * 判断规则：
 * f)不应通过捆绑不同类型服务、捆绑基本业务功能和扩展业务功能、批量申请授权等方式，诱导、强迫用户一次性同意个人信息收集请求；
 * 发现风险：
 * 【无隐私政策】
 * 1、未检测到隐私政策
 * 【有隐私政策】
 * 1、隐私中政策中未对【SDK名称列表】进行说明。
 */
@EnableDetectPoint
public class DetectPoint40350203 extends DetectPoint350203 {


}
