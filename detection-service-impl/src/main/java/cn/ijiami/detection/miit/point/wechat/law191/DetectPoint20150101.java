package cn.ijiami.detection.miit.point.wechat.law191;

import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import cn.ijiami.detection.miit.point.android.law191.DetectPoint150101;
import cn.ijiami.detection.utils.StreamUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 既未经用户同意，也未做匿名化处理，App客户端直接向第三方提供个人信息，包括通过客户端嵌入的第三方代码、插件等方式向第三方提供个人信息；
 * <p>
 * 发现风险：
 * 1、隐私政策授权前，SDK触发个人信息行为
 * 2、SDK获取的个人信息行为未在隐私政策找到相应关键词
 * 3、触发个人信息SDK未在隐私政策说明
 */
@EnableDetectPoint
public class DetectPoint20150101 extends DetectPoint150101 {

}
