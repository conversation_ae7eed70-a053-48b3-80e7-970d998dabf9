package cn.ijiami.detection.miit.point.android.law41391;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint10101;
import cn.ijiami.detection.miit.point.android.law191.DetectPoint140201;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350202.java
 * @Description
 * App收集的个人信息是否满足告知同意要求
 * 2. App是否存在用户拒绝或撤回同意收集非必要个人信息时而拒绝用户使用该App的基本业务情况
 * 判断规则：
 * d)当用户同意收集App必要个人信息时，应保障用户可拒绝或撤回同意收集非必要个人信息且不应因用户拒绝或撤回同意提供非必要个人信息，而拒绝用户使用该App的基本业务功能；
 * 注2：例如提供“退出”“上一步”“关闭”“取消”的按钮等方式供用户拒绝个人信息收集。
 * 发现风险：
 * 建议用户拒绝或撤回同意收集非必要个人信息时或打开非必要权限仍提供App的基础业务功能。
 */
@EnableDetectPoint
public class DetectPoint350202 extends DetectPoint140201 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.getBaseDetectResult(commonDetectInfo, customDetectInfo);
        // 遍历界面
        if (isAppQuitAfterDisagreePermission(commonDetectInfo, detectResult)) {
            String executeName = executor(commonDetectInfo);
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
            detectResult.setConclusion(buildSequenceTextFormat("%s存在用户拒绝或撤回同意收集非必要个人信息时而拒绝用户使用该%s的基本业务情况", executeName, executeName));
            detectResult.setSuggestion(buildSequenceTextFormat("建议用户拒绝或撤回同意收集非必要个人信息时或打开非必要权限仍提供%s的基础业务功能。", executeName));
        } else {
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
            detectResult.setConclusion(buildSequenceText("该检测项未发现风险"));
        }
        return detectResult;
    }

}
