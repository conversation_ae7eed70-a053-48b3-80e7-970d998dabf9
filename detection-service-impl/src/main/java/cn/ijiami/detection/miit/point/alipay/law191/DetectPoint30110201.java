package cn.ijiami.detection.miit.point.alipay.law191;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.point.android.law191.DetectPoint110201;

/**
 * 在App首次运行时未通过弹窗等明显方式提示用户阅读隐私政策等收集使用规则；
 *
 * 未发现风险：
 * 1、检测到隐私政策
 * 发现风险：
 * 1、未检测到隐私政策
 */
@EnableDetectPoint
public class DetectPoint30110201 extends DetectPoint110201 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }

}
