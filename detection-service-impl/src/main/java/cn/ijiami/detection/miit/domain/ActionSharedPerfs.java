package cn.ijiami.detection.miit.domain;

import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ActionSharedPerfs.java
 * @Description 储存行为
 * @createTime 2022年04月02日 11:53:00
 */
@Data
public class ActionSharedPerfs implements Serializable {

    /**
     * 行为id
     */
    private Long              actionId;

    @ApiModelProperty(value = "行为阶段 授权前行为、前台运行行为、后台运行行为")
    private BehaviorStageEnum behaviorStage;

    @ApiModelProperty(value = "触发时间")
    private Date actionTime;

    @ApiModelProperty(value = "主体为2存储sdkId，json格式")
    private String sdkIds;

    @ApiModelProperty(value = "主体类型 1APP 2SDK")
    private Integer executorType;

    @ApiModelProperty(value = "主体")
    private String executor;

    @ApiModelProperty(value = "包名")
    private String packageName;
    /**
     * 代码段
     */
    @ApiModelProperty(value = "代码段")
    private String code;
}
