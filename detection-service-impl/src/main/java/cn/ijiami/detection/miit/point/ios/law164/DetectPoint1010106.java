package cn.ijiami.detection.miit.point.ios.law164;

import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint10106;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * APP以隐私政策弹窗的形式向用户明示收集使用规则，但未见清晰明示APP收集设备MAC地址、软件安装列表等的目的方式范围，用户同意隐私政策后，存在收集设备MAC地址、软件安装列表的行为。
 * <blockquote><pre>
 * 1、隐私政策截图
 * 2、隐私政策中检测不到「MAC地址、软件安装列表」等关键词——无数据
 * 3、共XXX次（MAC地址、软件安装列表）
 * 触发时间 SDK 主体名称 个人信息行为名称 频次；
 * 「前台行为」
 *
 * 1、判断有隐私政策
 * 2、非静默安装状态下，判断是否有触发收集设备MAC地址、软件安装列表的行为，且触发主体是SDK，如果有以上任何一种行为的触发，再关键词匹配隐私政策文本中，是否有该行为的说明，如果没有，则违规，有则合规
 * 涉及行为：110036L
 * </pre></blockquote>
 *
 */
@EnableDetectPoint
public class DetectPoint1010106 extends DetectPoint10106 {
    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        // 不存在隐私政策,不涉及此项
        if (commonDetectInfo.nonHasPrivacyPolicy()) {
            return this.buildNonInvolved(commonDetectInfo, customDetectInfo);
        }

        // 统计并提取行为数据
        List<ActionAnalyse> actionAnalyses =
                this.filterAndCountActionByStageAndExe(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_FRONT, ExecutorTypeEnum.SDK);

        actionAnalyses = checkNotComplianceActionExistInFragment(customDetectInfo, getBaseTargetPrivacyPolicyText(commonDetectInfo, customDetectInfo), actionAnalyses);

        String privacyPolicyFragmentText = MiitWordKit.defaultKeywordExtractionFromContent(commonDetectInfo.getPrivacyPolicyContent(), customDetectInfo.getDecideRuleKeys());
        DetectResult detectResult = this.buildResultByActionAnalyseThenKeyWords(commonDetectInfo, customDetectInfo, actionAnalyses);
        detectResult.setPrivacyPolicyFragment(privacyPolicyFragmentText);
        return detectResult;
    }
}
