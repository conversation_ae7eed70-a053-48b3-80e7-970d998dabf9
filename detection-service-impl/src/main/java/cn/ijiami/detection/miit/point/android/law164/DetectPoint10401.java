package cn.ijiami.detection.miit.point.android.law164;

import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.*;
import cn.ijiami.detection.miit.enums.MiitDataTypeEnum;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.kit.MiitActionKit;
import cn.ijiami.detection.miit.kit.MiitLogKit;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.util.*;

/**
 * APP的YYY页面或功能存在定向推送功能，但隐私政策未见向用户告知，将收集的用户个人信息用于定向推送、精准营销。
 * <p>
 * <blockquote><pre>
 * 1、隐私政策截图
 * 2、隐私政策中检测到「定向推送、个性化展示、精准营销」等关键词语句
 * 3、共XXX次（个人信息行为）
 * 触发时间 （推送）SDK 主体名称 个人信息行为名称 频次；
 * 触发时间 （推送） 主体名称 个人信息行为名称 频次；
 * 「前台行为」
 *
 * 1、判断第三方SDK检测结果中，是否有检测出推送类SDK，如果有，则进一步根据关键字，匹配隐私政策文本中，是否匹配出定向推送、精准营销等功能的描述，如果没有，则违规，有则合规
 * </pre></blockquote>
 * 2、界面上有关键词，但是隐私政策里没有，判断为违规
 *
 * <AUTHOR>
 * @date 2020/12/22 17:42
 **/
@EnableDetectPoint
public class DetectPoint10401 extends AbstractDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        // JZ.END(未测试)
        DetectResult detectResult = hasRecommendUi(commonDetectInfo, customDetectInfo);
        if (commonDetectInfo.nonHasPrivacyPolicy()) {
            return detectResult;
        }
        List<ActionAnalyse> actionAnalyses =
                this.filterAndCountActionByStageAndExe(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_FRONT, ExecutorTypeEnum.SDK);
        // 推送类sdk，个人信息相关的名称
        List<ActionAnalyse> pushSdkAction = this.filterPersonalAndPushFromResult(commonDetectInfo, actionAnalyses);
        //筛选出不合规的行为
    	pushSdkAction = checkNotComplianceActionExistInFragment(customDetectInfo, getBaseTargetPrivacyPolicyText(commonDetectInfo, customDetectInfo), pushSdkAction);
        detectResult.setAnalysisResult(actionAnalyses);
        detectResult.setCount(MiitActionKit.countActionAnalyse(actionAnalyses));
        String relatedKeywords = this.getBaseTargetPrivacyPolicyText(commonDetectInfo, customDetectInfo);
        // 获取关键词文本
        detectResult.setPrivacyPolicyFragment(relatedKeywords);
        // 存在推送类sdk,匹配关键字是否存在
        if (!pushSdkAction.isEmpty() && StringUtils.isBlank(relatedKeywords)) {
            // 不存在关键字，违规
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
        }
        // 存放隐私政策截图
        detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
        return detectResult;
    }

    /**
     * 是否界面上有推荐关键词
     * @param commonDetectInfo
     * @param customDetectInfo
     * @return
     */
    private DetectResult hasRecommendUi(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        // 判断依据 一
        DetectResult detectResult = this.buildEmptyConclusionNonInvolved(commonDetectInfo, customDetectInfo);
        Set<String> keywordSet = customDetectInfo.getDecideRuleKeys();
        if (CollectionUtils.isEmpty(keywordSet)) {
            return detectResult;
        }
        // 判断依据 二，存放特定的截图
        Set<String> images = new HashSet<>();
        // 获取关于推送类截图
        List<ResultDataLogBO> resultDataLogs = MiitLogKit.filterByTag(commonDetectInfo.getResultDataLogs(), MiitDataTypeEnum.UI_PERSONAL);
        if (!CollectionUtils.isEmpty(resultDataLogs)) {
            // 根据时间获取对应tag的图片 字符串去重
            List<String> md5List = new ArrayList<>();
            for (ResultDataLogBO resultDataLogBO : resultDataLogs) {
                if(resultDataLogBO.getUiDumpResult()==null) {
                    continue;
                }
                String fullText = resultDataLogBO.getUiDumpResult().getFullText();
                
                String privacyHtmlRegex = cn.ijiami.detection.utils.SeleniumUtils.privacyHtmlRegex;
                boolean isPrivacy = cn.ijiami.detection.helper.PrivacyPolicyHtmlHelper.isPrivacyDetail(fullText, privacyHtmlRegex);
                
                if (isPrivacy || resultDataLogBO.getDataTag() != MiitDataTypeEnum.UI_PERSONAL.getValue()
                        || isMatchAllStr(fullText, "帐号描述", "小程序", "搜索")
                        || isMatchAllStr(fullText, "推荐给朋友", "小程序", "设置")) {
                    continue;
                }
                //并判断页面是否存在定推关键词
                String md5 = getMD5Str(fullText);
                String recommendWords = MiitWordKit.defaultKeywordExtractionFromContent(fullText, keywordSet);
                if (!md5List.contains(md5) && org.apache.commons.lang.StringUtils.isNotBlank(recommendWords)) {
                    String absolutePath = commonDetectInfo.getFilePath() + File.separator + resultDataLogBO.getImgPath();
                    if (MiitLogKit.isFileExist(absolutePath)) {
                        images.add(absolutePath);
                    }
                    md5List.add(md5);
                }
            }
        }
        String privacyPolicyText = MiitWordKit.defaultKeywordExtractionFromContent(commonDetectInfo.getPrivacyPolicyContent(), keywordSet);
        detectResult.setPrivacyPolicyFragment(privacyPolicyText);
        detectResult.setScreenshots(images);
        // 没有关键词片段，直接不涉及
        if (images.isEmpty()) {
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        } else {
            // 有涉及到的字样，且有截图，则算不涉及
            if (org.apache.commons.lang.StringUtils.isNotBlank(privacyPolicyText) && !CollectionUtils.isEmpty(images)) {
                detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
            } else {
                detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
            }
        }
        // 存放隐私政策截图
        detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
        return detectResult;
    }

    private boolean isMatchAllStr(String text, String... search) {
        for (String s : search) {
            if (!StringUtils.containsAny(text, s)) {
                return false;
            }
        }
        return true;
    }

}
