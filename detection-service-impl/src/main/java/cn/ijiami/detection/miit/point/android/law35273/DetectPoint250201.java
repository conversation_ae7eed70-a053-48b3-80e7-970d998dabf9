package cn.ijiami.detection.miit.point.android.law35273;

import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.LawJudgmentHelper;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint200501.java
 * @Description
 * 35273检测
 * 5.2.b
 * 自动采集个人信息的频率应是实现产品或服务的业务功能所必需的最低频率
 *
 * 判断规则
 * 发现风险
 * 【有隐私政策】
 * 1 同意隐私政策前，存在收集个人信息行为
 * 2 前台静默状态下收集个人信息。
 * 3 前台运行时，切换功能菜单收集同一个人信息。
 * 4 后台运行时收集个人信息。
 * 5 前台运行时，频率超过1次/秒。
 * 【无隐私政策】
 * 存在收集个人信息行为
 * @createTime 2022年03月24日 17:32:00
 */
@EnableDetectPoint
public class DetectPoint250201 extends Law35273DetectPoint {
    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.buildNonInvolved(commonDetectInfo, customDetectInfo);
        // 隐私政策截图截图地址
        detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
        List<String> conclusionList = new ArrayList<>();
        List<String> suggestionList = new ArrayList<>();
        // 证据列表
        List<ActionAnalyse> resultList = new ArrayList<>();
        if (commonDetectInfo.nonHasPrivacyPolicy()) {
            checkNonePrivacyPolicyActionAnalyse(commonDetectInfo, customDetectInfo, resultList, conclusionList, suggestionList);
        } else {
            checkHasPrivacyPolicyActionAnalyse(commonDetectInfo, customDetectInfo, resultList, conclusionList, suggestionList);
        }
        // 没有触发个人信息行为的SDK，不涉及
        if (conclusionList.isEmpty()) {
            detectResult.setConclusion(buildSequenceText("该检测项未发现风险"));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        } else {
            detectResult.setConclusion(buildSequenceText(conclusionList));
            detectResult.setAnalysisResult(resultList);
            detectResult.setSuggestion(buildSequenceText(suggestionList));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
        }
        return detectResult;
    }

}
