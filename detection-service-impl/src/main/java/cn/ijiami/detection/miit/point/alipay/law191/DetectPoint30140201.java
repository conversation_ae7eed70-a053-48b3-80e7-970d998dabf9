package cn.ijiami.detection.miit.point.alipay.law191;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.point.android.law191.DetectPoint140201;

/**
 * 因用户不同意收集非必要个人信息或打开非必要权限，拒绝提供业务功能；
 *
 * 发现风险：
 * 1、拒绝权限之后App退出
 */
@EnableDetectPoint
public class DetectPoint30140201 extends DetectPoint140201 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }
}
