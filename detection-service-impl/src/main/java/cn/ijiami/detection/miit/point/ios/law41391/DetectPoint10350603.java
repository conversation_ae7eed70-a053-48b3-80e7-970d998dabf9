package cn.ijiami.detection.miit.point.ios.law41391;

import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;
import cn.ijiami.detection.miit.point.android.law41391.DetectPoint350603;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350603.java
 * @Description
 * 三方SDK是否存在超范围申请使用个人信息权限
 * 3. App是否存在超频次获取个人信息行为
 * 判断规则：
 * e)应对SDK申请使用权限进行审核，确保其申请的权限具有明确、合理的使用目的，并监督SDK的个人信息收集行为是否超出约定或用户同意的范围。
 * 发现风险：
 * APP未见向用户明示SDK的收集使用规则，未经用户同意，SDK存在收集通【取表：行为-关键词中个人信息列的名称，多项时，用“、”隔开】等信息的行为，
 * 非服务所必需且无合理应用场景，超出与收集个人信息时所声称的目的具有直接或合理关联的范围。
 */
@EnableDetectPoint
public class DetectPoint10350603 extends DetectPoint350603 {

}
