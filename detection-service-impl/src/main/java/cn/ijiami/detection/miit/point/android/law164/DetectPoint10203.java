package cn.ijiami.detection.miit.point.android.law164;

import java.util.ArrayList;
import java.util.List;

import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;

/**
 * APP未见向用户明示SDK的收集使用规则，未经用户同意，SDK存在收集通讯录、短信、通话记录、相机等信息的行为，非服务所必需且无合理应用场景，超出与收集个人信息时所声称的目的具有直接或合理关联的范围。
 * <p>
 * <blockquote><pre>
 * 1、无隐私政策
 * 2、共XXX次（通讯录、短信、通话记录、相机）
 * 触发时间 SDK 主体名称 个人信息行为名称 频次；
 * 触发时间 SDK 主体名称 个人信息行为名称 频次；
 * 「前台行为」
 * 1、隐私政策截图
 * 2、隐私政策中检测不到「通讯录、短信、通话记录、相机」等关键词——无数据
 * 2、共XXX次（通讯录、短信、通话记录、相机）
 * 触发时间 SDK 主体名称 个人信息行为名称 频次；
 * 触发时间 SDK 主体名称 个人信息行为名称 频次；
 * 「授权前行为」
 *
 * 分两种情况：
 * 一、无隐私政策
 * 1、判断无隐私政策
 * 2、判断自动化遍历状态下，SDK是否有触发收集设备MAC地址、软件安装列表的行为，如果有，则违规，否则合规
 * 二、有隐私政策
 * 1、判断有隐私政策
 * 2、判断静默安装状态下，SDK是否有触发收集设备MAC地址、软件安装列表的行为，如果有任一行为触发，再根据关键字匹配隐私政策文本，看是否有允许SDK收集设备MAC地址、软件安装列表的行为说明，如果有，则合规，否则违规
 *
 * 涉及行为：24009L,28005L
 *
 * </pre></blockquote>
 * 
 * 
 * 分两种情况：
 * 一、无隐私政策
 *      1、判断无隐私政策
 *      2、判断自动化遍历状态下，SDK是否有触发收集设备MAC地址、软件安装列表的行为，如果有，则违规，否则合规
 * 二、有隐私政策
 *      1、判断有隐私政策
 *      2、判断静默安装状态下，SDK是否有触发收集设备MAC地址、软件安装列表的行为，如果有任一行为触发，再根据关键字匹配隐私政策文本，看是否有允许SDK收集设备MAC地址、软件安装列表的行为说明，如果有，则合规，否则违规

 * 新增判断：
 *      有隐私政策：
 *      场景一：
 *       1、有隐私政策
 *       2、隐私政策文本中没有行为关键词
 *       3、SDK触发了该项判断依据中罗列的行为【前台行为】
 *      场景二：
 *       1、有隐私政策
 *       2、隐私政策文本中有行为关键词
 *       3、SDK触发了该项判断依据中罗列的行为【授权前行为】
 *
 * <AUTHOR>
 * @date 2020/12/22 17:42
 **/
@EnableDetectPoint
public class DetectPoint10203 extends AbstractDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return buildNonInvolved(commonDetectInfo, customDetectInfo);
    }

    private DetectResult oldDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
		// JZ.END(未测试)
		boolean hasPrivacyPolicy = commonDetectInfo.isHasPrivacyPolicy();
		BehaviorStageEnum behaviorStage = hasPrivacyPolicy ? BehaviorStageEnum.BEHAVIOR_GRANT : BehaviorStageEnum.BEHAVIOR_FRONT;

		DetectResult detectResult = null;
		//授权前行为
		if (hasPrivacyPolicy) {

			List<ActionAnalyse> actionAnalyses = new ArrayList<>();
			boolean scene1 = true; //场景1
			boolean scene2 = true; //场景2

			//场景1 有隐私政策   隐私政策文本中没有行为关键词
			List<ActionAnalyse> actionAnalyses1 = this.filterAndCountActionByStageAndExe(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_FRONT, ExecutorTypeEnum.SDK);
			//筛选出不合规的行为
			actionAnalyses1 = checkNotComplianceActionExistInFragment(customDetectInfo, getBaseTargetPrivacyPolicyText(commonDetectInfo, customDetectInfo), actionAnalyses1);
			actionAnalyses.addAll(actionAnalyses1);
			// 存在隐私政策，判断增强
			boolean notExisWords = buildResultByActionExistWords(commonDetectInfo, customDetectInfo, actionAnalyses1);

			if(actionAnalyses1!= null && actionAnalyses1.size()>0 && notExisWords==false) {
				scene1 = false;
			}

			//场景2 授权前行为-隐私政策文本中行为有关键词
			List<ActionAnalyse> actionAnalyses2 = this.filterAndCountActionByStageAndExe(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GRANT, ExecutorTypeEnum.SDK);
			if(actionAnalyses2!= null && actionAnalyses2.size()>0){
				actionAnalyses.addAll(actionAnalyses2);
				scene2 = false;
			}

			detectResult= buildBaseResultByActionAnalyse(commonDetectInfo, customDetectInfo, actionAnalyses);
			buildResultPrivacyPolicyFragmentText(commonDetectInfo, customDetectInfo, actionAnalyses, detectResult);

			detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
			if(scene1== false || scene2==false) {
				detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
			}
		}else {
			List<ActionAnalyse> actionAnalyses = this.filterAndCountActionByStageAndExe(commonDetectInfo, customDetectInfo, behaviorStage, ExecutorTypeEnum.SDK);
			detectResult= buildBaseResultByActionAnalyse(commonDetectInfo, customDetectInfo, actionAnalyses);
		}
		detectResult.setPrivacyPolicyFragment(null);
		return detectResult;
	}
}
