package cn.ijiami.detection.miit.point.android.law164;

import java.util.List;

import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.helper.PermissionNameHelper;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.kit.MiitActionKit;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;

/**
 * APP虽然有向用户明示并经用户同意环节，但频繁自启动或关联启动发生在用户同意前。
 * <p>
 * <blockquote><pre>
 * 1、隐私政策截图
 * 2、共XXX次（自启动或关联启动的行为）
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 「授权前行为」
 *
 * 1、判断有隐私政策
 * 2、静默安装前，有启动Activity、启动Service行为，根据行为数据的触发时间，按秒统计触发频次，超过1次则为不合规
 *
 * 涉及行为：21004L,21005L
 * </pre></blockquote>
 *
 * <AUTHOR>
 * @date 2020/12/22 17:42
 **/
@EnableDetectPoint
public class DetectPoint20202 extends AbstractDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
//        return buildNonInvolved(commonDetectInfo, customDetectInfo);
        return oldDetect(commonDetectInfo, customDetectInfo);
    }

    private DetectResult oldDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        if (commonDetectInfo.nonHasPrivacyPolicy()) {
            DetectResult detectResult = this.buildNonInvolved(commonDetectInfo, customDetectInfo);
            return detectResult;
        }
//        List<ActionAnalyse> nameFilterAfter = getNameFilterAfter(commonDetectInfo, customDetectInfo);
        //授权前阶段判断是否存在自启动
        List<ActionAnalyse> nameFilterAfter = this.filterAndCountActionByStage(commonDetectInfo, customDetectInfo,  BehaviorStageEnum.BEHAVIOR_GRANT );
        DetectResult detectResult = buildBaseResultByActionAnalyse(commonDetectInfo, customDetectInfo, nameFilterAfter);
        // 隐私政策截图截图地址
        detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
        detectResult.setPrivacyPolicyFragment(null);
        // 统计超过每秒1次的频率的数据
        long count = nameFilterAfter.stream().filter(a -> a.getFrequency() >= 1).count();
        // 扩展判断
        long actionCount = nameFilterAfter.stream().filter(this::isAutoBoot).count();


        boolean isSell = false;     //自启动
        boolean isrelation = false; //关联

        if(nameFilterAfter != null && nameFilterAfter.size()>0) {
            for (ActionAnalyse actionAnalyse : nameFilterAfter) {
                if (MiitActionKit.APP_OWN_START.equals(actionAnalyse.getActionName())) {
                    isSell = true;
                }
                if ( MiitActionKit.APP_ASSOCIATE_START.equals(actionAnalyse.getActionName())) {
                    isrelation = true;
                }
            }
        }

        //没有授权同意，如果授权前阶段存在自启动就违规
        detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        if(nameFilterAfter==null || nameFilterAfter.size()==0) {
            return detectResult;
        }

//        String privacyPolicyText = commonDetectInfo.getPrivacyPolicyContent();
//        //自启动
//        boolean isSellExist = MiitWordKit.checkTextMeetTheRegex(privacyPolicyText, PermissionNameHelper.sellFilterStartUp);
//        //关联启动
//        boolean isExist = MiitWordKit.checkTextMeetTheRegex(privacyPolicyText, PermissionNameHelper.filterStartUp);
//        //行为有自启动和关联启动情况 隐私文本就要包含 “自启动”、“关联启动”
//        if(isSell ==true && isrelation==true) {
//            if(isSellExist==false && isExist==false && count > 0 && actionCount>0) {
//                detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
//            }
//            return detectResult;
//        }
//        //行为有自启情况 隐私文本就要包含 “自启动”
//        if(isSell==true && isrelation==false) {
//            if(isSellExist==false && count > 0 && actionCount>0) {
//                detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
//            }
//            return detectResult;
//        }
//        //行为有关联启动情况 隐私文本就要包含 “关联启动”
//        if(isSell==false && isrelation==true) {
//            if(isExist==false && count > 0 && actionCount>0) {
//                detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
//            }
//            return detectResult;
//        }
        
        if(count > 0 && actionCount>0) {
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
        }
        return detectResult;
    }

    protected boolean isAutoBoot(ActionAnalyse analyse) {
        return analyse.getActionId() == 32001;
    }

}
