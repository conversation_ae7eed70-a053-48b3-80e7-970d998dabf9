package cn.ijiami.detection.miit.point.android.law164;

import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * APP未见向用户明示SDK的收集使用规则，未经用户同意，SDK存在每30s读取一次位置信息，非服务所必需且无合理应用场景，超出实现产品或服务的业务功能所必需的最低频率。
 * <p>
 * <blockquote><pre>
 * 1、无隐私政策
 * 2、共XXX次（地理位置行为）
 * 触发时间 SDK 主体名称 个人信息行为名称 频次；
 * 触发时间 SDK 主体名称 个人信息行为名称 频次；
 * 「前台行为」
 *
 * 1、隐私政策截图
 * 2、共XXX次（地理位置行为）
 * 触发时间 SDK 主体名称 个人信息行为名称 频次；
 * 触发时间 SDK 主体名称 个人信息行为名称 频次；
 * 「授权前行为」
 *
 * 分两种情况：
 * 一、无隐私政策
 * 1、判断无隐私政策
 * 2、判断自动化遍历状态下，SDK是否有触发获取位置信息的行为，如果无，则合规，如果有，则再进一步根据触发时间戳判断触发的时间间隔是否等于、小于30s,如果是，则违规，否则合规
 * 二、有隐私政策
 * 1、判断有隐私政策
 * 2、判断静默安装状态下，SDK是否有触发获取位置信息的行为，如果无，则合规，如果有，且根据触发时间戳判断触发的时间间隔等于、小于30s，再根据关键词匹配隐私政策中是否有允许SDK收集通讯录、短信、通话记录、相机等信息的行为的说明，如有，则合规，无则违规
 *
 * 涉及行为：12001L, 12002L, 12003L, 12004L, 12005L, 12006L
 *
 * </pre></blockquote>
 *
 *
 * 场景一：修改
 * 1、没有有隐私政策
 * 2、某一类SDK行为存在固定频率触发（只要存在3次以上即存在风险，时间间隔中间存在其他行为或者其他次数，都不管）
 * 例如：固定3秒触发获取mac地址行为
 * 【精准到秒即可】
 * 【授权前行为】【前台行为】【后台行为】
 *
 * 场景二：修改
 * 1、有隐私政策
 * 2、有关键词
 * 2、某一类SDK行为存在固定频率触发（只要存在3次以上即存在风险，时间间隔中间存在其他行为或者其他次数，都不管）
 * 例如：固定3秒触发获取mac地址行为
 * 【精准到秒即可】
 * 【授权前行为】【后台行为】
 *
 * 场景三：修改
 * 1、有隐私政策
 * 2、没有关键词
 * 2、某一类SDK行为存在固定频率触发（只要存在3次以上即存在风险，时间间隔中间存在其他行为或者其他次数，都不管）
 * 例如：固定3秒触发获取mac地址行为
 * 【精准到秒即可】
 * 【授权前行为】【前台行为】【后台行为】
 *
 * <AUTHOR>
 * @date 2020/12/22 17:42
 **/
@EnableDetectPoint
public class DetectPoint10204 extends AbstractDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        if (!commonDetectInfo.isHasPrivacyPolicy()) {
            return nonePrivacyPolicyContentScene(commonDetectInfo, customDetectInfo);
        }else {
            return havePrivacyPolicyContentScene(commonDetectInfo, customDetectInfo);
        }
    }

    /**
     * 无隐私的场景
     * @param commonDetectInfo
     * @param customDetectInfo
     * @return
     */
    protected DetectResult nonePrivacyPolicyContentScene(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = getBaseDetectResult(commonDetectInfo, customDetectInfo);
        //授权前行为
        List<ActionAnalyse> grantAnalyses = filterGteCountInvolvedActionByExecutorType(commonDetectInfo, customDetectInfo,
                BehaviorStageEnum.BEHAVIOR_GRANT, ExecutorTypeEnum.SDK, PinfoConstant.MINI_COUNT);
        List<ActionAnalyse> analysesList = new ArrayList<>(grantAnalyses);
        // 前台行为
        List<ActionAnalyse> frontAnalyses = filterGteCountInvolvedActionByExecutorType(commonDetectInfo, customDetectInfo,
                BehaviorStageEnum.BEHAVIOR_FRONT, ExecutorTypeEnum.SDK, PinfoConstant.MINI_COUNT);
        analysesList.addAll(frontAnalyses);
        //后台行为
        List<ActionAnalyse> groundAnalyses = filterGteCountInvolvedActionByExecutorType(commonDetectInfo, customDetectInfo,
                BehaviorStageEnum.BEHAVIOR_GROUND, ExecutorTypeEnum.SDK, PinfoConstant.MINI_COUNT);
        analysesList.addAll(groundAnalyses);

        detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
        detectResult.setAnalysisResult(analysesList);
        detectResult.setComplianceStatus(analysesList.isEmpty() ? MiitDetectStatusEnum.NON_INVOLVED : MiitDetectStatusEnum.NON_COMPLIANCE);
        return detectResult;
    }

    protected DetectResult havePrivacyPolicyContentScene(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        //场景2 有隐私政策 有关键词  固定频率
        //授权前行为
        List<ActionAnalyse> grantAnalyses = filterGteCountInvolvedActionByExecutorType(commonDetectInfo, customDetectInfo,
                BehaviorStageEnum.BEHAVIOR_GRANT, ExecutorTypeEnum.SDK, PinfoConstant.MINI_COUNT);
        List<ActionAnalyse> analysesList = new ArrayList<>(grantAnalyses);
        //后台行为
        List<ActionAnalyse> groundAnalyses = filterGteCountInvolvedActionByExecutorType(commonDetectInfo, customDetectInfo,
                BehaviorStageEnum.BEHAVIOR_GROUND, ExecutorTypeEnum.SDK, PinfoConstant.MINI_COUNT);
        //筛选出不合规的行为
        List<ActionAnalyse> nonComplianceGroundAnalyses = checkNotComplianceActionExistInFragment(customDetectInfo, getBaseTargetPrivacyPolicyText(commonDetectInfo, customDetectInfo), groundAnalyses);
        analysesList.addAll(nonComplianceGroundAnalyses);
        boolean isExistWords = buildResultByActionExistWords(commonDetectInfo, customDetectInfo, nonComplianceGroundAnalyses);
        //不合规情况，增强判断关键词
        boolean scene2NonCompliance = CollectionUtils.isNotEmpty(grantAnalyses) || (CollectionUtils.isNotEmpty(analysesList) && isExistWords);
        //场景3  有隐私政策 没关键词  固定频率
        // 前台行为
        List<ActionAnalyse> frontAnalyses = filterGteCountInvolvedActionByExecutorType(commonDetectInfo, customDetectInfo,
                BehaviorStageEnum.BEHAVIOR_FRONT, ExecutorTypeEnum.SDK, PinfoConstant.MINI_COUNT);
        // 筛选出不合规的行为
        List<ActionAnalyse> nonComplianceFrontAnalyses = checkNotComplianceActionExistInFragment(customDetectInfo, getBaseTargetPrivacyPolicyText(commonDetectInfo, customDetectInfo), frontAnalyses);
        analysesList.addAll(nonComplianceFrontAnalyses);
        // 有固定频率后看看是否有关键词
        boolean notExistWords = !buildResultByActionExistWords(commonDetectInfo, customDetectInfo, nonComplianceFrontAnalyses);
        boolean scene3NonCompliance = CollectionUtils.isNotEmpty(nonComplianceFrontAnalyses) && notExistWords;
        DetectResult detectResult = getBaseDetectResult(commonDetectInfo, customDetectInfo);
        // 隐私政策截图截图地址
        detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
        detectResult.setAnalysisResult(analysesList);
        detectResult.setComplianceStatus((scene2NonCompliance || scene3NonCompliance) ? MiitDetectStatusEnum.NON_COMPLIANCE : MiitDetectStatusEnum.NON_INVOLVED);
        return detectResult;
    }
}