package cn.ijiami.detection.miit.point.harmony.law41391;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.point.android.law41391.DetectPoint350601;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350601.java
 * @Description
 * App是否存在违规嵌入使用第三方SDK情况
 * 1. 是否明确同三方SDK的责任与义务
 * 判断规则：
 * c)应与第三方SDK明确双方的个人信息处理规则和保护责任，包括：
 * 1)SDK收集个人信息的目的、方式、范围；
 * 2)SDK申请的系统权限和申请目的；
 * 3)SDK收集个人信息的保存期限、停止嵌入后的个人信息处理方式；
 * 4)个人信息安全责任和保护措施；
 * 5)SDK是否存在热更新机制；
 * 6)SDK是否存在自启动、关联启动；
 * 7)SDK收集个人信息是否涉及向境外提供；
 * 8)SDK协助App响应用户个人信息权利请求的措施。
 * 发现风险：
 * 【无隐私政策】
 * 1、未检测到隐私政策
 * 【有隐私政策】
 * 1、未明确同三方SDK的责任与义务，隐私中政策中未对【SDK名称列表】进行说明。
 */
@EnableDetectPoint
public class DetectPoint40350601 extends DetectPoint350601 {

}
