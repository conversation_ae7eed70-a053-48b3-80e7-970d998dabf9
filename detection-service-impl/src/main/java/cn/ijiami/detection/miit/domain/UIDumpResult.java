package cn.ijiami.detection.miit.domain;

import cn.ijiami.detection.DTO.ocr.RecognizeData;
import cn.ijiami.detection.helper.bean.PrivacyPolicyTextInfo;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * uidump.xml解析
 */
public class UIDumpResult {

    // 界面类型(申请隐私政策界面,隐私策略详情)
    private int uiType;

    // 完整文本
    private String fullText;

    /**
     * ios界面记录携带的ocr数据
     */
    private List<RecognizeData> ocrList;
    private String ocrText;

    /**
     * ios界面记录携带的html数据
     */
    private List<PrivacyPolicyTextInfo> policyTextInfoList;

    // 包含控件
    private List<UIComponent> uiComponentList;

    // 获取权限信息
    private String permissionText;

    // 屏幕大小
    private int screenWidth;
    private int screenHeight;

    public int getUiType() {
        return uiType;
    }

    public void setUiType(int uiType) {
        this.uiType = uiType;
    }

    public String getFullText() {
        return fullText;
    }

    public void setFullText(String fullText) {
        this.fullText = fullText;
    }

    public List<UIComponent> getUiComponentList() {
        return uiComponentList;
    }

    public void setUiComponentList(List<UIComponent> uiComponentList) {
        this.uiComponentList = uiComponentList;
    }


    public String getPermissionText() {
        if (StringUtils.isNotBlank(this.getFullText()) && StringUtils.contains(this.getFullText(), "？")) {
            return this.getFullText().substring(0, this.getFullText().indexOf("？"));
        }
        return this.getFullText();
    }

    public void setPermissionText(String permissionText) {
        this.permissionText = permissionText;
    }

    public int getScreenWidth() {
        return screenWidth;
    }

    public void setScreenWidth(int screenWidth) {
        this.screenWidth = screenWidth;
    }

    public int getScreenHeight() {
        return screenHeight;
    }

    public void setScreenHeight(int screenHeight) {
        this.screenHeight = screenHeight;
    }

    public List<RecognizeData> getOcrList() {
        return ocrList;
    }

    public void setOcrList(List<RecognizeData> ocrList) {
        this.ocrList = ocrList;
    }

    public String getOcrText() {
        return ocrText;
    }

    public void setOcrText(String ocrText) {
        this.ocrText = ocrText;
    }

    public List<PrivacyPolicyTextInfo> getPolicyTextInfoList() {
        return policyTextInfoList;
    }

    public void setPolicyTextInfoList(List<PrivacyPolicyTextInfo> policyTextInfoList) {
        this.policyTextInfoList = policyTextInfoList;
    }
}
