package cn.ijiami.detection.analyzer.parser;

import cn.hutool.core.io.FileUtil;
import cn.ijiami.detection.VO.CheckList;
import cn.ijiami.detection.VO.detection.privacy.dto.AppletActionBO;
import cn.ijiami.detection.VO.detection.privacy.dto.AppletInputParamDTO;
import cn.ijiami.detection.analyzer.CaptureHostIpAction;
import cn.ijiami.detection.analyzer.bo.CaptureInfoBO;
import cn.ijiami.detection.analyzer.bo.HarmonyCaptureInfoBO;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.*;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.BooleanEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.helper.SensitiveWordsHelper;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.FileInputStream;
import java.io.InputStream;
import java.net.InetAddress;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.UnknownHostException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA_ANDROID_OR_APPLET_LOG_BEHAVIOR_STAGE;
import static cn.ijiami.detection.constant.PinfoConstant.DETAILS_EMPTY;
import static cn.ijiami.detection.utils.AppletActionUtils.*;
import static cn.ijiami.detection.utils.AppletActionUtils.getMethod;
import static cn.ijiami.detection.utils.CommonUtil.*;
import static cn.ijiami.detection.utils.SensitiveUtils.isPlaintextTransmission;

/**
 * <AUTHOR>
 * @version 1.0.1
 * @ClassName HarmonyCaptureHostIpAction.java
 * @Description 鸿蒙网络行为解析
 * @createTime 2025年03月03日 17:57:00
 */
@Slf4j
@Component
public class HarmonyCaptureHostIpAction {

    public static final String PARAM_METHOD = "Method";
    public static final String PARAM_URL = "Url";
    public static final String PARAM_HOST = "Host";
    public static final String PARAM_QUEST_TIME = "QuestTime";
    public static final String PARAM_CONTENTS = "Contents";
    public static final String PARAM_HEADERS = "Headers";

    @Autowired
    private IpUtil ipUtil;

    public List<HarmonyCaptureInfoBO> analyzeCaptureInfo(String filePath, List<TSdkLibrary> sdks, String appName, String appPackageName) {
        List<HarmonyCaptureInfoBO> result = new ArrayList<>();
        if (!FileUtil.exist(filePath)) {
            log.info("没有网络数据不存在 {}", filePath);
            return result;
        }
        try {
            //得到文件流
            InputStream in = Files.newInputStream(Paths.get(filePath));
            Workbook workbook = new XSSFWorkbook(in);
            //获取第一张表
            Sheet sheet = workbook.getSheetAt(0);
            Row title = sheet.getRow(0);
            int methodIdx = findIndexByName(title, PARAM_METHOD);
            int urlIdx = findIndexByName(title, PARAM_URL);
            int hostIdx = findIndexByName(title, PARAM_HOST);
            int actionTimeIdx = findIndexByName(title, PARAM_QUEST_TIME);
            int contentIdx = findIndexByName(title, PARAM_CONTENTS);
            int headerIdx = findIndexByName(title, PARAM_HEADERS);
            for (int i = 1; i < sheet.getLastRowNum() + 1; i++) {
                Row row = sheet.getRow(i);
                String dateStr = getCellAsStringIndex(row, actionTimeIdx);
                if (DETAILS_EMPTY.equals(dateStr)) {
                    continue;
                }
                HarmonyCaptureInfoBO captureInfoBO = new HarmonyCaptureInfoBO();
                captureInfoBO.setMethod(getCellAsStringIndex(row, methodIdx));
                captureInfoBO.setUrl(getCellAsStringIndex(row, urlIdx));
                captureInfoBO.setHost(getCellAsStringIndex(row, hostIdx));
                captureInfoBO.setActionTime(new Date(Long.parseLong(dateStr)));
                captureInfoBO.setContent(getCellAsStringIndex(row, contentIdx));
                String header = getCellAsStringIndex(row, headerIdx);
                captureInfoBO.setHeader(header);
                captureInfoBO.setAppName(appName);
                captureInfoBO.setAppPackageName(appPackageName);
                captureInfoBO.setCookie(extractCookie(header));
                try {
                    URI url = new URI(captureInfoBO.getUrl());
                    captureInfoBO.setPort(url.getPort() < 0 ? PinfoConstant.DETAILS_EMPTY : String.valueOf(url.getPort()));
                    captureInfoBO.setAddress(StringUtils.isEmpty(url.getPath()) ? PinfoConstant.DETAILS_EMPTY : url.getPath());
                    captureInfoBO.setProtocol(StringUtils.isEmpty(url.getScheme()) ? PinfoConstant.DETAILS_EMPTY : url.getScheme());
                    try {
                        captureInfoBO.setIp(InetAddress.getByName(url.getHost()).getHostAddress());
                    } catch (UnknownHostException e) {
                        captureInfoBO.setIp(PinfoConstant.DETAILS_EMPTY);
                    }
                } catch (URISyntaxException e) {
                    log.info("解析url失败 url={} msg={}", captureInfoBO.getUrl(), e.getMessage());
                }
                result.add(captureInfoBO);
            }
        } catch (Exception e) {
            log.error("解析网络行为文件错误", e);
        }
        log.info("分析网络数据{}条记录", result.size());
        return Collections.emptyList();
    }

    private TPrivacyOutsideAddress build(Long taskId, BehaviorStageEnum behaviorStage,
                                                HarmonyCaptureInfoBO captureInfoBO) {
        TPrivacyOutsideAddress outsideAddress = new TPrivacyOutsideAddress();
        outsideAddress.setTaskId(taskId);
        outsideAddress.setHost(captureInfoBO.getHost());
        //新增cookie标识，以便前台展示
        outsideAddress.setCookieMark(BooleanEnum.FALSE.value);
        //url地址
        outsideAddress.setUrl(optDetails(captureInfoBO.getUrl()));
        // 网络访问数据类型
        outsideAddress.setRequestMethod(captureInfoBO.getMethod());
        // 请求内容
        outsideAddress.setDetailsData(optDetails(captureInfoBO.getContent()));
        outsideAddress.setStackInfo(DETAILS_EMPTY);
        // 响应内容
        outsideAddress.setResponseData(DETAILS_EMPTY);
        outsideAddress.setActionTime(captureInfoBO.getActionTime());
        outsideAddress.setBehaviorStage(behaviorStage);
        outsideAddress.setStrTime(captureInfoBO.getActionTime());
        outsideAddress.setCounter(1);
        outsideAddress.setExecutorType(ExecutorTypeEnum.APP.getValue());
        outsideAddress.setExecutor(captureInfoBO.getAppName());
        outsideAddress.setPackageName(captureInfoBO.getAppPackageName());

        outsideAddress.setCookie(captureInfoBO.getCookie());
        outsideAddress.setCookieMark(cookieMark(outsideAddress));
        outsideAddress.setAddress(ipUtil.getAddressFromIP(captureInfoBO.getIp()));
        //协议类型
        outsideAddress.setProtocol(captureInfoBO.getProtocol());
        outsideAddress.setPort(captureInfoBO.getPort());
        outsideAddress.setIp(captureInfoBO.getIp());
        return outsideAddress;
    }

    private static String getCellAsStringIndex(Row row, int index) {
        return index >= 0 ? getCellAsString(row.getCell(index)) : DETAILS_EMPTY;
    }

    private static int findIndexByName(Row row, String name) {
        short minColIx = row.getFirstCellNum();
        short maxColIx = row.getLastCellNum();
        for(short colIx=minColIx; colIx<maxColIx; colIx++) {
            Cell cell = row.getCell(colIx);
            if(cell == null) {
                continue;
            }
            if (getCellAsString(cell).equalsIgnoreCase(name)) {
                return colIx;
            }
        }
        return -1;
    }

    public List<TPrivacyOutsideAddress> getPrivacyOutsideAddressResult(List<HarmonyCaptureInfoBO> captureInfoList, Long taskId,
                                                                       BehaviorStageEnum behaviorStage) {
        return captureInfoList.stream().map(captureInfo ->  build(taskId, behaviorStage, captureInfo)).collect(Collectors.toList());
    }

    public List<TPrivacySensitiveWord> getPrivacySensitiveWordResult(List<HarmonyCaptureInfoBO> captureInfoList, List<TSensitiveWord> sensitiveWords, Long taskId,
                                                                     BehaviorStageEnum behaviorStage) {
        List<TPrivacySensitiveWord> result = new ArrayList<>();
        sensitiveWords.forEach(sensitiveWord -> {
            // 无名称，无正则视为无效
            for (HarmonyCaptureInfoBO captureInfoBO:captureInfoList) {
                if(StringUtils.isNotBlank(sensitiveWord.getRegex())){
                    result.addAll(matchCaptureInfoByRegex(behaviorStage, captureInfoBO, sensitiveWord, taskId));
                }else if(org.apache.commons.lang.StringUtils.isNotBlank(sensitiveWord.getName())){
                    result.addAll(matchCaptureInfoByWord(behaviorStage, captureInfoBO, sensitiveWord, taskId));
                }
            }
        });
        log.info("分析传输个人信息数据{}条记录", result.size());
        return Collections.emptyList();
    }

    /**
     * 正则匹配传输个人信息数据
     *
     * @param data 捕获数据
     * @param sensitive   敏感信息
     * @return
     */
    private List<TPrivacySensitiveWord> matchCaptureInfoByRegex(BehaviorStageEnum behaviorStageEnum, HarmonyCaptureInfoBO data,
                                                                TSensitiveWord sensitive, Long taskId) {
        List<TPrivacySensitiveWord> sensitiveWordList = new ArrayList<>();
        // 判断请求参数中是否携带个人信息数据
        if (hasParams(data.getContent())) {
            sensitiveWordList.addAll(matchByRegexFromCookieOrParams(behaviorStageEnum, data, sensitive, taskId, false));
        }
        // 判断cookie中是否携带个人信息数据
        if (hasCookie(data.getCookie())) {
            sensitiveWordList.addAll(matchByRegexFromCookieOrParams(behaviorStageEnum, data, sensitive, taskId, true));
        }
        return sensitiveWordList;
    }

    /**
     * 关键词匹配传输个人信息数据
     *
     * @param data 捕获数据
     * @param sensitive   敏感信息
     * @return
     */
    private List<TPrivacySensitiveWord> matchCaptureInfoByWord(BehaviorStageEnum behaviorStageEnum, HarmonyCaptureInfoBO data,
                                                               TSensitiveWord sensitive, Long taskId) {
        List<TPrivacySensitiveWord> sensitiveWordList = new ArrayList<>();
        // 判断请求参数中是否携带个人信息数据
        if (hasParams(data.getContent())) {
            sensitiveWordList.addAll(matchByWordFromCookieOrParams(behaviorStageEnum, data, sensitive, taskId, false));
        }
        // 判断cookie中是否携带个人信息数据
        if (hasCookie(data.getHeader())) {
            sensitiveWordList.addAll(matchByWordFromCookieOrParams(behaviorStageEnum, data, sensitive, taskId, true));
        }
        return sensitiveWordList;
    }

    private boolean hasParams(String inputParam) {
        return StringUtils.isNotBlank(inputParam);
    }

    private boolean hasCookie(String cookie) {
        return StringUtils.isNotBlank(cookie);
    }

    private List<TPrivacySensitiveWord> matchByRegexFromCookieOrParams(BehaviorStageEnum behaviorStageEnum, HarmonyCaptureInfoBO data,
                                                                       TSensitiveWord sensitive, Long taskId, boolean cookie) {
        List<TPrivacySensitiveWord> sensitiveWordList = new ArrayList<>();
        String from;
        if (cookie) {
            from = optDetails(data.getCookie());
        } else {
            from = optDetails(data.getContent());
        }
        Pattern pattern = Pattern.compile(sensitive.getRegex());
        Matcher matcher = pattern.matcher(from);
        // 获取正则匹配到的内容
        while (matcher.find()) {
            boolean isInvalidInfo = SensitiveWordsHelper.isInvalidInfo(matcher, from, sensitive, true);
            if (isInvalidInfo) {
                continue;
            }
            String sensitiveWord = matcher.group();
            if (StringUtils.isBlank(sensitiveWord)) {
                continue;
            }
            String code = SensitiveWordsHelper.getMatchString(matcher, from);
            String method = data.getMethod();
            // 组装实体
            TPrivacySensitiveWord bean = buildCaptureInfoToSensitiveWord(behaviorStageEnum, data, sensitive, code, method, sensitiveWord, taskId, cookie);
            // 存放结果
            sensitiveWordList.add(bean);
        }
        return sensitiveWordList;
    }

    /**
     * 从capture info中组装敏感词
     *
     * @param data  网络抓包数据
     * @param sensitive    关键词
     * @param code 标记源
     * @param method       方法类型
     * @param keyword      抓取关键词
     * @return
     */
    private TPrivacySensitiveWord buildCaptureInfoToSensitiveWord(BehaviorStageEnum behaviorStageEnum, HarmonyCaptureInfoBO data,
                                                                  TSensitiveWord sensitive, String code, String method, String keyword, Long taskId, boolean cookie) {
        //v2.5版本只要是string类型为空则按照--入库
        // 组装实体
        TPrivacySensitiveWord bean = new TPrivacySensitiveWord();
        // 存放堆栈数据
        bean.setStackInfo(PinfoConstant.DETAILS_EMPTY);
        bean.setDetailsData(optDetails(data.getContent()));
        bean.setResponseData(PinfoConstant.DETAILS_EMPTY);
        bean.setActionTime(data.getActionTime());
        // 任务信息
        bean.setTaskId(taskId);
        bean.setBehaviorStage(behaviorStageEnum);
        bean.setExecutorType(ExecutorTypeEnum.APP.getValue());
        bean.setExecutor(data.getAppName());
        bean.setSdkName(StringUtils.EMPTY);
        // 存放系解析出的json数据
        bean.setSdkIds(StringUtils.EMPTY);
        // 存放敏感词数据
        bean.setTypeId(sensitive.getTypeId());
        bean.setName(optDetails(sensitive.getName()));
        bean.setSensitiveWord(optDetails(keyword));
        bean.setRiskLevel(sensitive.getRiskLevel());
        bean.setSuggestion(sensitive.getSuggestion());
        // 存放解析出的数据
        bean.setMethod(optDetails(method));
        bean.setUrl(data.getUrl());
        bean.setHost(data.getHost());
        bean.setPort(data.getPort());
        bean.setAddress(data.getAddress());
        bean.setProtocol(data.getProtocol());
        bean.setIp(data.getIp());
        bean.setAttributively(optDetails(ipUtil.getAddress(data.getIp())));
        bean.setCode(optDetails(code));
        bean.setCookie(data.getCookie());
        //增加cookie标记，以便前端展示cookie标签
        bean.setCookieMark(StringUtils.isNotBlank(bean.getCookie()) ? BooleanEnum.TRUE.value : null);
        bean.setCookieWord(cookie);
        bean.setPackageName(data.getAppPackageName());
        bean.setPlaintextTransmission(isPlaintextTransmission(sensitive).value);
        return bean;
    }

    /**
     * 从输入的文本中提取Cookie字段的值
     *
     * @param headerText 输入的HTTP头部信息文本
     * @return 如果存在Cookie字段，则返回其值；否则返回null
     */
    public static String extractCookie(String headerText) {
        if (headerText == null || headerText.isEmpty()) {
            return StringUtils.EMPTY; // 如果输入为空，直接返回空字符
        }

        // 正则表达式匹配Cookie字段
        String regex = "\\(b'Cookie',\\sb'([^']*)'\\)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(headerText);

        // 如果找到匹配项，返回Cookie的值
        if (matcher.find()) {
            return matcher.group(1); // 提取第一个捕获组的内容
        }

        return StringUtils.EMPTY; // 如果没有找到Cookie字段，返回空字符
    }

    private List<TPrivacySensitiveWord> matchByWordFromCookieOrParams(BehaviorStageEnum behaviorStageEnum, HarmonyCaptureInfoBO data,
                                                                      TSensitiveWord sensitive, Long taskId, boolean cookie) {
        String sensitiveWord = sensitive.getSensitiveWords();
        if (StringUtils.isBlank(sensitiveWord)) {
            return new ArrayList<>();
        }
        List<TPrivacySensitiveWord> sensitiveWordList = new ArrayList<>();
        for (SensitiveWordsHelper.SensitiveInfo word : SensitiveWordsHelper.find(cookie ? data.getCookie() : optDetails(data.getContent()),
                StringUtils.EMPTY, sensitive.getSensitiveWords(),false)) {
            // 组装实体
            TPrivacySensitiveWord bean = buildCaptureInfoToSensitiveWord(behaviorStageEnum, data, sensitive,
                    word.getCode(), optDetails(data.getContent()), word.getWord(), taskId, cookie);
            // 存放结果
            sensitiveWordList.add(bean);
        }
        return sensitiveWordList;
    }

}
