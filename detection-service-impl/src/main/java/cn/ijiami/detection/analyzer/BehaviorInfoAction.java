package cn.ijiami.detection.analyzer;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;

import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.analyzer.bo.ActionStackBO;
import cn.ijiami.detection.analyzer.helper.BehaviorActionConvertHelper;
import cn.ijiami.detection.analyzer.helper.FilteractionRegexHelper;
import cn.ijiami.detection.analyzer.helper.SpecialActionTypeConvertHelper;
import cn.ijiami.detection.analyzer.parser.BehaviorInfoParser;
import cn.ijiami.detection.analyzer.parser.IDetectDataParser;
import cn.ijiami.detection.config.IjiamiCommonProperties;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.dao.ActionFilterGroupDao;
import cn.ijiami.detection.entity.TActionFilterGroupRegex;
import cn.ijiami.detection.entity.TActionFilterRegex;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TSdkLibrary;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.entity.interfaces.RawExecutor;
import cn.ijiami.detection.server.client.base.enums.ActionFilterTypeEnum;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.server.client.base.enums.TerminalTypeEnum;
import cn.ijiami.detection.helper.PackageNameExtractHelper;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.service.api.CacheService;
import cn.ijiami.detection.utils.SdkUtils;

/**
 * 堆栈数据处理
 *
 * <AUTHOR>
 * @date 2020-09-21 10:24
 */
@Component
public class BehaviorInfoAction {

    private static Logger logger = LoggerFactory.getLogger(BehaviorInfoAction.class);

    @Autowired
    private BehaviorActionConvertHelper convertHelper;
    @Autowired
    private IjiamiCommonProperties commonProperties;

    @Autowired
    private TTaskMapper taskMapper;

    @Autowired
    private ActionFilterGroupDao actionFilterGroupDao;

    @Autowired
    private CacheService cacheService;

    /**
     * 自启动行为id
     */
    private static final long ACTION_ID_BOOT = 32001;

    /**
     * 分析新为数据，入库堆栈信息【behave_info】
     *
     * @param filePath      文件路径
     * @param taskDetailVO  任务详情
     * @param behaviorStage 行为阶段
     * @param sdks          sdks数据
     * @return 行为数据
     */
    public List<TPrivacyActionNougat> analyzeBehaviorInfo(String filePath, TaskDetailVO taskDetailVO, BehaviorStageEnum behaviorStage, List<TSdkLibrary> sdks) {
        // 获取文件中的堆栈数据
        IDetectDataParser parser = new BehaviorInfoParser();
        List<ActionStackBO> stacks = parser.parser(filePath, taskDetailVO.getApk_name());
        if (CollectionUtils.isEmpty(stacks)) {
            logger.info("BehaviorInfoAction - 行为数据初步解析，无数据");
            return new ArrayList<>();
        }

        logger.info("BehaviorInfoAction -  行为数据初步解析: {} 条", stacks.size());
        
        String sandboxPackageFilter = commonProperties.getProperty("sandbox.filter.packages");
    	if(StringUtils.isBlank(sandboxPackageFilter)) {
    		sandboxPackageFilter = SpecialActionTypeConvertHelper.WRITE_EXTERNAL_SYSTEM_APP_ACTIONIDS_REGEX;
    	}
        List<TActionFilterGroupRegex> groupRegexList = findActionFilterGroupRegexList(taskDetailVO.getTaskId());
        // 组装需求的数据
        String finalSandboxPackageFilter = sandboxPackageFilter;
        List<TPrivacyActionNougat> actionNougats = stacks.parallelStream()
                .map(stack -> buildBehaveInfoToActionNougat(taskDetailVO, behaviorStage, stack, sdks, groupRegexList))
                .filter(Objects::nonNull)
                .filter(action -> effectAction(action.getActionId(), action.getStackInfo(), action.getDetailsData(),
                        taskDetailVO.getApk_name(), taskDetailVO.getApk_package(), finalSandboxPackageFilter))
                .filter(action -> !filterActionGroupRegex(groupRegexList, action.getActionId(), action.getStackInfo(), action.getDetailsData()))
                .collect(Collectors.toList());
        logger.info("BehaviorInfoAction - 行为数据解析，获取到的符合要求的完整解析数据: {} 条", stacks.size());
        return actionNougats;
    }

    public static boolean effectAction(long actionId, String stackInfo, String details, String appName, String packageName, String sandboxFilterPackages) {
        // 获取应用列表的行为判断
        if (actionId == 28005 || actionId == 28006) {
            // 没有数据详情的不是有效行为
            if (StringUtils.isBlank(details) || StringUtils.equals(details, PinfoConstant.DETAILS_EMPTY)) {
                return false;
            }
            if (StringUtils.containsAny(stackInfo, PinfoConstant.ORIGINAL_WEB_VIEW_CLASS_NAME, PinfoConstant.CHROMIUM_WEB_VIEW_CLASS_NAME)) {
                return false;
            }
            
            // 排除app名字后，从应用列表中获取的应用超过1才是有效的获取应用列表行为
            long getAppCount = Arrays.stream(details.split("\n"))
                    .filter(s -> !s.equalsIgnoreCase(packageName))
                    .filter(s -> !s.equalsIgnoreCase(appName))
                    .filter(s -> !StringUtils.isBlank(s))
                    .filter(s -> !SpecialActionTypeConvertHelper.isMatcherContent(s, sandboxFilterPackages))
                    .count();
            return getAppCount >= 1;
        }
        if (actionId == 110048) {
            // ios关联启动，如果是参数里包含http或者app自己名字的，则是唤起APP的URL Scheme，需要排除掉。
            // 例如[, , https://special.ziroom.com/2018/tpl201810/index.html?id=22003, , , , ]
            return Arrays.stream(details.split(","))
                    .filter(s -> !StringUtils.isBlank(s))
                    .allMatch(s -> !s.startsWith("http") && !s.startsWith(appName));
        }
        // 根据产品要求，屏蔽掉获取运行中的进程行为
        if (actionId == 22002) {
            return false;
        }
        return true;
    }

    public List<TActionFilterGroupRegex> findActionFilterGroupRegexList(Long taskId) {
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (Objects.isNull(task)) {
            return Collections.emptyList();
        }
        return actionFilterGroupDao.findActionFilterGroupRegexList(task.getTaskId(), task.getTerminalType());
    }

    public static boolean filterActionGroupRegex(List<TActionFilterGroupRegex> regexList, Long actionId, String stackInfo, String detailsData) {
        for (TActionFilterGroupRegex regex : regexList) {
        	//指定监控函数
//        	if(regex.getActionType() != null && regex.getActionType() == ActionFilterTypeEnum.APPOINT) {
//        		//去掉过滤逻辑
//        		if (Objects.nonNull(regex.getActionId())) {
//                    if (regex.getActionId().equals(actionId)) {
//                    	// 如果包含指定监控的行为就不过滤
//                        return !SpecialActionTypeConvertHelper.isMatcherContent(stackInfo, regex.getActionFilterRegex());
//                    }
//                } else if (StringUtils.isNotBlank(regex.getActionFilterRegex())) {
//                	// 如果包含指定监控的行为就不过滤
//                    if (SpecialActionTypeConvertHelper.isMatcherContent(stackInfo, regex.getActionFilterRegex())) {
//                        return false;
//                    }
//                }
//        	}else {
//        		// 过滤函数
//        		if (Objects.nonNull(regex.getActionId())) {
//                    if (regex.getActionId().equals(actionId)) {
//                        return SpecialActionTypeConvertHelper.isMatcherContent(stackInfo, regex.getActionFilterRegex());
//                    }
//                } else if (StringUtils.isNotBlank(regex.getActionFilterRegex())) {
//                    if (SpecialActionTypeConvertHelper.isMatcherContent(stackInfo, regex.getActionFilterRegex())) {
//                        return true;
//                    }
//                }
//        	}
        	
        	if(regex.getActionType() != null && regex.getActionType() == ActionFilterTypeEnum.FILTER) {
        		if (Objects.nonNull(regex.getActionId())) {
                    if (regex.getActionId().equals(actionId)) {
                        return SpecialActionTypeConvertHelper.isMatcherContent(stackInfo, regex.getActionFilterRegex());
                    }
                } else if (StringUtils.isNotBlank(regex.getActionFilterRegex())) {
                    if (SpecialActionTypeConvertHelper.isMatcherContent(stackInfo, regex.getActionFilterRegex())) {
                        return true;
                    }
                }
        	}
        }
        return false;
    }
    
    public static boolean appointActionGroupRegex(List<TActionFilterGroupRegex> regexList, Long actionId, String stackInfo, String detailsData) {
        for (TActionFilterGroupRegex regex : regexList) {
        	//指定监控函数
        	if(regex.getActionType() == null ||  regex.getActionType() != ActionFilterTypeEnum.APPOINT) {
        		continue;
        	}
    		//去掉过滤逻辑
    		if (Objects.nonNull(regex.getActionId())) {
                if (regex.getActionId().equals(actionId)) {
                	// 如果包含指定监控的行为就不过滤
                    return SpecialActionTypeConvertHelper.isMatcherContent(stackInfo, regex.getActionFilterRegex());
                }
            } else if (StringUtils.isNotBlank(regex.getActionFilterRegex())) {
            	// 如果包含指定监控的行为就不过滤
                if (SpecialActionTypeConvertHelper.isMatcherContent(stackInfo, regex.getActionFilterRegex())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 构建堆栈信息【behave_info】
     *
     * @param taskDetailVO  任务详情
     * @param behaviorStage 行为阶段
     * @param stack         解析出的堆栈信息
     * @return 堆栈详细信息
     */
    private TPrivacyActionNougat buildBehaveInfoToActionNougat(TaskDetailVO taskDetailVO, BehaviorStageEnum behaviorStage, ActionStackBO stack, 
    		List<TSdkLibrary> sdks, List<TActionFilterGroupRegex> groupRegexList) {
        
    	TPrivacyActionNougat actionNougat = new TPrivacyActionNougat();
        actionNougat.setTaskId(taskDetailVO.getTaskId());
        // 设置行为阶段： 授权前行为、前台运行行为、后台运行行为
        actionNougat.setBehaviorStage(behaviorStage);
        // 转换actionId
        actionNougat.setActionId(convertHelper.convertActionId(stack.getActionId(), stack.getStackInfo(), stack.getDetailsData(), taskDetailVO.getApk_package()));
        String stackInfo = StringUtils.isEmpty(stack.getStackInfo()) ? PinfoConstant.DETAILS_EMPTY : stack.getStackInfo();
        String jniStackInfo = StringUtils.isEmpty(stack.getJniStackInfo()) ? StringUtils.EMPTY : stack.getJniStackInfo();
        String detailsData = StringUtils.isEmpty(stack.getDetailsData()) ? PinfoConstant.DETAILS_EMPTY : stack.getDetailsData();
        //v2.5版本只要是string类型的为空则按照--入库
        actionNougat.setStackInfo(stackInfo);
        actionNougat.setJniStackInfo(jniStackInfo);
        actionNougat.setDetailsData(detailsData);
        setExecutorInfo(actionNougat, detailsData, stackInfo, actionNougat.getActionId(), sdks, taskDetailVO.getApk_package(), taskDetailVO.getApk_name());
        if (Objects.nonNull(stack.getActionTime())) {
            actionNougat.setActionTime(stack.getActionTime());
            actionNougat.setActionTimeStamp(stack.getActionTime().getTime());
        }
        //20220809过滤传感器误报数据
    	String actionStackInfo = stack.getStackInfo();
    	
    	String filter = commonProperties.getProperty("filter.action.stackInfo");
    	if(StringUtils.isBlank(filter)) {
    		filter = SpecialActionTypeConvertHelper.FILTERPACKAGE;
    	}
    	
    	String platformmark = commonProperties.getProperty("ijiami.platformmark");
    	if(StringUtils.isNotBlank(platformmark) && platformmark.equals("demo1")) {
    		filter = SpecialActionTypeConvertHelper.FILTERPACKAGE_DEMO1;
    	}
    	
    	//判断是否有数据库配置制定的函数
    	if(!appointActionGroupRegex(groupRegexList, actionNougat.getActionId(), actionStackInfo, detailsData)){
    		//过滤小米行为
        	if(SpecialActionTypeConvertHelper.ACTIONID_XIAOMI.contains(actionNougat.getActionId())){
        		return null;
        	}

        	//过滤动态广播行为
        	if(SpecialActionTypeConvertHelper.ACTIONID_32002.contains(actionNougat.getActionId()) && !SpecialActionTypeConvertHelper.isMatcherContent(actionNougat.getDetailsData(), SpecialActionTypeConvertHelper.DYNAMICREGISTRATIONBROADCAST)){
        		return null;
        	}

        	//过滤浏览器行为误报2022-11-29
        	if(StringUtils.isNoneBlank(actionStackInfo) && SpecialActionTypeConvertHelper.isMatcherContent(actionStackInfo, filter)) {
        		return null;
        	}
        	
        	//过滤28006 详情为gms包名
        	if(SpecialActionTypeConvertHelper.ACTIONID_28006.contains(actionNougat.getActionId()) && (SpecialActionTypeConvertHelper.isMatcherContent(stackInfo, SpecialActionTypeConvertHelper.FILTERPACKAGE) || SpecialActionTypeConvertHelper.isMatcherContent(detailsData, SpecialActionTypeConvertHelper.FILTERPACKAGE))) {
        		return null;
        	}
        	
        	if(SpecialActionTypeConvertHelper.ACTIONID_28005.contains(actionNougat.getActionId()) && SpecialActionTypeConvertHelper.isMatcherContent(stackInfo, SpecialActionTypeConvertHelper.FILTERACTIONSTACKINFO_28005)) {
        		return null;
        	}
        	

        	//过滤iccid
        	if(SpecialActionTypeConvertHelper.ACTIONID_10004.contains(actionNougat.getActionId())  && !SpecialActionTypeConvertHelper.isMatcherContent(detailsData, SpecialActionTypeConvertHelper.ICCID_PHONE_NUMBER_PATTERN_10004)) {
        		return null;
        	}
        	
        	//20220907过滤获取运行中的进程代码中过滤掉这个行为 (去除getRunningAppProcesses监控)
        	if(SpecialActionTypeConvertHelper.ACTIONID_22002.contains(actionNougat.getActionId()) && (actionStackInfo.contains(SpecialActionTypeConvertHelper.FILTERACTIONSTACKINFO1) || actionStackInfo.contains(SpecialActionTypeConvertHelper.FILTERACTIONSTACKINFO2))) {
        		return null;
        	}
        	
        	//过滤尝试写入SDCard数据(创建) mkdir行为过滤
        	if(SpecialActionTypeConvertHelper.WRITE_EXTERNAL_STORAGE_ACTIONIDS.contains(actionNougat.getActionId()) && StringUtils.isNoneBlank(actionNougat.getDetailsData()) && SpecialActionTypeConvertHelper.writeExternalStorageRegex(actionNougat.getDetailsData())) {
        		return null;
        	}
            
        	//过滤runtime的getprop产生的获取设备序列号信息
        	if(SpecialActionTypeConvertHelper.ACTIONID_31001.contains(actionNougat.getActionId()) && StringUtils.isNoneBlank(actionNougat.getDetailsData()) && SpecialActionTypeConvertHelper.isMatcherContent(actionNougat.getDetailsData(), SpecialActionTypeConvertHelper.FILTER_31001_DETAIL)) {
        		return null;
        	}

        	//过滤iccid
        	if(SpecialActionTypeConvertHelper.ACTIONID_10004.contains(actionNougat.getActionId()) && (SpecialActionTypeConvertHelper.isMatcherContent(stackInfo, SpecialActionTypeConvertHelper.DYNAMICICCIDSTACKINFO) && !SpecialActionTypeConvertHelper.isMatcherContent(detailsData, SpecialActionTypeConvertHelper.ICCID_PHONE_NUMBER_PATTERN_10004))) {
        		return null;
        	}
        	
        	//过滤多媒体发生变化
        	if(SpecialActionTypeConvertHelper.ACTIONID_14017_14018.contains(actionNougat.getActionId()) && (!SpecialActionTypeConvertHelper.isMatcherContent(actionNougat.getDetailsData(), SpecialActionTypeConvertHelper.FILTER_DETAIL_14017_14018_REGEX))) {
        		return null;
        	}
        	
        	//过滤获取IP误报
        	if(SpecialActionTypeConvertHelper.ACTIONID_IP.contains(actionNougat.getActionId()) && 
        			(SpecialActionTypeConvertHelper.isMatcherContent(stackInfo, SpecialActionTypeConvertHelper.FILTER_DETAIL_IP_REGEX))) {
        		return null;
        	}
        	
        	//设定开关-打开后行为只有库里面规则比配才能入库-2023-03-08
        	String taierFilterActionOpen = commonProperties.getProperty("taier.filter.action.open");
        	//泰尔实验室行为数据
        	if(StringUtils.isNotBlank(taierFilterActionOpen) && "true".equals(taierFilterActionOpen) && 
        			filterActionRegex(actionNougat.getActionId(),actionNougat.getStackInfo(), actionNougat.getDetailsData()) == false){
        		return null;
        	}
    	}

        //v2.6.2_R1 20220510增加行为类型转换bing
        if(StringUtils.isNoneBlank(actionNougat.getDetailsData()) && SpecialActionTypeConvertHelper.ACTIONIDS.contains(actionNougat.getActionId())) {
        	String detail = SpecialActionTypeConvertHelper.actionTypeConvert(actionNougat.getDetailsData());
        	actionNougat.setDetailsData(StringUtils.isBlank(detail) ? actionNougat.getDetailsData() : detail);
        }
    	
        // 是否是使用APP前触发
        boolean isTriggeredBeforeUseApp = behaviorStage == BehaviorStageEnum.BEHAVIOR_GRANT;
        actionNougat.setType(isTriggeredBeforeUseApp);
        return actionNougat;
    }

    public boolean filterActionRegex(Long actionId, String stackInfo, String detailsData) {
        List<TActionFilterRegex> regexList = actionFilterGroupDao.findFilterList(TerminalTypeEnum.ANDROID);
        if (regexList == null || regexList.isEmpty()) {
            return true;
        }
        return FilteractionRegexHelper.filteractionRegexMethod(actionId, stackInfo, detailsData, regexList);
    }

    public static List<SdkUtils.TargetSdkInfo> analysisSdk(String detailsData, List<String> stackInfoList, long actionId, List<TSdkLibrary> sdks) {
        // 根据调用栈找到sdk信息
        List<SdkUtils.TargetSdkInfo> targetSdks = SdkUtils.findTargetSdk(sdks, stackInfoList);
        /**
         * 判断自启动行为是app还是sdk触发的，判断触发的类包名是否和应用包名一致
         * com.orion.xiaoya.speakerclient/com.orion.xiaoya.speakerclient.push.service.SyncService
         * com.shuqi.controller/com.taobao.accs.EventReceiver action=android.intent.action.BOOT_COMPLETED
         */
        if (actionId == ACTION_ID_BOOT && StringUtils.isNotBlank(detailsData)) {
            String[] params = detailsData.split("/");
            if (params.length >= 2 && !StringUtils.startsWith(params[1], params[0])) {
                // 拿到类名
                String clazzName = params[1].replace("\\s?action=[\\s\\S]+$", "");
                // 根据类名提取包名，默认包名就是3层
                String packageName = PackageNameExtractHelper.extractThreeLevelPackageName(clazzName);
                // 为了避免重复添加，需要判断包名是否在sdk列表内
                if (targetSdks.stream().noneMatch(sdk -> sdk.getPackageName().stream().noneMatch(p -> StringUtils.contains(p, packageName)))) {
                    SdkUtils.TargetSdkInfo targetSdkInfo = new SdkUtils.TargetSdkInfo();
                    targetSdkInfo.setName(packageName);
                    targetSdkInfo.setPackageName(Collections.singletonList(packageName));
                    targetSdks.add(targetSdkInfo);
                }
            }
        }
        return targetSdks;
    }

    public static List<String> parseStackInfo(String stackInfoText, String splitRegex) {
        if (StringUtils.isBlank(stackInfoText)) {
            return  Collections.emptyList();
        }
        return Arrays.stream(stackInfoText.split(splitRegex))
                .filter(s -> !s.startsWith("android."))
                .filter(s -> !s.startsWith("java."))
                .collect(Collectors.toList());
    }

    public static void setExecutorInfo(RawExecutor actionExecutor, String detailsData, String stackInfo, Long actionId,
                                       List<TSdkLibrary> sdkList, String appPackageName, String appName) {
        List<String> stackInfoList = parseStackInfo(stackInfo, "<---");
        // 分析sdk信息
        List<SdkUtils.TargetSdkInfo> targetSdks = analysisSdk(detailsData, stackInfoList, actionId, sdkList);

        //增强校验是否是app（也可能存在误报）
        boolean isApp = SdkUtils.checkStackInfoIsConcatAppPackage(appPackageName, stackInfoList);

        // 根据堆栈数据，更新主体类型
        if (CollectionUtils.isNotEmpty(targetSdks) && !isApp) {
            Set<String> executors = targetSdks.stream().map(SdkUtils.TargetSdkInfo::getName).collect(Collectors.toSet());
            Set<String> packages = targetSdks.stream()
                    .flatMap(targetSdkInfo -> targetSdkInfo.getPackageName().stream())
                    .collect(Collectors.toSet());
            Set<Long> sdkIds = targetSdks.stream()
                    .map(SdkUtils.TargetSdkInfo::getId)
                    .filter(Objects::nonNull).collect(Collectors.toSet());
            // 存放json格式数据，报告书数据解析用
            if (!sdkIds.isEmpty()) {
                actionExecutor.setSdkIds(JSON.toJSONString(sdkIds));
            }
            actionExecutor.setExecutorType(ExecutorTypeEnum.SDK.getValue());
            actionExecutor.setExecutor(String.join(",", executors));
            actionExecutor.setPackageName(String.join(",", packages));
        } else {
            // 默认主体类型为APP（本应用）
            actionExecutor.setExecutorType(ExecutorTypeEnum.APP.getValue());
            actionExecutor.setExecutor(appName);
            actionExecutor.setPackageName(appPackageName);
        }
    }

}
