package cn.ijiami.detection.analyzer;

import cn.ijiami.detection.analyzer.bo.CaptureInfoBO;
import cn.ijiami.detection.analyzer.parser.CaptureHostIpInfoParser;
import cn.ijiami.detection.analyzer.parser.IDetectDataParser;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.*;
import cn.ijiami.detection.entity.interfaces.RawExecutor;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.BooleanEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.helper.SensitiveWordsHelper;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.HttpPacketFormatter;
import cn.ijiami.detection.utils.IpUtil;
import cn.ijiami.detection.utils.SdkUtils;
import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.ijiami.detection.analyzer.BehaviorInfoAction.parseStackInfo;
import static cn.ijiami.detection.analyzer.bo.CaptureInfoBO.buildCaptureInfoBO;
import static cn.ijiami.detection.constant.PinfoConstant.DETAILS_EMPTY;
import static cn.ijiami.detection.utils.CommonUtil.optDetails;
import static cn.ijiami.detection.utils.SensitiveUtils.isPlaintextTransmission;

@Component
public class CaptureHostIpAction {

    private static Logger logger = LoggerFactory.getLogger(CaptureHostIpAction.class);

    @Autowired
    private IpUtil ipUtil;

    public List<CaptureInfoBO> analyzeCaptureInfo(String filePath, List<TSdkLibrary> sdks, String appName, String appPackageName) {
        logger.info("analyzeCaptureInfo");
        IDetectDataParser<CaptureInfoBO> parser = new CaptureHostIpInfoParser();
        List<CaptureInfoBO> result = parser.parser(filePath, appName);
        result.parallelStream().forEach(captureInfoBO -> {
            setExecutorInfo(captureInfoBO, captureInfoBO.getStackInfo(), sdks, appPackageName, appName);
        });
        return result;
    }

    public static void setExecutorInfo(RawExecutor actionExecutor, String stackInfo, List<TSdkLibrary> sdkList, String appPackageName, String appName) {
        List<String> stackInfoList = parseStackInfo(stackInfo, "\\s+");
        List<SdkUtils.TargetSdkInfo> targetSdks = SdkUtils.findTargetSdk(sdkList, stackInfoList);
        //增强校验是否是app
        boolean isApp = SdkUtils.checkStackInfoIsConcatAppPackage(appPackageName, stackInfoList);
        if (CollectionUtils.isNotEmpty(targetSdks) && !isApp) {
            Set<String> executors = targetSdks.stream().map(SdkUtils.TargetSdkInfo::getName).collect(Collectors.toSet());
            Set<String> packages = targetSdks.stream()
                    .flatMap(targetSdkInfo -> targetSdkInfo.getPackageName().stream())
                    .collect(Collectors.toSet());
            Set<Long> sdkIds = targetSdks.stream().map(SdkUtils.TargetSdkInfo::getId).collect(Collectors.toSet());
            // 存放json格式数据，报告书数据解析用
            actionExecutor.setSdkIds(JSON.toJSONString(sdkIds));
            actionExecutor.setExecutorType(ExecutorTypeEnum.SDK.getValue());
            actionExecutor.setExecutor(String.join(",", executors));
            actionExecutor.setPackageName(String.join(",", packages));
        } else {
            actionExecutor.setExecutorType(ExecutorTypeEnum.APP.getValue());
            actionExecutor.setExecutor(appName);
            actionExecutor.setPackageName(appPackageName);
        }
    }

    /**
     * 获取传输敏感词信息
     *
     * @param captureInfos
     * @param sensitiveWords
     * @param taskId
     * @param behaviorStageEnum
     * @return
     */
    public List<TPrivacySensitiveWord> getPrivacySensitiveWordResult(List<CaptureInfoBO> captureInfos, List<TSensitiveWord> sensitiveWords, Long taskId,
                                                                     BehaviorStageEnum behaviorStageEnum) {
        if (CollectionUtils.isEmpty(captureInfos)) {
            logger.info("CaptureInfoAction - 传输个人信息数据初步解析，无数据");
            return new ArrayList<>();
        }
        logger.info("CaptureInfoAction - 传输个人信息数据初步解析：{} 条", captureInfos.size());
        List<TPrivacySensitiveWord> result = new ArrayList<>();
        sensitiveWords.forEach(sensitiveWord -> {
            // 无名称，无正则视为无效
            if (StringUtils.isNotBlank(sensitiveWord.getRegex()) || StringUtils.isNotBlank(sensitiveWord.getName())) {
                captureInfos.forEach(captureInfo -> {
                    if (StringUtils.isNotBlank(sensitiveWord.getRegex())) {
                        result.addAll(matchCaptureInfoByRegex(taskId, behaviorStageEnum, captureInfo, sensitiveWord));
                    } else {
                        result.addAll(matchCaptureInfoByWord(taskId, behaviorStageEnum, captureInfo, sensitiveWord));
                    }
                });
            }
        });
        return result;
    }

    /**
     * 获取传输敏感词信息
     *
     * @param outsideAddress
     * @param sensitiveWords
     * @return
     */
    public List<TPrivacySensitiveWord> getPrivacySensitiveWordResult(TPrivacyOutsideAddress outsideAddress, List<TSensitiveWord> sensitiveWords) {
        if (Objects.isNull(outsideAddress)) {
            return new ArrayList<>();
        }
        List<TPrivacySensitiveWord> result = new ArrayList<>();
        CaptureInfoBO captureInfo = buildCaptureInfoBO(outsideAddress);
        sensitiveWords.forEach(sensitiveWord -> {
            // 无名称，无正则视为无效
            if (StringUtils.isNotBlank(sensitiveWord.getRegex()) || StringUtils.isNotBlank(sensitiveWord.getName())) {
                if (StringUtils.isNotBlank(sensitiveWord.getRegex())) {
                    result.addAll(matchCaptureInfoByRegex(outsideAddress.getTaskId(), outsideAddress.getBehaviorStage(), captureInfo, sensitiveWord));
                } else {
                    result.addAll(matchCaptureInfoByWord(outsideAddress.getTaskId(), outsideAddress.getBehaviorStage(), captureInfo, sensitiveWord));
                }
            }
        });
        return result;
    }

    /**
     * 获取境内外ip通讯信息
     *
     * @param captureInfos
     * @param taskId
     * @param behaviorStageEnum
     * @return
     */
    public List<TPrivacyOutsideAddress> getPrivacyOutsideAddressResult(List<CaptureInfoBO> captureInfos, Long taskId, BehaviorStageEnum behaviorStageEnum) {
        long startTime = System.currentTimeMillis();
        List<TPrivacyOutsideAddress> result = captureInfos.stream().parallel().map(captureInfo -> {
            //v2.5版本只要是string类型为空则按照--入库
            TPrivacyOutsideAddress outsideAddress = new TPrivacyOutsideAddress();
            // 存放堆栈数据
            outsideAddress.setTaskId(taskId);
            outsideAddress.setBehaviorStage(behaviorStageEnum);
            outsideAddress.setExecutorType(captureInfo.getExecutorType());
            outsideAddress.setExecutor(captureInfo.getExecutor());
            // 存放json格式的数据
            outsideAddress.setSdkIds(captureInfo.getSdkIds());
            outsideAddress.setStackInfo(CommonUtil.filterPrintString(captureInfo.getStackInfo()));
            outsideAddress.setDetailsData(StringUtils.isEmpty(captureInfo.getDetailsData())?DETAILS_EMPTY:CommonUtil.filterPrintString(captureInfo.getDetailsData()));
            outsideAddress.setResponseData(optDetails(CommonUtil.filterPrintString(captureInfo.getResponse())));
            Date date = new Date(Long.parseLong(captureInfo.getTimeStamp()));
            outsideAddress.setStrTime(date);
            outsideAddress.setActionTime(date);
            // 存放host_ip中的数据
            outsideAddress.setIp(StringUtils.isEmpty(captureInfo.getIp())?DETAILS_EMPTY:captureInfo.getIp());
            outsideAddress.setProtocol(StringUtils.isEmpty(captureInfo.getProtocol())?DETAILS_EMPTY:captureInfo.getProtocol());
            outsideAddress.setCookie(StringUtils.isEmpty(captureInfo.getCookie())?DETAILS_EMPTY:captureInfo.getCookie());
            //新增cookie标识，以便前台展示
            outsideAddress.setCookieMark(StringUtils.isNotBlank(captureInfo.getCookie())? BooleanEnum.TRUE.value : BooleanEnum.FALSE.value);
            outsideAddress.setPort(StringUtils.isEmpty(captureInfo.getPort())?DETAILS_EMPTY:captureInfo.getPort());
            outsideAddress.setHost(StringUtils.isEmpty(captureInfo.getHost())?DETAILS_EMPTY:captureInfo.getHost());
            String address = "";
            if (StringUtils.isNotBlank(captureInfo.getIp())) {
                address = ipUtil.getAddress(captureInfo.getIp());
            } else {
            	if (StringUtils.contains(captureInfo.getHost(), ":")) {
	                // 避免端口获取不到具体地址信息
	                address = ipUtil.getAddress(captureInfo.getHost().split(":")[0]);
	            } else {
	                address = ipUtil.getAddress(captureInfo.getHost());
	            }
            }
            outsideAddress.setAddress(StringUtils.isEmpty(address)?DETAILS_EMPTY:address);
            outsideAddress.setCounter(1);
            int isOutSide = IpUtil.isOutSide(address);
            outsideAddress.setOutside(isOutSide);
            outsideAddress.setRequestMethod(StringUtils.isEmpty(captureInfo.getMethod())?DETAILS_EMPTY:captureInfo.getMethod());
            outsideAddress.setUrl(StringUtils.isEmpty(captureInfo.getUrl())?DETAILS_EMPTY:captureInfo.getUrl());
            outsideAddress.setPackageName(captureInfo.getPackageName());
            return outsideAddress;
        }).collect(Collectors.toList());
        logger.info("getPrivacyOutsideAddressResult captureInfos={}条 耗时：{}ms", captureInfos.size(), System.currentTimeMillis() - startTime);
        return result;
    }

    /**
     * 关键词匹配传输个人信息数据
     *
     * @param captureInfo 捕获数据
     * @param sensitive   敏感信息
     * @return
     */
    private List<TPrivacySensitiveWord> matchCaptureInfoByWord(Long taskId, BehaviorStageEnum behaviorStageEnum, CaptureInfoBO captureInfo,
                                                               TSensitiveWord sensitive) {
        List<TPrivacySensitiveWord> sensitiveWordList = new ArrayList<>();
        // 判断请求参数中是否携带个人信息数据
        if(sensitive.getTypeId() != null && sensitive.getTypeId() == 11) {
        	if (StringUtils.isNotBlank(captureInfo.getOriginal())) {
                sensitiveWordList.addAll(matchByWordFromCookieOrParams(taskId, behaviorStageEnum, captureInfo, sensitive, false));
            }
        }else {
        	if (StringUtils.isNotBlank(captureInfo.getParams())) {
                sensitiveWordList.addAll(matchByWordFromCookieOrParams(taskId, behaviorStageEnum, captureInfo, sensitive, false));
            }
        }
        
        // 判断cookie中是否携带个人信息数据
        if (StringUtils.isNotBlank(captureInfo.getCookie())) {
            sensitiveWordList.addAll(matchByWordFromCookieOrParams(taskId, behaviorStageEnum, captureInfo, sensitive, true));
        }
        return sensitiveWordList;
    }

    /**
     * 正则匹配传输个人信息数据
     *
     * @param captureInfo 捕获数据
     * @param sensitive   敏感信息
     * @return
     */
    private List<TPrivacySensitiveWord> matchCaptureInfoByRegex(Long taskId, BehaviorStageEnum behaviorStageEnum, CaptureInfoBO captureInfo,
                                                                TSensitiveWord sensitive) {
        List<TPrivacySensitiveWord> sensitiveWordList = new ArrayList<>();
        // 判断请求参数中是否携带个人信息数据
        if (StringUtils.isNotBlank(captureInfo.getParams())) {
            sensitiveWordList.addAll(matchByRegexFromCookieOrParams(taskId, behaviorStageEnum, captureInfo, sensitive, false));
        }
        // 判断cookie中是否携带个人信息数据
        if (StringUtils.isNotBlank(captureInfo.getCookie())) {
            sensitiveWordList.addAll(matchByRegexFromCookieOrParams(taskId, behaviorStageEnum, captureInfo, sensitive, true));
        }
        return sensitiveWordList;
    }

    private List<TPrivacySensitiveWord> matchByWordFromCookieOrParams(Long taskId, BehaviorStageEnum behaviorStageEnum, CaptureInfoBO captureInfo,
                                                                      TSensitiveWord sensitive, boolean cookie) {
        String sensitiveWord = sensitive.getSensitiveWords();
        if (StringUtils.isBlank(sensitiveWord)) {
            return new ArrayList<>();
        }
        boolean isSpec = false;
        String params = captureInfo.getParams();
        if(sensitive.getTypeId() != null && sensitive.getTypeId() == 11) {
        	params = captureInfo.getOriginal();
        	isSpec = true;
        }
        List<TPrivacySensitiveWord> sensitiveWordList = new ArrayList<>();
        for (SensitiveWordsHelper.SensitiveInfo word : SensitiveWordsHelper.find(cookie ? captureInfo.getCookie() : params,
                StringUtils.EMPTY, sensitive.getSensitiveWords(), isSpec)) {
            // 组装实体
            TPrivacySensitiveWord bean = buildCaptureInfoToSensitiveWord(taskId, behaviorStageEnum, captureInfo, sensitive,
                    word.getCode(), captureInfo.getMethod(), word.getWord(), cookie);
            // 存放结果
            sensitiveWordList.add(bean);
        }
        return sensitiveWordList;
    }

    private List<String> splitParamValues(String paramsStr) {
        List<String> values = new ArrayList<>();
        String[] paramArray = paramsStr.split("&");
        for (String param:paramArray) {
            int start = param.indexOf("=");
            if (start >= 0 && start + 1 < param.length()) {
                values.add(param.substring(start + 1));
            } else {
                values.add(param);
            }
        }
        return values;
    }

    private List<String> splitCookieValues(String cookieStr) {
        List<String> values = new ArrayList<>();
        String[] cookieArray = cookieStr.split(";");
        for (String cookie:cookieArray) {
            int start = cookie.indexOf("=");
            if (start >= 0 && start + 1 < cookie.length()) {
                values.add(cookie.substring(start + 1));
            } else {
                values.add(cookie);
            }
        }
        return values;
    }

    private List<TPrivacySensitiveWord> matchByRegexFromCookieOrParams(Long taskId, BehaviorStageEnum behaviorStageEnum, CaptureInfoBO captureInfo,
                                                                       TSensitiveWord sensitive, boolean cookie) {
        List<TPrivacySensitiveWord> sensitiveWordList = new ArrayList<>();
        String from;
        if (cookie) {
            from = captureInfo.getCookie();
        } else {
        	if(sensitive.getSensitiveWords().contains("OAID")) {
        		logger.info(sensitive.getSensitiveWords());
        	}
        	if(sensitive.getTypeId() != null && sensitive.getTypeId()==11) {
        		 from = captureInfo.getOriginal();
        	}else {
        		 from = captureInfo.getParams();
        	}
        }
        Pattern pattern = Pattern.compile(sensitive.getRegex());
        Matcher matcher = pattern.matcher(from);
        logger.info("sensitiveWord.getRegex()={}, sensitiveWord.getName()={} details={}", sensitive.getRegex(), sensitive.getName(), captureInfo.getDetailsData());
        // 获取正则匹配到的内容
        while (matcher.find()) {
            boolean isInvalidInfo = SensitiveWordsHelper.isInvalidInfo(matcher, from, sensitive, true);
            if (isInvalidInfo) {
                continue;
            }
            String sensitiveWord = matcher.group();
            if (StringUtils.isBlank(sensitiveWord)) {
                continue;
            }
            //关键字匹配到开始位置
            String code = SensitiveWordsHelper.getMatchString(matcher, from);
            String method = captureInfo.getMethod();
            // 组装实体
            TPrivacySensitiveWord bean = buildCaptureInfoToSensitiveWord(taskId, behaviorStageEnum, captureInfo, sensitive, code, method, sensitiveWord, cookie);
            // 存放结果
            sensitiveWordList.add(bean);
        }
        return sensitiveWordList;
    }

    /**
     * 从capture info中组装敏感词
     *
     * @param captureInfo  网络抓包数据
     * @param sensitive    关键词
     * @param code 标记源
     * @param method       方法类型
     * @param keyword      抓取关键词
     * @return
     */
    private TPrivacySensitiveWord buildCaptureInfoToSensitiveWord(Long taskId, BehaviorStageEnum behaviorStageEnum, CaptureInfoBO captureInfo,
                                                                  TSensitiveWord sensitive, String code, String method, String keyword, boolean cookie) {
        //v2.5版本只要是string类型为空则按照--入库
        // 组装实体
        TPrivacySensitiveWord bean = new TPrivacySensitiveWord();
        // 存放堆栈数据
        bean.setStackInfo(StringUtils.isEmpty(captureInfo.getStackInfo())?PinfoConstant.DETAILS_EMPTY:CommonUtil.filterPrintString(captureInfo.getStackInfo()));
        bean.setDetailsData(optDetails(CommonUtil.filterPrintString(captureInfo.getDetailsData())));
        bean.setResponseData(optDetails(CommonUtil.filterPrintString(captureInfo.getResponse())));
        bean.setActionTime(new Date(Long.parseLong(captureInfo.getTimeStamp())));
        // 任务信息
        bean.setTaskId(taskId);
        bean.setBehaviorStage(behaviorStageEnum);
        bean.setExecutorType(captureInfo.getExecutorType());
        bean.setExecutor(captureInfo.getExecutor());
        bean.setSdkName(captureInfo.getExecutor());
        // 存放系解析出的json数据
        bean.setSdkIds(captureInfo.getSdkIds());
        // 存放敏感词数据
        bean.setTypeId(sensitive.getTypeId());
        bean.setName(StringUtils.isEmpty(sensitive.getName())?PinfoConstant.DETAILS_EMPTY:sensitive.getName());
        bean.setSensitiveWord(StringUtils.isEmpty(keyword)?PinfoConstant.DETAILS_EMPTY:keyword);
        bean.setRiskLevel(sensitive.getRiskLevel());
        bean.setSuggestion(sensitive.getSuggestion());
        // 存放解析出的数据
        bean.setMethod(StringUtils.isEmpty(method)?PinfoConstant.DETAILS_EMPTY:method);
        bean.setUrl(StringUtils.isEmpty(captureInfo.getUrl())?PinfoConstant.DETAILS_EMPTY:captureInfo.getUrl());
        bean.setHost(StringUtils.isEmpty(captureInfo.getHost())?PinfoConstant.DETAILS_EMPTY:captureInfo.getHost());
        bean.setPort(StringUtils.isEmpty(captureInfo.getPort())?PinfoConstant.DETAILS_EMPTY:captureInfo.getPort());
        bean.setAddress(StringUtils.isEmpty(captureInfo.getPath())?PinfoConstant.DETAILS_EMPTY:captureInfo.getPath());
        bean.setCode(StringUtils.isEmpty(code)?PinfoConstant.DETAILS_EMPTY : code);
        bean.setProtocol(StringUtils.isEmpty(captureInfo.getProtocol())? PinfoConstant.DETAILS_EMPTY:captureInfo.getProtocol());
        bean.setCookie(StringUtils.isEmpty(captureInfo.getCookie())?PinfoConstant.DETAILS_EMPTY:captureInfo.getCookie());
        //增加cookie标记，以便前端展示cookie标签
        bean.setCookieMark(StringUtils.isNotBlank(captureInfo.getCookie())?1:null);
        bean.setCookieWord(cookie);
        bean.setIp(StringUtils.isEmpty(captureInfo.getIp())?PinfoConstant.DETAILS_EMPTY:captureInfo.getIp());
        bean.setPackageName(captureInfo.getPackageName());
        String address = "";
        if(StringUtils.isNoneBlank(captureInfo.getIp())) {
        	address = ipUtil.getAddress(captureInfo.getIp());
        } else {
        	// 避免端口获取不到具体地址信息
            if (StringUtils.contains(captureInfo.getHost(), ":")) {
                address = ipUtil.getAddress(captureInfo.getHost().split(":")[0]);
            } else {
                address = ipUtil.getAddress(captureInfo.getHost());
            }
        }
        
        bean.setAttributively(StringUtils.isEmpty(address)?PinfoConstant.DETAILS_EMPTY:address);
        bean.setPlaintextTransmission(isPlaintextTransmission(sensitive).value);
        return bean;
    }

}
