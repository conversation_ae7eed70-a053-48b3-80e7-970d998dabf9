package cn.ijiami.detection.analyzer.parser;

import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.VO.detection.privacy.dto.AppletActionBO;
import cn.ijiami.detection.VO.detection.privacy.dto.HarmonyActionBO;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TSdkLibrary;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.utils.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName HarmonyBehaviorInfoAction.java
 * @Description 鸿蒙行为解析
 * @createTime 2024年06月24日 17:55:00
 */
@Slf4j
@Component
public class HarmonyBehaviorInfoAction {


    public List<TPrivacyActionNougat> analyzeBehaviorInfo(String filePath, TaskDetailVO taskDetailVO, BehaviorStageEnum behaviorStage) {
        HarmonyBehaviorInfoParser infoParser = new HarmonyBehaviorInfoParser();
        List<HarmonyActionBO> actionBOList = infoParser.parser(filePath, taskDetailVO.getApk_name());
        if (CollectionUtils.isEmpty(actionBOList)) {
            log.info("HarmonyBehaviorInfoAction - 行为数据初步解析，无数据");
            return new ArrayList<>();
        }
        return actionBOList.stream()
                .map(bo -> buildBehavioral(taskDetailVO.getTaskId(), bo, behaviorStage, taskDetailVO))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static TPrivacyActionNougat buildBehavioral(Long taskId, HarmonyActionBO data, BehaviorStageEnum behaviorStage, TaskDetailVO taskDetailVO) {
        long actionTime = parseTimestamp(data.getActionTime());
        if (actionTime <= 0) {
            return null;
        }
        TPrivacyActionNougat actionNougat = new TPrivacyActionNougat();
        actionNougat.setTaskId(taskId);
        // 默认主体类型为APP（本应用）
        actionNougat.setExecutorType(ExecutorTypeEnum.APP.getValue());
        actionNougat.setExecutor(taskDetailVO.getApk_name());
        actionNougat.setPackageName(taskDetailVO.getApk_package());
        // 组装堆栈数据
        actionNougat.setActionId(data.getTypeId());
        actionNougat.setStackInfo(PinfoConstant.DETAILS_EMPTY);
        actionNougat.setDetailsData(buildDetailsData(data.getLogMark()));
        actionNougat.setBehaviorStage(behaviorStage);
        actionNougat.setActionTime(new Date(actionTime));
        actionNougat.setActionTimeStamp(actionTime);
        // 是否是使用APP前触发
        boolean isTriggeredBeforeUseApp = actionNougat.getBehaviorStage() == BehaviorStageEnum.BEHAVIOR_GRANT;
        actionNougat.setType(isTriggeredBeforeUseApp);
        return actionNougat;
    }

    public static String buildDetailsData(String logMark) {
        int index = StringUtils.indexOf(logMark, "/");
        if (index >= 0 && index + 1 < logMark.length()) {
            return logMark.substring(index + 1, logMark.length() - 1);
        }
        return PinfoConstant.DETAILS_EMPTY;
    }

    public static long parseTimestamp(String dateTimeStr) {
        // 定义输入的日期时间格式
        SimpleDateFormat inputFormat = new SimpleDateFormat("MM-dd HH:mm:ss.SS");

        // 获取当前日期
        Calendar calendar = Calendar.getInstance();
        int currentYear = calendar.get(Calendar.YEAR);

        try {
            // 解析输入的日期时间字符串
            Date parsedDate = inputFormat.parse(dateTimeStr);

            // 设置解析后的日期的年份为当前年份
            calendar.setTime(parsedDate);
            calendar.set(Calendar.YEAR, currentYear);

            // 返回时间戳（毫秒）
            return calendar.getTimeInMillis();
        } catch (ParseException e) {
            log.error("HarmonyBehaviorInfoAction - 解析时间错误", e);
            return -1; // 如果解析失败，返回 -1 表示错误
        }
    }
}
