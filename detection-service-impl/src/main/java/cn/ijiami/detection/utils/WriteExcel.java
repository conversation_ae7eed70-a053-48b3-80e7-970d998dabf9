package cn.ijiami.detection.utils;

import static cn.ijiami.detection.utils.CommonUtil.getReportBehaviorStageName;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.common.usermodel.HyperlinkType;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.ClientAnchor;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.Drawing;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Hyperlink;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Picture;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import cn.hutool.core.date.DateUtil;
import cn.ijiami.base.common.file.api.IBaseFileService;
import cn.ijiami.detection.VO.AppletExtraInfoVO;
import cn.ijiami.detection.VO.ExcelReportPrivacyLawsDetailVO;
import cn.ijiami.detection.VO.LawActionDetailVO;
import cn.ijiami.detection.VO.PermissionVO;
import cn.ijiami.detection.VO.Privacy164ResultVO;
import cn.ijiami.detection.VO.PrivacyLawsMisjudgmentVO;
import cn.ijiami.detection.VO.PrivacyLawsResult;
import cn.ijiami.detection.VO.RealTimeLog;
import cn.ijiami.detection.VO.RealtimeSdkItem;
import cn.ijiami.detection.VO.SensitiveWordExcelReportVO;
import cn.ijiami.detection.VO.SharedPrefsExcelReportVO;
import cn.ijiami.detection.VO.SuspiciousSdkDataVO;
import cn.ijiami.detection.VO.compliance.CompliancePrivacyActionNougatVO;
import cn.ijiami.detection.VO.detection.SdkVO;
import cn.ijiami.detection.VO.statistics.AssetsInfo;
import cn.ijiami.detection.VO.statistics.AssetsStatisticsDetail;
import cn.ijiami.detection.VO.statistics.AssetsTask;
import cn.ijiami.detection.VO.statistics.DetectFalsePositivesDetail;
import cn.ijiami.detection.VO.statistics.DetectFalsePositivesReportAssetsInfo;
import cn.ijiami.detection.VO.statistics.DetectFalsePositivesReportItem;
import cn.ijiami.detection.VO.statistics.DetectFalsePositivesReportLaw;
import cn.ijiami.detection.VO.statistics.DetectionStatisticsDetail;
import cn.ijiami.detection.VO.statistics.DetectionStatisticsLawItem;
import cn.ijiami.detection.VO.statistics.LawStatistics;
import cn.ijiami.detection.VO.statistics.SdkStatisticsDetail;
import cn.ijiami.detection.VO.statistics.SdkUsageStatistics;
import cn.ijiami.detection.config.IjiamiCommonProperties;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacyLawsDetail;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import cn.ijiami.detection.entity.TPrivacySharedPrefs;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.server.client.base.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.enums.LawDetailDataTypeEnum;
import cn.ijiami.detection.server.client.base.enums.LawResultStatusEnum;
import cn.ijiami.detection.enums.PrivacyLawId;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.enums.TaskDetectionTypeEnum;
import cn.ijiami.detection.server.client.base.enums.TerminalTypeEnum;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import cn.ijiami.framework.kit.utils.UuidUtil;
import cn.ijiami.manager.syslog.entity.TsysOperateLog;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

public class WriteExcel {

	private static final Logger LOG = LoggerFactory.getLogger(WriteExcel.class);

	private static final String EXCEL_XLS = "xls";
	private static final String EXCEL_XLSX = "xlsx";
	private static final String EXCEL_EMPTY = "--";
	private static final int STATISTICS_COLUMN_WIDTH = 256 * 30;

	private static final int STATISTICS_DEFAULT_SIZE = 20;
	private static final int COLUMN_WIDTH_AUTO = -1;
	private static final String FONT_NAME = "微软雅黑";

	public static void main(String[] args) {

//		Privacy164ResultVO dataMap = new Privacy164ResultVO();
//		dataMap.setName("爱加密");
//		dataMap.setMD5("ddddddd");
//		dataMap.setPakage("com.ijiami.cn");
//		dataMap.setApkReinforce("爱加密加固");
//		dataMap.setR10107(1);
//		dataMap.setR10207(1);
//		dataMap.setR10401(1);
//		List<Privacy164ResultVO> list = new ArrayList<>();
//		list.add(dataMap);
//		writeExcel(list, "F:/总数132_年月日时分秒1.xlsx");
		
		List<SdkVO> dataList = new ArrayList<>();
		SdkVO vo =new SdkVO();
		vo.setName("百度地图");
		vo.setTypeName("导航");
		vo.setPackageName("com.baidu.amap");
		vo.setManufacturer("百度科技");
		vo.setDescribe("这是一款地图导航类SDK");
		vo.setMinSdkVersion(12);
		vo.setTargetSdkVersion(29);
		vo.setCompileSdkVersion(3);
		
		List<PermissionVO> permissions = new ArrayList<>();
		
		for (int i = 0; i < 3; i++) {
			PermissionVO p = new PermissionVO();
			p.setAliasName("222"+i);
			p.setName("WRITE_EXTERNAL_STORAGE");
			p.setRemark("写入外部存储");
			p.setProtectionLevel("dangerous");
			p.setIsPrivacy(2);
			p.setUsed(true);
			p.setType(2);
			permissions.add(p);
		}
		vo.setPermissions(permissions);
		dataList.add(vo);

	}

	private static final String FULL_DATA_FORMAT = "yyyy-MM-dd HH:mm:ss";
	
	private static final String FULL_DATA_FORMATSS = "yyyy-MM-dd HH:mm:ss:SSS";

	public static void writeExcel(List<Privacy164ResultVO> dataList, String finalXlsxPath, Integer terminalType) {
		TerminalTypeEnum terminalTypeEnum = TerminalTypeEnum.getAndValid(terminalType);
		OutputStream out = null;
		try {
			// 获取总列数
			// int columnNumCount = cloumnCount;
			// 读取Excel文档
			File finalXlsxFile = new File(finalXlsxPath);
			Workbook workBook = getWorkbok(finalXlsxFile);
			// sheet 对应一个工作页
			Sheet sheet = workBook.getSheetAt(0);

			// 创建单元格样式
			CellStyle cell_style = workBook.createCellStyle();
			cell_style.setBorderBottom(BorderStyle.THIN); //下边框
			cell_style.setBorderLeft(BorderStyle.THIN);//左边框
			cell_style.setBorderTop(BorderStyle.THIN);//上边框
			cell_style.setBorderRight(BorderStyle.THIN);//右边框
			// 自定义字体颜色, 同单元格样式
			Font font = workBook.createFont();
			font.setFontName("微软雅黑");
			font.setColor(IndexedColors.RED.getIndex());
			cell_style.setFont(font);
			
			
			CellStyle cellStyle = workBook.createCellStyle();
			cellStyle.setBorderBottom(BorderStyle.THIN); //下边框
			cellStyle.setBorderLeft(BorderStyle.THIN);//左边框
			cellStyle.setBorderTop(BorderStyle.THIN);//上边框
			cellStyle.setBorderRight(BorderStyle.THIN);//右边框
			// 自定义字体颜色, 同单元格样式
			Font font1 = workBook.createFont();
			font1.setFontName("微软雅黑");
			cellStyle.setFont(font1);

			/**
			 * 往Excel中写新数据
			 */
			for (int j = 0; j < dataList.size(); j++) {
				// 创建一行：从第二行开始，跳过属性列
				Row row = sheet.createRow(j + 3);
				int columnIndex = 0;
				// 得到要插入的每一条记录
				Privacy164ResultVO vo = dataList.get(j);
				// 在一行内循环
				Cell first = row.createCell(columnIndex++);
				first.setCellValue(vo.getTaskStarttime() == null ? EXCEL_EMPTY : DateUtils.getDateFormat(vo.getTaskStarttime(), FULL_DATA_FORMAT));
				first.setCellStyle(cellStyle);

				writeStringAppInfo(row.createCell(columnIndex++), cellStyle, vo.getName());
				
				if (terminalTypeEnum.isApplet()) {
					AppletExtraInfoVO extraInfoVO = vo.getTaskDetailVO().getAppletExtraInfo();
					writeStringAppInfo(row.createCell(columnIndex++), cellStyle, vo.getVersion());
					writeStringAppInfo(row.createCell(columnIndex++), cellStyle, Objects.isNull(extraInfoVO) ? EXCEL_EMPTY : extraInfoVO.getAppId());
					writeStringAppInfo(row.createCell(columnIndex++), cellStyle, Objects.isNull(extraInfoVO) ? EXCEL_EMPTY : extraInfoVO.getServiceCategory());
					writeStringAppInfo(row.createCell(columnIndex++), cellStyle, Objects.isNull(extraInfoVO) ? EXCEL_EMPTY : extraInfoVO.getAccountSubject());
					writeStringAppInfo(row.createCell(columnIndex++), cellStyle, Objects.isNull(extraInfoVO) ? EXCEL_EMPTY : extraInfoVO.getPrivacyData());
					writeStringAppInfo(row.createCell(columnIndex++), cellStyle, Objects.isNull(extraInfoVO) ? EXCEL_EMPTY : extraInfoVO.getStatement());
					writeStringAppInfo(row.createCell(columnIndex++), cellStyle, Objects.isNull(extraInfoVO) ? EXCEL_EMPTY : extraInfoVO.getUpdateTime());
				} else {
					writeStringAppInfo(row.createCell(columnIndex++), cellStyle, vo.getPakage());
					writeStringAppInfo(row.createCell(columnIndex++), cellStyle, vo.getVersion());
					Cell c4 = row.createCell(columnIndex++);
					c4.setCellValue(vo.getSize()==null?0:Double.parseDouble(vo.getSize()));
					c4.setCellStyle(cellStyle);

					writeStringAppInfo(row.createCell(columnIndex++), cellStyle, vo.getMd5());
					writeStringAppInfo(row.createCell(columnIndex++), cellStyle, vo.getSignMd5());
					writeStringAppInfo(row.createCell(columnIndex++), cellStyle, vo.getSign());
					writeStringAppInfo(row.createCell(columnIndex++), cellStyle, vo.getApkReinforce());
				}
				
				Cell c9 = row.createCell(columnIndex++);
				c9.setCellValue(vo.getPermission1()==null?0:vo.getPermission1());
				c9.setCellStyle(cellStyle);
				
				Cell c10 = row.createCell(columnIndex++);
				c10.setCellValue(vo.getPermission2()==null?0:vo.getPermission2());
				c10.setCellStyle(cellStyle);
				
				Cell c11 = row.createCell(columnIndex++);
				c11.setCellValue(vo.getPermission3()==null?0:vo.getPermission3());
				c11.setCellStyle(cellStyle);
				
				Cell c12 = row.createCell(columnIndex++);
				if (terminalTypeEnum.isApplet()) {
					c12.setCellValue(vo.getPluginNum()==null?0:vo.getPluginNum());
				} else {
					c12.setCellValue(vo.getSdkNum()==null?0:vo.getSdkNum());
				}
				c12.setCellStyle(cellStyle);
				
				Cell c13 = row.createCell(columnIndex++);
				c13.setCellValue(vo.getR10101()==null || vo.getR10101()==2 ? "未发现风险" : "存在风险");
				c13.setCellStyle(cellStyle);
				if(vo.getR10101() != null && vo.getR10101()==1){
					c13.setCellStyle(cell_style);
				}
				
				Cell c14 = row.createCell(columnIndex++);
				c14.setCellValue(vo.getR10102()==null||vo.getR10102()==2 ? "未发现风险" : "存在风险");
				c14.setCellStyle(cellStyle);
				if(vo.getR10102() != null&&vo.getR10102()==1){
					c14.setCellStyle(cell_style);
				}
				
				Cell c15 = row.createCell(columnIndex++);
				c15.setCellValue(vo.getR10103()==null||vo.getR10103()==2 ? "未发现风险" : "存在风险");
				c15.setCellStyle(cellStyle);
				if(vo.getR10103() != null && vo.getR10103()==1){
					c15.setCellStyle(cell_style);
				}
				
				if (!terminalTypeEnum.isApplet()) {
					Cell c16 = row.createCell(columnIndex++);
					c16.setCellValue(vo.getR10104()==null||vo.getR10104()==2 ? "未发现风险" : "存在风险");
					c16.setCellStyle(cellStyle);
					if(vo.getR10104() !=null && vo.getR10104()==1){
						c16.setCellStyle(cell_style);
					}
					Cell c17 = row.createCell(columnIndex++);
					c17.setCellValue(vo.getR10105()==null || vo.getR10105()==2 ? "未发现风险" : "存在风险");
					c17.setCellStyle(cellStyle);
					if(vo.getR10105() !=null && vo.getR10105()==1){
						c17.setCellStyle(cell_style);
					}

					Cell c18 = row.createCell(columnIndex++);
					c18.setCellValue(vo.getR10106()==null||vo.getR10106()==2 ? "未发现风险" : "存在风险");
					c18.setCellStyle(cellStyle);
					if(vo.getR10106() !=null &&vo.getR10106()==1){
						c18.setCellStyle(cell_style);
					}
				}
				
				Cell c19 = row.createCell(columnIndex++);
				c19.setCellValue(vo.getR10107()==null||vo.getR10107()==2 ? "未发现风险" : "存在风险");
				c19.setCellStyle(cellStyle);
				if(vo.getR10107() !=null &&vo.getR10107()==1){
					c19.setCellStyle(cell_style);
				}
				
				Cell c20 = row.createCell(columnIndex++);
				c20.setCellValue(vo.getR10108()==null||vo.getR10108()==2 ? "未发现风险" : "存在风险");
				c20.setCellStyle(cellStyle);
				if(vo.getR10108() !=null &&vo.getR10108()==1){
					c20.setCellStyle(cell_style);
				}
				
				//
				Cell c21 = row.createCell(columnIndex++);
				c21.setCellValue(vo.getR10201()==null||vo.getR10201()==2 ? "未发现风险" : "存在风险");
				c21.setCellStyle(cellStyle);
				if(vo.getR10201() != null &&vo.getR10201()==1){
					c21.setCellStyle(cell_style);
				}
				
				Cell c22 = row.createCell(columnIndex++);
				c22.setCellValue(vo.getR10202()==null||vo.getR10202()==2 ? "未发现风险" : "存在风险");
				c22.setCellStyle(cellStyle);
				if(vo.getR10202() != null &&vo.getR10202()==1){
					c22.setCellStyle(cell_style);
				}

				if (!terminalTypeEnum.isApplet()) {
					Cell c23 = row.createCell(columnIndex++);
					c23.setCellValue(vo.getR10203()==null||vo.getR10203()==2 ? "未发现风险" : "存在风险");
					c23.setCellStyle(cellStyle);
					if(vo.getR10203() != null &&vo.getR10203()==1){
						c23.setCellStyle(cell_style);
					}

					Cell c24 = row.createCell(columnIndex++);
					c24.setCellValue(vo.getR10204()==null||vo.getR10204()==2 ? "未发现风险" : "存在风险");
					c24.setCellStyle(cellStyle);
					if(vo.getR10204() != null &&vo.getR10204()==1){
						c24.setCellStyle(cell_style);
					}
				}
				
				Cell c25 = row.createCell(columnIndex++);
				c25.setCellValue(vo.getR10205()==null||vo.getR10205()==2 ? "未发现风险" : "存在风险");
				c25.setCellStyle(cellStyle);
				if(vo.getR10205() != null &&vo.getR10205()==1){
					c25.setCellStyle(cell_style);
				}
				
				Cell c26 = row.createCell(columnIndex++);
				c26.setCellValue(vo.getR10206()==null||vo.getR10206()==2 ? "未发现风险" : "存在风险");
				c26.setCellStyle(cellStyle);
				if(vo.getR10206() != null &&vo.getR10206()==1){
					c26.setCellStyle(cell_style);
				}
				if (!terminalTypeEnum.isApplet()) {
					Cell c27 = row.createCell(columnIndex++);
					c27.setCellValue(vo.getR10207()==null||vo.getR10207()==2 ? "未发现风险" : "存在风险");
					c27.setCellStyle(cellStyle);
					if(vo.getR10207() != null &&vo.getR10207()==1){
						c27.setCellStyle(cell_style);
					}

					Cell c28 = row.createCell(columnIndex++);
					c28.setCellValue(vo.getR10208()==null||vo.getR10208()==2 ? "未发现风险" : "存在风险");
					c28.setCellStyle(cellStyle);
					if(vo.getR10208() != null &&vo.getR10208()==1){
						c28.setCellStyle(cell_style);
					}
				}
				/////////
				Cell c29 = row.createCell(columnIndex++);
				c29.setCellValue(vo.getR10301()==null||vo.getR10301()==2 ? "未发现风险" : "存在风险");
				c29.setCellStyle(cellStyle);
				if(vo.getR10301() != null&& vo.getR10301()==1){
					c29.setCellStyle(cell_style);
				}
				
				Cell c30 = row.createCell(columnIndex++);
				c30.setCellValue(vo.getR10302()==null||vo.getR10302()==2 ? "未发现风险" : "存在风险");
				c30.setCellStyle(cellStyle);
				if(vo.getR10302() != null&& vo.getR10302()==1){
					c30.setCellStyle(cell_style);
				}
				
				////
				Cell c31 = row.createCell(columnIndex++);
				c31.setCellValue(vo.getR10401()==null||vo.getR10401()==2 ? "未发现风险" : "存在风险");
				c31.setCellStyle(cellStyle);
				if(vo.getR10401() != null&& vo.getR10401()==1){
					c31.setCellStyle(cell_style);
				}
				
				
				Cell c33 = row.createCell(columnIndex++);
				c33.setCellValue(vo.getR10403()==null||vo.getR10403()==2 ? "未发现风险" : "存在风险");
				c33.setCellStyle(cellStyle);
				if(vo.getR10403() != null&&  vo.getR10403()==1){
					c33.setCellStyle(cell_style);
				}
				
				
				Cell c34 = row.createCell(columnIndex++);
				c34.setCellValue(vo.getR20101()==null||vo.getR20101()==2 ? "未发现风险" : "存在风险");
				c34.setCellStyle(cellStyle);
				if(vo.getR20101() !=null && vo.getR20101()==1){
					c34.setCellStyle(cell_style);
				}
				
				Cell c35 = row.createCell(columnIndex++);
				c35.setCellValue(vo.getR20102()==null||vo.getR20102()==2 ? "未发现风险" : "存在风险");
				c35.setCellStyle(cellStyle);
				if(vo.getR20102() !=null && vo.getR20102()==1){
					c35.setCellStyle(cell_style);
				}
				
				Cell c36 = row.createCell(columnIndex++);
				c36.setCellValue(vo.getR20103()==null||vo.getR20103()==2 ? "未发现风险" : "存在风险");
				c36.setCellStyle(cellStyle);
				if(vo.getR20103() !=null && vo.getR20103()==1){
					c36.setCellStyle(cell_style);
				}
				
				Cell c37 = row.createCell(columnIndex++);
				c37.setCellValue(vo.getR20104()==null||vo.getR20104()==2 ? "未发现风险" : "存在风险");
				c37.setCellStyle(cellStyle);
				if(vo.getR20104() !=null && vo.getR20104()==1){
					c37.setCellStyle(cell_style);
				}
				
				Cell c38 = row.createCell(columnIndex++);
				c38.setCellValue(vo.getR20105()==null||vo.getR20105()==2 ? "未发现风险" : "存在风险");
				c38.setCellStyle(cellStyle);
				if(vo.getR20105() !=null && vo.getR20105()==1){
					c38.setCellStyle(cell_style);
				}
				
				if (!terminalTypeEnum.isApplet()) {
					Cell c39 = row.createCell(columnIndex++);
					c39.setCellValue(vo.getR20106()==null||vo.getR20106()==2 ? "未发现风险" : "存在风险");
					c39.setCellStyle(cellStyle);
					if(vo.getR20106() !=null && vo.getR20106()==1){
						c39.setCellStyle(cell_style);
					}
				}
				
				Cell c40 = row.createCell(columnIndex++);
				c40.setCellValue(vo.getR20107()==null||vo.getR20107()==2 ? "未发现风险" : "存在风险");
				c40.setCellStyle(cellStyle);
				if(vo.getR20107() !=null && vo.getR20107()==1){
					c40.setCellStyle(cell_style);
				}
				
				Cell c41 = row.createCell(columnIndex++);
				c41.setCellValue(vo.getR20201()==null||vo.getR20201()==2 ? "未发现风险" : "存在风险");
				c41.setCellStyle(cellStyle);
				if(vo.getR20201() !=null && vo.getR20201()==1){
					c41.setCellStyle(cell_style);
				}
				
				Cell c42 = row.createCell(columnIndex++);
				c42.setCellValue(vo.getR20202()==null||vo.getR20202()==2 ? "未发现风险" : "存在风险");
				c42.setCellStyle(cellStyle);
				if(vo.getR20202() !=null && vo.getR20202()==1){
					c42.setCellStyle(cell_style);
				}
				
				Cell c43 = row.createCell(columnIndex++);
				c43.setCellValue(vo.getR20203()==null || vo.getR20203()==2 ? "未发现风险" : "存在风险");
				c43.setCellStyle(cellStyle);
				if(vo.getR20203() !=null && vo.getR20203()==1){
					c43.setCellStyle(cell_style);
				}
				Cell e1 = row.createCell(columnIndex++);
				e1.setCellValue(vo.getR110101()==null || vo.getR110101()==2 ? "未发现风险" : "存在风险");
				e1.setCellStyle(cellStyle);
				if(vo.getR110101() !=null && vo.getR110101()==1){
					e1.setCellStyle(cell_style);
				}

				Cell e2 = row.createCell(columnIndex++);
				e2.setCellValue(vo.getR110201()==null || vo.getR110201()==2 ? "未发现风险" : "存在风险");
				e2.setCellStyle(cellStyle);
				if(vo.getR110201() !=null && vo.getR110201()==1){
					e2.setCellStyle(cell_style);
				}

				Cell e3 = row.createCell(columnIndex++);
				e3.setCellValue(vo.getR110401()==null || vo.getR110401()==2 ? "未发现风险" : "存在风险");
				e3.setCellStyle(cellStyle);
				if(vo.getR110401() !=null && vo.getR110401()==1){
					e3.setCellStyle(cell_style);
				}

				Cell e4 = row.createCell(columnIndex++);
				e4.setCellValue(vo.getR120101()==null || vo.getR120101()==2 ? "未发现风险" : "存在风险");
				e4.setCellStyle(cellStyle);
				if(vo.getR120101() !=null && vo.getR120101()==1){
					e4.setCellStyle(cell_style);
				}

				Cell e5 = row.createCell(columnIndex++);
				e5.setCellValue(vo.getR120301()==null || vo.getR120301()==2 ? "未发现风险" : "存在风险");
				e5.setCellStyle(cellStyle);
				if(vo.getR120301() !=null && vo.getR120301()==1){
					e5.setCellStyle(cell_style);
				}


				Cell e6 = row.createCell(columnIndex++);
				e6.setCellValue(vo.getR130101()==null || vo.getR130101()==2 ? "未发现风险" : "存在风险");
				e6.setCellStyle(cellStyle);
				if(vo.getR130101() !=null && vo.getR130101()==1){
					e6.setCellStyle(cell_style);
				}


				Cell e7 = row.createCell(columnIndex++);
				e7.setCellValue(vo.getR130301()==null || vo.getR130301()==2 ? "未发现风险" : "存在风险");
				e7.setCellStyle(cellStyle);
				if(vo.getR130301() !=null && vo.getR130301()==1){
					e6.setCellStyle(cell_style);
				}



				Cell e8 = row.createCell(columnIndex++);
				e8.setCellValue(vo.getR130401()==null || vo.getR130401()==2 ? "未发现风险" : "存在风险");
				e8.setCellStyle(cellStyle);
				if(vo.getR130401() !=null && vo.getR130401()==1){
					e8.setCellStyle(cell_style);
				}

				Cell e9 = row.createCell(columnIndex++);
				e9.setCellValue(vo.getR130501()==null || vo.getR130501()==2 ? "未发现风险" : "存在风险");
				e9.setCellStyle(cellStyle);
				if(vo.getR130501() !=null && vo.getR130501()==1){
					e9.setCellStyle(cell_style);
				}

				Cell e9_1 = row.createCell(columnIndex++);
				e9_1.setCellValue(vo.getR130801()==null || vo.getR130801()==2 ? "未发现风险" : "存在风险");
				e9_1.setCellStyle(cellStyle);
				if(vo.getR130801() !=null && vo.getR130801()==1){
					e9_1.setCellStyle(cell_style);
				}

				Cell e10 = row.createCell(columnIndex++);
				e10.setCellValue(vo.getR140101()==null || vo.getR140101()==2 ? "未发现风险" : "存在风险");
				e10.setCellStyle(cellStyle);
				if(vo.getR140101() !=null && vo.getR140101()==1){
					e10.setCellStyle(cell_style);
				}

				Cell e11 = row.createCell(columnIndex++);
				e11.setCellValue(vo.getR140201()==null || vo.getR140201()==2 ? "未发现风险" : "存在风险");
				e11.setCellStyle(cellStyle);
				if(vo.getR140201() !=null && vo.getR140201()==1){
					e11.setCellStyle(cell_style);
				}

				Cell e12 = row.createCell(columnIndex++);
				e12.setCellValue(vo.getR140401()==null || vo.getR140401()==2 ? "未发现风险" : "存在风险");
				e12.setCellStyle(cellStyle);
				if(vo.getR140401() !=null && vo.getR140401()==1){
					e12.setCellStyle(cell_style);
				}

				Cell e13 = row.createCell(columnIndex++);
				e13.setCellValue(vo.getR140303()==null || vo.getR140303()==2 ? "未发现风险" : "存在风险");
				e13.setCellStyle(cellStyle);
				if(vo.getR140303() !=null && vo.getR140303()==1){
					e13.setCellStyle(cell_style);
				}

				Cell e14 = row.createCell(columnIndex++);
				e14.setCellValue(vo.getR140304()==null || vo.getR140304()==2 ? "未发现风险" : "存在风险");
				e14.setCellStyle(cellStyle);
				if(vo.getR140304() !=null && vo.getR140304()==1){
					e14.setCellStyle(cell_style);
				}

				Cell e15 = row.createCell(columnIndex++);
				e15.setCellValue(vo.getR150101()==null || vo.getR150101()==2 ? "未发现风险" : "存在风险");
				e15.setCellStyle(cellStyle);
				if(vo.getR150101() !=null && vo.getR150101()==1){
					e15.setCellStyle(cell_style);
				}

				Cell e16 = row.createCell(columnIndex++);
				e16.setCellValue(vo.getR160101()==null || vo.getR160101()==2 ? "未发现风险" : "存在风险");
				e16.setCellStyle(cellStyle);
				if(vo.getR160101() !=null && vo.getR160101()==1){
					e16.setCellStyle(cell_style);
				}


				Cell e17 = row.createCell(columnIndex++);
				e17.setCellValue(vo.getR160301()==null || vo.getR160301()==2 ? "未发现风险" : "存在风险");
				e17.setCellStyle(cellStyle);
				if(vo.getR160301() !=null && vo.getR160301()==1){
					e17.setCellStyle(cell_style);
				}
				// 写入35273的内容
				writeLawExcel(columnIndex, vo.getLaw35273List(), row, cellStyle, cell_style);
				// 写入41391的内容
				writeLawExcel(terminalTypeEnum.isApplet() ? columnIndex + 13 : columnIndex + 14, vo.getLaw41391List(), row, cellStyle, cell_style);
			}
			// 创建文件输出流，准备输出电子表格：这个必须有，否则你在sheet上做的任何操作都不会有效
			out = new FileOutputStream(finalXlsxPath);
			workBook.write(out);
			workBook.close();
		} catch (Exception e) {
			e.getMessage();
		} finally {
			try {
				if (out != null) {
					out.flush();
					out.close();
				}
			} catch (IOException e) {
				e.getMessage();
			}
		}
		LOG.info("数据导出成功.finalXlsxPath={}",finalXlsxPath);
	}

	private static void writeStringAppInfo(Cell c4, CellStyle style, String value) {
		c4.setCellValue(StringUtils.isEmpty(value) ? EXCEL_EMPTY : value);
		c4.setCellStyle(style);
	}

	private static void writeLawExcel(int start, List<PrivacyLawsResult> resultList, Row row, CellStyle involvedCellStyle, CellStyle nonComplianceCellStyle) {
		for (int i=0; i<resultList.size(); i++) {
			PrivacyLawsResult result = resultList.get(i);
			Cell e1 = row.createCell(start + i);
			if (result.getResultStatus() == null || result.getResultStatus() == LawResultStatusEnum.NON_INVOLVED.getValue()) {
				e1.setCellValue("未发现风险");
				e1.setCellStyle(involvedCellStyle);
			} else if(result.getResultStatus() == LawResultStatusEnum.NON_COMPLIANCE.getValue()){
				e1.setCellStyle(nonComplianceCellStyle);
				e1.setCellValue("存在风险");
			}
		}
	}

	/**
	 * 判断Excel的版本,获取Workbook
	 * 
	 * @param
	 * @param
	 * @return
	 * @throws IOException
	 */
	public static Workbook getWorkbok(File file) throws IOException {
		Workbook wb = null;
		FileInputStream in = new FileInputStream(file);
		if (file.getName().endsWith(EXCEL_XLS)) { // Excel&nbsp;2003
			wb = new HSSFWorkbook(in);
		} else if (file.getName().endsWith(EXCEL_XLSX)) { // Excel 2007/2010
			wb = new XSSFWorkbook(in);
		}
		return wb;
	}
	
	public static XSSFWorkbook getHSSFWorkbok(File file) throws IOException {
		XSSFWorkbook wb = null;
		FileInputStream in = new FileInputStream(file);
		if (file.getName().endsWith(EXCEL_XLS)) { // Excel&nbsp;2003
			wb = new XSSFWorkbook(in);
		} else if (file.getName().endsWith(EXCEL_XLSX)) { // Excel 2007/2010
			wb = new XSSFWorkbook(in);
		}
		return wb;
	}
	
	public static void writeExcelMisjudgment01(List<SensitiveWordExcelReportVO> dataList, String finalXlsxPath) {
		if(dataList==null || dataList.size()==0) {
			return;
		}
		OutputStream out = null;
		try {
			// 读取Excel文档
			File finalXlsxFile = new File(finalXlsxPath);
			Workbook workBook = getWorkbok(finalXlsxFile);
			// sheet 对应一个工作页
			Sheet sheet = workBook.getSheetAt(0);

			// 创建单元格样式
			CellStyle cell_style = workBook.createCellStyle();
			cell_style.setBorderBottom(BorderStyle.THIN); //下边框
			cell_style.setBorderLeft(BorderStyle.THIN);//左边框
			cell_style.setBorderTop(BorderStyle.THIN);//上边框
			cell_style.setBorderRight(BorderStyle.THIN);//右边框
			// 自定义字体颜色, 同单元格样式
			Font font = workBook.createFont();
			font.setFontName("微软雅黑");
			font.setColor(IndexedColors.RED.getIndex());
			cell_style.setFont(font);
			
			
			CellStyle cell_style1 = workBook.createCellStyle();
			cell_style1.setBorderBottom(BorderStyle.THIN); //下边框
			cell_style1.setBorderLeft(BorderStyle.THIN);//左边框
			cell_style1.setBorderTop(BorderStyle.THIN);//上边框
			cell_style1.setBorderRight(BorderStyle.THIN);//右边框
			// 自定义字体颜色, 同单元格样式
			Font font1 = workBook.createFont();
			font1.setFontName("微软雅黑");
			cell_style1.setFont(font1);

			/**
			 * 往Excel中写新数据
			 */
			for (int j = 0; j < dataList.size(); j++) {
				// 创建一行：从第二行开始，跳过属性列
				Row row = sheet.createRow(j + 1);
				// 得到要插入的每一条记录
				SensitiveWordExcelReportVO vo = dataList.get(j);

				// 在一行内循环
				Cell first = row.createCell(0);
				first.setCellValue(vo.getTaskId());
				first.setCellStyle(cell_style1);

				Cell second = row.createCell(1);
				second.setCellValue(vo.getAppName() == null ? "" : vo.getAppName());
				second.setCellStyle(cell_style1);

				Cell third = row.createCell(2);
				third.setCellValue(vo.getPakage() == null ? "" : vo.getPakage());
				third.setCellStyle(cell_style1);

				Cell c3 = row.createCell(3);
				c3.setCellValue(vo.getPath() == null ? "" : vo.getPath());
				c3.setCellStyle(cell_style1);
				
				Cell c4 = row.createCell(4);
				c4.setCellValue(vo.getBehaviorStage()==null?"":BehaviorStageEnum.getItem(vo.getBehaviorStage()).getName());
				c4.setCellStyle(cell_style1);
				
				Cell c5 = row.createCell(5);
				c5.setCellValue(vo.getActionTime()==null?"":DateUtils.getDateFormat(vo.getActionTime(), FULL_DATA_FORMAT));
				c5.setCellStyle(cell_style1);
				
				Cell c6 = row.createCell(6);
				c6.setCellValue(vo.getExecutorType()==1?"APP":"SDK");//1APP 2SDK
				c6.setCellStyle(cell_style1);
				
				Cell c7 = row.createCell(7);
				c7.setCellValue(vo.getExecutor()==null? "" : vo.getExecutor());
				c7.setCellStyle(cell_style1);
				
				Cell c71 = row.createCell(8);
				c71.setCellValue(vo.getActionPackageName()==null? "" : vo.getActionPackageName());
				c71.setCellStyle(cell_style1);
				
				Cell c8 = row.createCell(9);
				c8.setCellValue(vo.getTypeName()==null?"":vo.getTypeName());
				c8.setCellStyle(cell_style1);
				
				Cell c9 = row.createCell(10);
				c9.setCellValue(vo.getName()==null?"":vo.getName());
				c9.setCellStyle(cell_style1);
				
				Cell c91 = row.createCell(11);
				c91.setCellValue(vo.getIp()==null?"":vo.getIp());
				c91.setCellStyle(cell_style1);
				
				Cell c10 = row.createCell(12);
				c10.setCellValue(vo.getPort()==null?"":vo.getPort());
				c10.setCellStyle(cell_style1);
				
				Cell c11 = row.createCell(13);
				c11.setCellValue(vo.getHost()==null?"":vo.getHost());
				c11.setCellStyle(cell_style1);
				
				Cell c111 = row.createCell(14);
				c111.setCellValue(vo.getAddress()==null?"":vo.getAddress());
				c111.setCellStyle(cell_style1);
				
				Cell c12 = row.createCell(15);
				c12.setCellValue(vo.getProtocol()==null?"":vo.getProtocol());
				c12.setCellStyle(cell_style1);
				
				Cell c13 = row.createCell(16);
				c13.setCellValue(vo.getMethod()==null? "" : vo.getMethod());
				c13.setCellStyle(cell_style1);
				
				Cell c14 = row.createCell(17);
				c14.setCellValue(vo.getUrl()==null ? "" : vo.getUrl());
				c14.setCellStyle(cell_style1);
				
				Cell c15 = row.createCell(18);
				c15.setCellValue(vo.getSensitiveWord()==null ? "" : vo.getSensitiveWord());
				c15.setCellStyle(cell_style1);
				
				Cell c16 = row.createCell(19);
				c16.setCellValue(vo.getCode()==null ? "" : vo.getCode());
				c16.setCellStyle(cell_style1);
				
				Cell c17 = row.createCell(20);
				c17.setCellValue(vo.getDetailsData()==null  ? new XSSFRichTextString("") : new XSSFRichTextString(vo.getDetailsData()));
				c17.setCellStyle(cell_style1);
				
				System.out.println(vo.getStackInfo().length());
				
				Cell c18 = row.createCell(21);
				c18.setCellValue(vo.getStackInfo() == null  ? new XSSFRichTextString("") : new XSSFRichTextString(vo.getStackInfo()));
				c18.setCellStyle(cell_style1);
			}
			// 创建文件输出流，准备输出电子表格：这个必须有，否则你在sheet上做的任何操作都不会有效
			out = new FileOutputStream(finalXlsxPath);
			workBook.write(out);
			workBook.close();
		} catch (Exception e) {
			e.getMessage();
		} finally {
			try {
				if (out != null) {
					out.flush();
					out.close();
				}
			} catch (IOException e) {
				e.getMessage();
			}
		}
		LOG.info("数据导出成功.finalXlsxPath={}",finalXlsxPath);
	}
	
	public static void writeExcelMisjudgment02(List<SharedPrefsExcelReportVO> dataList, String finalXlsxPath) {
		if(dataList==null || dataList.size()==0) {
			return;
		}
		OutputStream out = null;
		try {
			// 读取Excel文档
			File finalXlsxFile = new File(finalXlsxPath);
			Workbook workBook = getWorkbok(finalXlsxFile);
			// sheet 对应一个工作页
			Sheet sheet = workBook.getSheetAt(0);

			// 创建单元格样式
			CellStyle cell_style = workBook.createCellStyle();
			cell_style.setBorderBottom(BorderStyle.THIN); //下边框
			cell_style.setBorderLeft(BorderStyle.THIN);//左边框
			cell_style.setBorderTop(BorderStyle.THIN);//上边框
			cell_style.setBorderRight(BorderStyle.THIN);//右边框
			// 自定义字体颜色, 同单元格样式
			Font font = workBook.createFont();
			font.setFontName("微软雅黑");
			font.setColor(IndexedColors.RED.getIndex());
			cell_style.setFont(font);
			
			
			CellStyle cell_style1 = workBook.createCellStyle();
			cell_style1.setBorderBottom(BorderStyle.THIN); //下边框
			cell_style1.setBorderLeft(BorderStyle.THIN);//左边框
			cell_style1.setBorderTop(BorderStyle.THIN);//上边框
			cell_style1.setBorderRight(BorderStyle.THIN);//右边框
			// 自定义字体颜色, 同单元格样式
			Font font1 = workBook.createFont();
			font1.setFontName("微软雅黑");
			cell_style1.setFont(font1);

			/**
			 * 往Excel中写新数据
			 */
			for (int j = 0; j < dataList.size(); j++) {
				// 创建一行：从第二行开始，跳过属性列
				Row row = sheet.createRow(j + 1);
				// 得到要插入的每一条记录
				SharedPrefsExcelReportVO vo = dataList.get(j);

				// 在一行内循环
				Cell first = row.createCell(0);
				first.setCellValue(vo.getTaskId());
				first.setCellStyle(cell_style1);

				Cell second = row.createCell(1);
				second.setCellValue(vo.getAppName() == null ? "" : vo.getAppName());
				second.setCellStyle(cell_style1);

				Cell third = row.createCell(2);
				third.setCellValue(vo.getPakage() == null ? "" : vo.getPakage());
				third.setCellStyle(cell_style1);

				Cell c3 = row.createCell(3);
				c3.setCellValue(vo.getAppPath() == null ? "" : vo.getAppPath());
				c3.setCellStyle(cell_style1);
				
				Cell c4 = row.createCell(4);
				c4.setCellValue(vo.getBehaviorStage()==null?"":BehaviorStageEnum.getItem(vo.getBehaviorStage()).getName());
				c4.setCellStyle(cell_style1);
				
				Cell c5 = row.createCell(5);
				c5.setCellValue(vo.getActionTime()==null?"":DateUtils.getDateFormat(vo.getActionTime(), FULL_DATA_FORMAT));
				c5.setCellStyle(cell_style1);
				
				Cell c6 = row.createCell(6);
				c6.setCellValue(vo.getExecutorType()==1?"APP":"SDK");//1APP 2SDK
				c6.setCellStyle(cell_style1);
				
				Cell c7 = row.createCell(7);
				c7.setCellValue(vo.getExecutor()==null? "" : vo.getExecutor());
				c7.setCellStyle(cell_style1);
				
				Cell c71 = row.createCell(8);
				c71.setCellValue(vo.getActionPackageName()==null? "" : vo.getActionPackageName());
				c71.setCellStyle(cell_style1);
				
				Cell c8 = row.createCell(9);
				c8.setCellValue(vo.getTypeName()==null?"":vo.getTypeName());
				c8.setCellStyle(cell_style1);
				
				Cell c9 = row.createCell(10);
				c9.setCellValue(vo.getName()==null?"":vo.getName());
				c9.setCellStyle(cell_style1);
				
				Cell c15 = row.createCell(11);
				c15.setCellValue(vo.getSensitiveWord()==null ? "" : vo.getSensitiveWord());
				c15.setCellStyle(cell_style1);
				
				Cell c16 = row.createCell(12);
				c16.setCellValue(vo.getPath()==null ? "" : vo.getPath());
				c16.setCellStyle(cell_style1);
				
				Cell c17 = row.createCell(13);
				c17.setCellValue(vo.getContent()==null  ? "" : vo.getContent());
				c17.setCellStyle(cell_style1);
				
				Cell c18 = row.createCell(14);
				c18.setCellValue(vo.getStackInfo() == null  ? "" : vo.getStackInfo());
				c18.setCellStyle(cell_style1);
			}
			// 创建文件输出流，准备输出电子表格：这个必须有，否则你在sheet上做的任何操作都不会有效
			out = new FileOutputStream(finalXlsxPath);
			workBook.write(out);
			workBook.close();
		} catch (Exception e) {
			e.getMessage();
		} finally {
			try {
				if (out != null) {
					out.flush();
					out.close();
				}
			} catch (IOException e) {
				e.getMessage();
			}
		}
		LOG.info("数据导出成功.finalXlsxPath={}",finalXlsxPath);
	}
	
	public static void writeExcelMisjudgment03(List<PrivacyLawsMisjudgmentVO> dataList, String finalXlsxPath) {
		if(dataList==null || dataList.size()==0) {
			return;
		}
		OutputStream out = null;
		try {
			// 读取Excel文档
			File finalXlsxFile = new File(finalXlsxPath);
			Workbook workBook = getWorkbok(finalXlsxFile);
			// sheet 对应一个工作页
			Sheet sheet = workBook.getSheetAt(0);

			// 创建单元格样式
			CellStyle cell_style = workBook.createCellStyle();
			cell_style.setBorderBottom(BorderStyle.THIN); //下边框
			cell_style.setBorderLeft(BorderStyle.THIN);//左边框
			cell_style.setBorderTop(BorderStyle.THIN);//上边框
			cell_style.setBorderRight(BorderStyle.THIN);//右边框
			// 自定义字体颜色, 同单元格样式
			Font font = workBook.createFont();
			font.setFontName("微软雅黑");
			font.setColor(IndexedColors.RED.getIndex());
			cell_style.setFont(font);
			
			
			CellStyle cell_style1 = workBook.createCellStyle();
			cell_style1.setBorderBottom(BorderStyle.THIN); //下边框
			cell_style1.setBorderLeft(BorderStyle.THIN);//左边框
			cell_style1.setBorderTop(BorderStyle.THIN);//上边框
			cell_style1.setBorderRight(BorderStyle.THIN);//右边框
			// 自定义字体颜色, 同单元格样式
			Font font1 = workBook.createFont();
			font1.setFontName("微软雅黑");
			cell_style1.setFont(font1);

			/**
			 * 往Excel中写新数据
			 */
			for (int j = 0; j < dataList.size(); j++) {
				// 创建一行：从第二行开始，跳过属性列
				Row row = sheet.createRow(j + 1);
				// 得到要插入的每一条记录
				PrivacyLawsMisjudgmentVO vo = dataList.get(j);

				// 在一行内循环
				Cell first = row.createCell(0);
				first.setCellValue(vo.getTaskId());
				first.setCellStyle(cell_style1);

				Cell second = row.createCell(1);
				second.setCellValue(vo.getName() == null ? "" : vo.getName());
				second.setCellStyle(cell_style1);
				
				Cell c5 = row.createCell(2);
				c5.setCellValue(vo.getPakage()==null?"":vo.getPakage());
				c5.setCellStyle(cell_style1);

				Cell third = row.createCell(3);
				third.setCellValue(vo.getVersion() == null ? "" : vo.getVersion());
				third.setCellStyle(cell_style1);

				Cell c3 = row.createCell(4);
				c3.setCellValue(vo.getTerminalType() == null || vo.getTerminalType()==1 ? "Android" : "iOS");
				c3.setCellStyle(cell_style1);
				
				Cell c4 = row.createCell(5);
				c4.setCellValue(vo.getPath()==null?"":vo.getPath());
				c4.setCellStyle(cell_style1);
				
				Cell c6 = row.createCell(6);
				c6.setCellValue(vo.getTitle()==null?"":vo.getTitle());
				c6.setCellStyle(cell_style1);
				
			}
			// 创建文件输出流，准备输出电子表格：这个必须有，否则你在sheet上做的任何操作都不会有效
			out = new FileOutputStream(finalXlsxPath);
			workBook.write(out);
			workBook.close();
		} catch (Exception e) {
			e.getMessage();
		} finally {
			try {
				if (out != null) {
					out.flush();
					out.close();
				}
			} catch (IOException e) {
				e.getMessage();
			}
		}
		LOG.info("数据导出成功.finalXlsxPath={}",finalXlsxPath);
	}
	
	
	public static void writeExcelMisjudgment04(List<SuspiciousSdkDataVO> dataList, String finalXlsxPath) {
		if(dataList==null || dataList.size()==0) {
			return;
		}
		OutputStream out = null;
		try {
			// 读取Excel文档
			File finalXlsxFile = new File(finalXlsxPath);
			Workbook workBook = getWorkbok(finalXlsxFile);
			// sheet 对应一个工作页
			Sheet sheet = workBook.getSheetAt(0);

			// 创建单元格样式
			CellStyle cell_style = workBook.createCellStyle();
			cell_style.setBorderBottom(BorderStyle.THIN); //下边框
			cell_style.setBorderLeft(BorderStyle.THIN);//左边框
			cell_style.setBorderTop(BorderStyle.THIN);//上边框
			cell_style.setBorderRight(BorderStyle.THIN);//右边框
			// 自定义字体颜色, 同单元格样式
			Font font = workBook.createFont();
			font.setFontName("微软雅黑");
			font.setColor(IndexedColors.RED.getIndex());
			cell_style.setFont(font);
			
			
			CellStyle cell_style1 = workBook.createCellStyle();
			cell_style1.setBorderBottom(BorderStyle.THIN); //下边框
			cell_style1.setBorderLeft(BorderStyle.THIN);//左边框
			cell_style1.setBorderTop(BorderStyle.THIN);//上边框
			cell_style1.setBorderRight(BorderStyle.THIN);//右边框
			// 自定义字体颜色, 同单元格样式
			Font font1 = workBook.createFont();
			font1.setFontName("微软雅黑");
			cell_style1.setFont(font1);

			/**
			 * 往Excel中写新数据
			 */
			for (int j = 0; j < dataList.size(); j++) {
				// 创建一行：从第二行开始，跳过属性列
				Row row = sheet.createRow(j + 1);
				// 得到要插入的每一条记录
				SuspiciousSdkDataVO vo = dataList.get(j);

				// 在一行内循环
				Cell first = row.createCell(0);
				first.setCellValue(vo.getPackageName());
				first.setCellStyle(cell_style1);

				Cell second = row.createCell(1);
				second.setCellValue(vo.getPermissionName() == null ? "" : vo.getPermissionName());
				second.setCellStyle(cell_style1);

				Cell third = row.createCell(2);
				third.setCellValue(vo.getNumber() == null ? 0 : vo.getNumber());
				third.setCellStyle(cell_style1);

				Cell c3 = row.createCell(3);
				c3.setCellValue(vo.getTaskIds() == null ? "" : vo.getTaskIds());
				c3.setCellStyle(cell_style1);
				
			}
			// 创建文件输出流，准备输出电子表格：这个必须有，否则你在sheet上做的任何操作都不会有效
			out = new FileOutputStream(finalXlsxPath);
			workBook.write(out);
			workBook.close();
		} catch (Exception e) {
			e.getMessage();
		} finally {
			try {
				if (out != null) {
					out.flush();
					out.close();
				}
			} catch (IOException e) {
				e.getMessage();
			}
		}
		LOG.info("数据导出成功.finalXlsxPath={}", finalXlsxPath);
	}

	//应用行为数据
	public static void writeExcelMisjudgmentLawActionData(FetchExcelData<ExcelReportPrivacyLawsDetailVO> fetchExcelData,
														  TTask task, Workbook workBook, ReadImage readImage) {
		String sheetName = "违规项&应用行为";
		ExcelData<ExcelReportPrivacyLawsDetailVO> data = fetchExcelData.load(1, EXCEL_ACTION_DATA_PAGE_SIZE);
		int sheetIndex = workBook.getSheetIndex(sheetName);
		if (sheetIndex < 0) {
			LOG.error("sheetName={} 不存在", sheetName);
			return;
		}
		if (CollectionUtils.isEmpty(data.dataList)) {
			// 没有数据，删掉这个
			workBook.removeSheetAt(sheetIndex);
			return;
		}
		try {
			// sheet 对应一个工作页
			Sheet sheet = workBook.getSheetAt(sheetIndex);  //第一个表格

			// 创建单元格样式
			CellStyle alertStyle = workBook.createCellStyle();
			alertStyle.setBorderBottom(BorderStyle.THIN); //下边框
			alertStyle.setBorderLeft(BorderStyle.THIN);//左边框
			alertStyle.setBorderTop(BorderStyle.THIN);//上边框
			alertStyle.setBorderRight(BorderStyle.THIN);//右边框
			// 自定义字体颜色, 同单元格样式
			Font font = workBook.createFont();
			font.setFontName("微软雅黑");
			font.setColor(IndexedColors.RED.getIndex());
			alertStyle.setFont(font);


			CellStyle normalStyle = workBook.createCellStyle();
			normalStyle.setBorderBottom(BorderStyle.THIN); //下边框
			normalStyle.setBorderLeft(BorderStyle.THIN);//左边框
			normalStyle.setBorderTop(BorderStyle.THIN);//上边框
			normalStyle.setBorderRight(BorderStyle.THIN);//右边框
			// 自定义字体颜色, 同单元格样式
			Font font1 = workBook.createFont();
			font1.setFontName("微软雅黑");
			normalStyle.setFont(font1);

			CellStyle cellStyleLink = workBook.createCellStyle();
			cellStyleLink.setBorderBottom(BorderStyle.THIN); //下边框
			cellStyleLink.setBorderLeft(BorderStyle.THIN);//左边框
			cellStyleLink.setBorderTop(BorderStyle.THIN);//上边框
			cellStyleLink.setBorderRight(BorderStyle.THIN);//右边框
			// 自定义字体颜色, 同单元格样式
			Font fontLink = workBook.createFont();
			fontLink.setFontName("微软雅黑");
			fontLink.setColor(IndexedColors.BLUE.getIndex());
			cellStyleLink.setFont(fontLink);
			int total = 0;
			while (!CollectionUtils.isEmpty(data.dataList)) {
				/**
				 * 往Excel中写新数据
				 */
				for (int j = 0; j < data.dataList.size(); j++) {
					// 创建一行：从第二行开始，跳过属性列
					int rowIndex = j + total + 1;
					Row row = sheet.createRow(rowIndex);
					// 得到要插入的每一条记录
					ExcelReportPrivacyLawsDetailVO vo = data.dataList.get(j);

					// 在一行内循环
					Cell first = row.createCell(0);
					first.setCellValue(rowIndex - 1);
					first.setCellStyle(normalStyle);

					Cell second = row.createCell(1);
					second.setCellValue(vo.getRootLawsName() == null ? "" : vo.getRootLawsName());
					second.setCellStyle(normalStyle);

					Cell third = row.createCell(2);
					third.setCellValue(vo.getParentLawsName() == null ? "" : vo.getParentLawsName());
					third.setCellStyle(normalStyle);

					Cell c3_1 = row.createCell(3);
					c3_1.setCellValue(vo.getItem().getName());
					c3_1.setCellStyle(normalStyle);

					Cell c4 = row.createCell(4);
					c4.setCellValue("存在风险");
					c4.setCellStyle(normalStyle);

					Cell c5 = row.createCell(5);
					c5.setCellValue((vo.getItem().getRiskLevel().description));
					c5.setCellStyle(normalStyle);

					Cell c6 = row.createCell(6);
					c6.setCellValue((vo.getItem().getConclusion()));
					c6.setCellStyle(normalStyle);

					Cell c7 = row.createCell(7);
					c7.setCellValue((vo.getItem().getSuggestion()));
					c7.setCellStyle(normalStyle);

					LawActionDetailVO detailVO = vo.getDetailVO();
					TPrivacyLawsDetail imageVO = vo.getImageVO();
					if (detailVO != null) {
						if (detailVO.getDataType() == LawDetailDataTypeEnum.ACTION.getValue()) {
							fillExcelAction(row, alertStyle, normalStyle, detailVO, task);
						} else if (detailVO.getDataType() == LawDetailDataTypeEnum.TRANSMISSION.getValue()) {
							CreationHelper creationHelper = workBook.getCreationHelper();
							Hyperlink hyperlink = creationHelper.createHyperlink(HyperlinkType.DOCUMENT);
							hyperlink.setAddress("'传输个人信息'!A1"); // 跳转到传输个人信息的A1单元格
							Cell c8 = row.createCell(8);
							c8.setCellValue("详情请查看《传输个人信息》");
							c8.setCellStyle(cellStyleLink);
							c8.setHyperlink(hyperlink);
						} else if (detailVO.getDataType() == LawDetailDataTypeEnum.SHARED_PREFS.getValue()) {
							CreationHelper creationHelper = workBook.getCreationHelper();
							Hyperlink hyperlink = creationHelper.createHyperlink(HyperlinkType.DOCUMENT);
							hyperlink.setAddress("'存储个人信息'!A1"); // 跳转到存储个人信息的A1单元格
							Cell c8 = row.createCell(8);
							c8.setCellValue("详情请查看《存储个人信息》");
							c8.setCellStyle(cellStyleLink);
							c8.setHyperlink(hyperlink);
						} else if (detailVO.getDataType() == LawDetailDataTypeEnum.OUTSIDE_ADDRESS.getValue()) {
							CreationHelper creationHelper = workBook.getCreationHelper();
							Hyperlink hyperlink = creationHelper.createHyperlink(HyperlinkType.DOCUMENT);
							hyperlink.setAddress("'通信行为'!A1"); // 跳转到通信行为的A1单元格
							Cell c8 = row.createCell(8);
							c8.setCellValue("详情请查看《通信行为》");
							c8.setCellStyle(cellStyleLink);
							c8.setHyperlink(hyperlink);
						}
					} else if (imageVO != null) {
						byte[] imageBytes = readImage.imageDataByFileKey(imageVO.getFileKey());
						if (imageBytes.length == 0) {
							LOG.info("图片无法显示 fileKey={}", imageVO.getFileKey());
							Cell c8 = row.createCell(8);
							c8.setCellValue("[图片无法显示]");
							c8.setCellStyle(alertStyle);
						} else {
							int pictureIdx = workBook.addPicture(imageBytes, Workbook.PICTURE_TYPE_JPEG);
							// 设置图片的固定高度和宽度
							pictureToSheet(sheet, row, pictureIdx, 8, rowIndex);
						}
						fillEmptyAction(row, normalStyle, 9, 19);
					} else {
						fillEmptyAction(row, normalStyle, 8, 19);
					}
				}
				total += data.dataList.size();
				data = fetchExcelData.load(data.pageNum + 1, EXCEL_ACTION_DATA_PAGE_SIZE);
			}
		} catch (Exception e) {
			e.getMessage();
		}
		LOG.info("应用行为数据写入表格成功.");
	}

	public static void writeExcelAssetsStatisticsDetail(List<AssetsStatisticsDetail> dataList, Workbook workBook) {
		if (dataList == null || dataList.size() == 0) {
			return;
		}
		try {
			// sheet 对应一个工作页
			Sheet sheet = workBook.createSheet("检测数据统计表");

			CellStyle titleCell = titleCell(workBook);
			CellStyle normalCell = workBook.createCellStyle();
			normalCell.setBorderBottom(BorderStyle.THIN); //下边框
			normalCell.setBorderLeft(BorderStyle.THIN);//左边框
			normalCell.setBorderTop(BorderStyle.THIN);//上边框
			normalCell.setBorderRight(BorderStyle.THIN);//右边框
			// 自定义字体颜色, 同单元格样式
			Font font1 = workBook.createFont();
			font1.setFontName("微软雅黑");
			normalCell.setFont(font1);
			int rowIndex = 0;
			int middleCell = 256 * 15;
			int largeCell = 256 * 22;
			Row statisticRow = createRow(sheet, rowIndex++, normalCell, COLUMN_WIDTH_AUTO);
			setCellContent(0, statisticRow, "应用名称", titleCell, largeCell);
			setCellContent(1, statisticRow, "版本", titleCell, largeCell);
			setCellContent(2, statisticRow, "检测次数", titleCell, middleCell);
			setCellContent(3, statisticRow, "快速检测次数", titleCell, middleCell);
			setCellContent(4, statisticRow, "全部完成", titleCell, middleCell);
			setCellContent(5, statisticRow, "失败中断次数", titleCell, middleCell);
			setCellContent(6, statisticRow, "深度检测次数", titleCell, middleCell);
			setCellContent(7, statisticRow, "全部完成", titleCell, middleCell);
			setCellContent(8, statisticRow, "失败中断次数", titleCell, middleCell);
			setCellContent(9, statisticRow, "最后一次检测时间", titleCell, largeCell);
			for (AssetsStatisticsDetail vo : dataList) {
				if (CollectionUtils.isEmpty(vo.getVersionList())) {
					Row row = createRow(sheet, rowIndex++, normalCell, COLUMN_WIDTH_AUTO);
					writeAssetsDetail(row, normalCell, vo);
				} else {
					for (AssetsStatisticsDetail version : vo.getVersionList()) {
						Row row = createRow(sheet, rowIndex++, normalCell, COLUMN_WIDTH_AUTO);
						writeAssetsDetail(row, normalCell, version);
					}
				}
			}
		} catch (Exception e) {
			e.getMessage();
		}
		LOG.info("数据导出成功.");
	}

	public static void writeAssetsDetail(Row row, CellStyle normalCell, AssetsStatisticsDetail version) {
		setCellContent(0, row, version.getAssetsName(), normalCell);
		setCellContent(1, row, version.getVersion(), normalCell);
		setCellContent(2, row, version.getDetectionCount(), normalCell);
		setCellContent(3, row, version.getFastDetectionCount(), normalCell);
		setCellContent(4, row, version.getFastDetectionCompletions(), normalCell);
		setCellContent(5, row, version.getFastDetectionFailures(), normalCell);
		setCellContent(6, row, version.getDeepDetectionCount(), normalCell);
		setCellContent(7, row, version.getDeepDetectionCompletions(), normalCell);
		setCellContent(8, row, version.getDeepDetectionFailures(), normalCell);
		setCellContent(9, row, DateUtils.getDateFormat(version.getLastDetectionTime(), FULL_DATA_FORMAT), normalCell);
	}

	public static void writeExcelAssetsAutoTaskStatistics(List<AssetsTask> dataList, TerminalTypeEnum terminalTypeEnum, Workbook workBook, String sheetName) {
		if (dataList == null || dataList.size() == 0) {
			return;
		}
		try {
			// sheet 对应一个工作页
			Sheet sheet = workBook.createSheet(sheetName);

			CellStyle titleCell = titleCell(workBook);
			CellStyle normalCell = workBook.createCellStyle();
			normalCell.setBorderBottom(BorderStyle.THIN); //下边框
			normalCell.setBorderLeft(BorderStyle.THIN);//左边框
			normalCell.setBorderTop(BorderStyle.THIN);//上边框
			normalCell.setBorderRight(BorderStyle.THIN);//右边框
			// 自定义字体颜色, 同单元格样式
			Font font1 = workBook.createFont();
			font1.setFontName(FONT_NAME);
			normalCell.setFont(font1);
			int rowIndex = 0;
			int smallCell = 256 * 6;
			int middleCell = 256 * 15;
			int largeCell = 256 * 22;
			Row statisticRow = createRow(sheet, rowIndex++, normalCell, COLUMN_WIDTH_AUTO);
			setCellContent(0, statisticRow, "序号", titleCell, smallCell);
			setCellContent(1, statisticRow, "应用名称", titleCell, largeCell);
			setCellContent(2, statisticRow, "版本", titleCell, largeCell);
			setCellContent(3, statisticRow, "创建者", titleCell, middleCell);
			setCellContent(4, statisticRow, "检测时间", titleCell, largeCell);
			setCellContent(5, statisticRow, "静态检测状态", titleCell, middleCell);
			setCellContent(6, statisticRow, "动态检测状态", titleCell, middleCell);
			setCellContent(7, statisticRow, "上传时间", titleCell, largeCell);
			if (!terminalTypeEnum.isApplet()) {
				setCellContent(8, statisticRow, "应用大小（MB）", titleCell, middleCell);
				setCellContent(9, statisticRow, "应用包名", titleCell, largeCell * 2);
			}
			for (AssetsTask vo : dataList) {
				Row row = createRow(sheet, rowIndex, normalCell, COLUMN_WIDTH_AUTO);
				setCellContent(0, row, rowIndex, normalCell);
				setCellContent(1, row, vo.getAssetsName(), normalCell);
				setCellContent(2, row, vo.getVersion(), normalCell);
				setCellContent(3, row, vo.getCreateUserName(), normalCell);
				setCellContent(4, row, DateUtils.getDateFormat(vo.getDetectionTime(), FULL_DATA_FORMAT), normalCell);
				setCellContent(5, row, vo.getStaticStatus() == null ? "" : vo.getStaticStatus().getName(), normalCell);
				setCellContent(6, row, vo.getDynamicStatus() == null ? "" : vo.getDynamicStatus().getName(), normalCell);
				setCellContent(7, row, DateUtils.getDateFormat(vo.getAssetsCreateTime(), FULL_DATA_FORMAT), normalCell);
				if (!terminalTypeEnum.isApplet()) {
					setCellContent(8, row, vo.getAssetsSize(), normalCell);
					setCellContent(9, row, vo.getPackageName(), normalCell);
				}
				rowIndex += 1;
			}
		} catch (Exception e) {
			e.getMessage();
		}
		LOG.info("数据导出成功.");
	}

	public static void writeExcelAssetsDeepTaskStatistics(List<AssetsTask> dataList, TerminalTypeEnum terminalTypeEnum, Workbook workBook) {
		if (dataList == null || dataList.size() == 0) {
			return;
		}
		try {
			// sheet 对应一个工作页
			Sheet sheet = workBook.createSheet("检测数据-深度检测");

			CellStyle titleCell = titleCell(workBook);
			CellStyle normalCell = workBook.createCellStyle();
			normalCell.setBorderBottom(BorderStyle.THIN); //下边框
			normalCell.setBorderLeft(BorderStyle.THIN);//左边框
			normalCell.setBorderTop(BorderStyle.THIN);//上边框
			normalCell.setBorderRight(BorderStyle.THIN);//右边框
			// 自定义字体颜色, 同单元格样式
			Font font1 = workBook.createFont();
			font1.setFontName(FONT_NAME);
			normalCell.setFont(font1);
			int rowIndex = 0;
			int smallCell = 256 * 6;
			int middleCell = 256 * 15;
			int largeCell = 256 * 22;
			Row statisticRow = createRow(sheet, rowIndex++, normalCell, COLUMN_WIDTH_AUTO);
			setCellContent(0, statisticRow, "序号", titleCell, smallCell);
			setCellContent(1, statisticRow, "应用名称", titleCell, largeCell);
			setCellContent(2, statisticRow, "版本", titleCell, largeCell);
			setCellContent(3, statisticRow, "创建者", titleCell, middleCell);
			setCellContent(4, statisticRow, "检测时间", titleCell, largeCell);
			setCellContent(5, statisticRow, "静态检测状态", titleCell, middleCell);
			setCellContent(6, statisticRow, "动态检测状态", titleCell, middleCell);
			setCellContent(7, statisticRow, "法规检测状态", titleCell, middleCell);
			setCellContent(8, statisticRow, "上传时间", titleCell, largeCell);
			if (!terminalTypeEnum.isApplet()) {
				setCellContent(9, statisticRow, "应用大小（MB）", titleCell, middleCell);
				setCellContent(10, statisticRow, "应用包名", titleCell, largeCell * 2);
			}
			for (AssetsTask vo : dataList) {
				Row row = createRow(sheet, rowIndex, normalCell, COLUMN_WIDTH_AUTO);
				setCellContent(0, row, rowIndex, normalCell);
				setCellContent(1, row, vo.getAssetsName(), normalCell);
				setCellContent(2, row, vo.getVersion(), normalCell);
				setCellContent(3, row, vo.getCreateUserName(), normalCell);
				setCellContent(4, row, DateUtils.getDateFormat(vo.getDetectionTime(), FULL_DATA_FORMAT), normalCell);
				setCellContent(5, row, vo.getStaticStatus() == null ? "" : vo.getStaticStatus().getName(), normalCell);
				setCellContent(6, row, vo.getDynamicStatus() == null ? "" : vo.getDynamicStatus().getName(), normalCell);
				setCellContent(7, row, vo.getLawStatus() == null ? "" : vo.getLawStatus().getName(), normalCell);
				setCellContent(8, row, DateUtils.getDateFormat(vo.getAssetsCreateTime(), FULL_DATA_FORMAT), normalCell);
				if (!terminalTypeEnum.isApplet()) {
					setCellContent(9, row, vo.getAssetsSize(), normalCell);
					setCellContent(10, row, vo.getPackageName(), normalCell);
				}
				rowIndex += 1;
			}
		} catch (Exception e) {
			e.getMessage();
		}
		LOG.info("数据导出成功.");
	}

	public static void writeExcelDetectionStatisticsDetail(TerminalTypeEnum terminalTypeEnum,
														   LawStatistics lawStatistics, List<DetectionStatisticsDetail> detectionList,
														   Map<Long, DetectionStatisticsLawItem> lawItemMap,
														   XSSFWorkbook workBook) {
		if (lawStatistics == null) {
			return;
		}
		try {
			String lawName = lawsSimpleName(lawStatistics.getLawId());
			// sheet 对应一个工作页
			Sheet sheet = workBook.createSheet("检测任务" + lawName);
			// 创建单元格样式
			CellStyle normalCell = normalCell(workBook);
			CellStyle titleCell = titleCell(workBook);
			CellStyle leftTitleCell = leftTitleCell(workBook);
			CellStyle boldTitleCell = boldTitleCell(workBook);
			CellStyle mergeCell = mergeCell(workBook);

			CellStyle lawDescCell = workBook.createCellStyle();
			lawDescCell.setBorderBottom(BorderStyle.THIN); //下边框
			lawDescCell.setBorderLeft(BorderStyle.THIN);//左边框
			lawDescCell.setBorderTop(BorderStyle.THIN);//上边框
			lawDescCell.setBorderRight(BorderStyle.THIN);//右边框
			lawDescCell.setAlignment(HorizontalAlignment.LEFT); // 水平居中对齐
			lawDescCell.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中对齐
			lawDescCell.setWrapText(true);

			// 自定义字体颜色, 同单元格样式
			Font normalFont = workBook.createFont();
			normalFont.setFontName(FONT_NAME);
			normalCell.setFont(normalFont);
			mergeCell.setFont(normalFont);
			titleCell.setFont(normalFont);
			leftTitleCell.setFont(normalFont);
			lawDescCell.setFont(normalFont);


			Row mainTitleRow = createRow(sheet, 0, normalCell);
			setCellContent(0, mainTitleRow, "合规问题统计（" + terminalTypeEnum.getName() + "）", boldTitleCell);
			CellRangeAddress mainTitleRegion = new CellRangeAddress(0, 0, 0, 4);
			setColumnsCellEmpty(1, 4, mainTitleRow, boldTitleCell);
			sheet.addMergedRegion(mainTitleRegion);

			Row statisticRow = createRow(sheet, 1, normalCell);
			setCellContent(0, statisticRow, "检测内容", normalCell);
			setCellContent(1, statisticRow, "检测次数", normalCell);
			setCellContent(2, statisticRow, "合规问题总数", normalCell);
			setCellContent(3, statisticRow, "", normalCell);
			setCellContent(4, statisticRow, "", normalCell);

			Row statisticDataRow = createRow(sheet, 2, normalCell);
			setCellContent(0, statisticDataRow, lawName, normalCell);
			setCellContent(1, statisticDataRow, lawStatistics.getDetectionTotalCount(), normalCell);
			setCellContent(2, statisticDataRow, lawStatistics.getNonComplianceTotalCount(), normalCell);

			Row mainTitleRow2 = createRow(sheet, 3, normalCell);
			setCellContent(0, mainTitleRow2, lawName + "检测情况统计详情", boldTitleCell);
			CellRangeAddress mainTitleRegion2 = new CellRangeAddress(3, 3, 0, 4);
			setColumnsCellEmpty(1, 4, mainTitleRow2, boldTitleCell);
			sheet.addMergedRegion(mainTitleRegion2);

			Row detailsTitleRow = createRow(sheet, 4, normalCell);
			setCellContent(0, detailsTitleRow, "应用名称", titleCell);
			setCellContent(1, detailsTitleRow, "版本", titleCell);
			setCellContent(2, detailsTitleRow, "检测时间", titleCell);
			setCellContent(3, detailsTitleRow, "风险总数", titleCell);
			setCellContent(4, detailsTitleRow, "风险详情", titleCell);
			createRow(sheet, 5, normalCell);
			int rowIndex = 6;
			for (DetectionStatisticsDetail vo : detectionList) {
				int assetsNameRowMergeBegin = rowIndex;
				int assetsNameRowMergeCount = assetsDetectionStatistics(vo, workBook, sheet, rowIndex);
				rowIndex += assetsNameRowMergeCount;
				// 合并单元格
				if (assetsNameRowMergeCount > 1) {
					int assetsNameRowMergeEnd = assetsNameRowMergeBegin + assetsNameRowMergeCount - 1;
					CellRangeAddress region = new CellRangeAddress(assetsNameRowMergeBegin, assetsNameRowMergeEnd, 0, 0);
					sheet.addMergedRegion(region);
				}
			}
			createRow(sheet, rowIndex++, normalCell);
			Row title2 = createRow(sheet, rowIndex++, normalCell);
			setCellContent(0, title2, "检测内容", titleCell);
			setCellContent(1, title2,
					lawsFullName(lawStatistics.getLawId()),
					normalCell);
			createRow(sheet, rowIndex++, normalCell);
			Row title3 = createRow(sheet, rowIndex++, normalCell);
			setCellContent(0, title3, "检测次数", titleCell);
			setCellContent(1, title3, lawStatistics.getDetectionTotalCount(), normalCell);
			setCellContent(2, title3, "检测方式", titleCell);
			setCellContent(3, title3, "自动化检测", normalCell);
			setCellContent(0, createRow(sheet, rowIndex++, normalCell), "应用问题", titleCell);
			int lawCount = 1;
			for (LawStatistics.LawItem lawItem : lawStatistics.getLawItemList()) {
				setCellContent(0, createRow(sheet, rowIndex++, normalCell), lawCount + "、" + lawItem.getLawItemName(), leftTitleCell);
				DetectionStatisticsLawItem lawItemInfo = lawItemMap.get(lawItem.getLawItemId());
				if (lawItemInfo != null) {
					int lawChildCount = 1;
					for (DetectionStatisticsLawItem.Detail detail : lawItemInfo.getLawDescriptionList()) {
						Row lawChildRow = createRow(sheet, rowIndex, normalCell);
						lawChildRow.setHeightInPoints(lawChildRow.getHeightInPoints() * 2);
						CellRangeAddress lawItemDesRegion = new CellRangeAddress(rowIndex, rowIndex, 0, 4);
						sheet.addMergedRegion(lawItemDesRegion);
						setCellContent(0, lawChildRow, lawCount + "-" + lawChildCount + " "
								+ detail.getLawDescription(), leftTitleCell);
						rowIndex += 1;
						Row riskRow = createRow(sheet, rowIndex, normalCell);
						setCellContent(0, riskRow, "存在风险", titleCell);
						setCellContent(1, riskRow, detail.getNonComplianceCount(), normalCell);
						rowIndex += 1;
						int column = 0;
						Row detectionRow = createRow(sheet, rowIndex, normalCell);
						detectionRow.setHeightInPoints(detectionRow.getHeightInPoints() * 12);
						for (AssetsInfo assetsInfo : detail.getAssetsList().getList()) {
							String content = "应用名称：" + assetsInfo.getAssetsName()
									+ "\n应用版本：" + assetsInfo.getVersion()
									+ "\n上传人员：" + assetsInfo.getUserName()
									+ "\n检测总数：" + assetsInfo.getDetectionCount()
									+ "\n问题总数：" + assetsInfo.getNonComplianceTotalCount()
									+ "\n本条次数：" + assetsInfo.getCurrentNonComplianceCount();
							sheet.setColumnWidth(column, STATISTICS_COLUMN_WIDTH);
							setCellContent(column, detectionRow, content, lawDescCell);
							column += 1;
						}
						rowIndex += 2;
						lawChildCount += 1;
					}
				}
				lawCount += 1;
			}
		} catch (Exception e) {
			e.getMessage();
		}
		LOG.info("数据导出成功.");
	}

	public static void writeExcelDetectFalsePositivesDetail(List<DetectFalsePositivesReportAssetsInfo> detailList,
															XSSFWorkbook workBook) {
		if (detailList == null) {
			return;
		}
		try {
			Sheet sheet = workBook.createSheet("误报数据统计");

			// 创建单元格样式
			CellStyle normalCell = normalCell(workBook);
			CellStyle titleCell = titleCell(workBook);
			CellStyle mergeCell = mergeCell(workBook);

			CellStyle lawDescCell = workBook.createCellStyle();
			lawDescCell.setBorderBottom(BorderStyle.THIN); //下边框
			lawDescCell.setBorderLeft(BorderStyle.THIN);//左边框
			lawDescCell.setBorderTop(BorderStyle.THIN);//上边框
			lawDescCell.setBorderRight(BorderStyle.THIN);//右边框
			lawDescCell.setAlignment(HorizontalAlignment.LEFT); // 水平居中对齐
			lawDescCell.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中对齐
			lawDescCell.setWrapText(true);

			// 自定义字体颜色, 同单元格样式
			Font normalFont = workBook.createFont();
			normalFont.setFontName(FONT_NAME);
			normalCell.setFont(normalFont);
			titleCell.setFont(normalFont);
			lawDescCell.setFont(normalFont);
			mergeCell.setFont(normalFont);

			int size = 13;
			int rowIndex = 0;
			Row mainTitleRow = createRow(sheet, rowIndex, size, titleCell, STATISTICS_COLUMN_WIDTH);
			setCellContent(0, mainTitleRow, "序号", titleCell);
			setCellContent(1, mainTitleRow, "应用名称", titleCell);
			setCellContent(2, mainTitleRow, "应用包名", titleCell);
			setCellContent(3, mainTitleRow, "版本", titleCell);
			setCellContent(4, mainTitleRow, "创建者", titleCell);
			setCellContent(5, mainTitleRow, "检测时间", titleCell);
			setCellContent(6, mainTitleRow, "法规", titleCell);
			setCellContent(7, mainTitleRow, "检测项", titleCell);
			setCellContent(8, mainTitleRow, "检测点", titleCell);
			setCellContent(9, mainTitleRow, "检测结论", titleCell);
			setCellContent(10, mainTitleRow, "评估结果（变更前）", titleCell);
			setCellContent(11, mainTitleRow, "评估结果（变更后）", titleCell);
			setCellContent(12, mainTitleRow, "备注", titleCell);
			rowIndex += 1;
			int number = 0;
			for (DetectFalsePositivesReportAssetsInfo assets:detailList) {
				number += 1;
				int itemCount = 0;
				int assetsNameRowMergeBegin = rowIndex;
				for (DetectFalsePositivesReportLaw law:assets.getLawList()) {
					int lawRowMergeBegin = rowIndex;
					for (DetectFalsePositivesReportItem detail:law.getItemList()) {
						Row itemRow = createRow(sheet, rowIndex, size, lawDescCell, STATISTICS_COLUMN_WIDTH);
						setCellContent(0, itemRow, String.valueOf(number), lawDescCell);
						setCellContent(1, itemRow, assets.getAssetsName(), lawDescCell);
						setCellContent(2, itemRow, assets.getPackageName(), lawDescCell);
						setCellContent(3, itemRow, assets.getVersion(), lawDescCell);
						setCellContent(4, itemRow, law.getUserName(), lawDescCell);
						setCellContent(5, itemRow, DateUtils.getDateFormat(law.getDetectionTime(), FULL_DATA_FORMAT), lawDescCell);
						setCellContent(6, itemRow, lawsSimpleName(law.getLawId()), lawDescCell);
						setCellContent(7, itemRow, detail.getLawsItemParentName(), lawDescCell);
						setCellContent(8, itemRow, detail.getLawsItemName(), lawDescCell);
						setCellContent(9, itemRow, detail.getConclusion(), lawDescCell);
						setCellContent(10, itemRow, detail.getOriginalResultStatus().getName(), lawDescCell);
						setCellContent(11, itemRow, detail.getResultStatus().getName(), lawDescCell);
						setCellContent(12, itemRow, detail.getMarkDescription(), lawDescCell);
						itemCount += 1;
						rowIndex += 1;
					}
					// 合并单元格
					if (law.getItemList().size() > 1) {
						int lawRowMergeEnd = lawRowMergeBegin + law.getItemList().size() - 1;
						mergeCell(sheet, lawRowMergeBegin, lawRowMergeEnd, 4, 4);
						mergeCell(sheet, lawRowMergeBegin, lawRowMergeEnd, 5, 5);
						mergeCell(sheet, lawRowMergeBegin, lawRowMergeEnd, 6, 6);
					}
				}
				// 合并单元格
				if (itemCount > 1) {
					int assetsNameRowMergeEnd = assetsNameRowMergeBegin + itemCount - 1;
					mergeCell(sheet, assetsNameRowMergeBegin, assetsNameRowMergeEnd, 0, 0);
					mergeCell(sheet, assetsNameRowMergeBegin, assetsNameRowMergeEnd, 1, 1);
					mergeCell(sheet, assetsNameRowMergeBegin, assetsNameRowMergeEnd, 2, 2);
					mergeCell(sheet, assetsNameRowMergeBegin, assetsNameRowMergeEnd, 3, 3);
				}
			}
		} catch (Exception e) {
			e.getMessage();
		}
		LOG.info("数据导出成功.");
	}

	private static void mergeCell(Sheet sheet, int rowBegin, int rowEnd, int colBegin, int colEnd) {
		CellRangeAddress region = new CellRangeAddress(rowBegin, rowEnd, colBegin, colEnd);
		sheet.addMergedRegion(region);
	}

	private static int assetsDetectFalsePositives(DetectFalsePositivesDetail vo, Workbook workBook, Sheet sheet, int rowIndex) {
		int detectionCount = 0;
		CellStyle normalCell = normalCell(workBook);
		CellStyle mergeCell = mergeCell(workBook);
		Font normalFont = workBook.createFont();
		normalFont.setFontName(FONT_NAME);
		normalCell.setFont(normalFont);
		mergeCell.setFont(normalFont);
		for (DetectFalsePositivesDetail.Version version : vo.getVersionList()) {
			int sameVersionMergeBegin = rowIndex + detectionCount;
			int sameVersionMergeCount = 0;
			for (DetectFalsePositivesDetail.Detection detection : version.getDetectionList()) {
				int detectionMergeBegin = rowIndex + detectionCount;
				int detectionMergeCount = 0;
				for (DetectFalsePositivesDetail.DetectFalsePositives content : detection.getDetectFalsePositivesList()) {
					Row row = createRow(sheet, rowIndex + detectionCount, normalCell);
					setCellContent(0, row, vo.getAssetsName(), mergeCell);
					setCellContent(1, row, version.getVersion(), mergeCell);
					setCellContent(2, row, DateUtils.getDateFormat(detection.getDetectionTime(), FULL_DATA_FORMAT), mergeCell);
					setCellContent(3, row, detection.getDetectFalsePositivesTotalCount(), mergeCell);
					setCellContent(4, row, (detectionMergeCount + 1) + "、" + content.getLawName(), normalCell);
					sheet.setColumnWidth(4, (int) (STATISTICS_COLUMN_WIDTH * 2.5));
					sameVersionMergeCount += 1;
					detectionMergeCount += 1;
					detectionCount += 1;
				}
				// 合并单元格
				if (detectionMergeCount > 1) {
					int detectionMergeEnd = detectionMergeBegin + detectionMergeCount - 1;
					CellRangeAddress dateRegion = new CellRangeAddress(detectionMergeBegin,
							detectionMergeEnd, 2, 2);
					sheet.addMergedRegion(dateRegion);
					CellRangeAddress countRegion = new CellRangeAddress(detectionMergeBegin,
							detectionMergeEnd, 3, 3);
					sheet.addMergedRegion(countRegion);
				}
			}
			// 合并同版本单元格
			if (sameVersionMergeCount > 1) {
				int sameVersionMerge = sameVersionMergeBegin + sameVersionMergeCount - 1;
				CellRangeAddress sameVersionRegion = new CellRangeAddress(sameVersionMergeBegin,
						sameVersionMerge, 1, 1);
				sheet.addMergedRegion(sameVersionRegion);
			}
		}
		return detectionCount;
	}

	private static int assetsDetectionStatistics(DetectionStatisticsDetail vo, Workbook workBook, Sheet sheet, int rowIndex) {
		int detectionCount = 0;
		CellStyle normalCell = normalCell(workBook);
		CellStyle mergeCell = mergeCell(workBook);
		Font normalFont = workBook.createFont();
		normalFont.setFontName(FONT_NAME);
		normalCell.setFont(normalFont);
		mergeCell.setFont(normalFont);
		for (DetectionStatisticsDetail.Version version : vo.getVersionList()) {
			int sameVersionMergeBegin = rowIndex + detectionCount;
			int sameVersionMergeCount = 0;
			for (DetectionStatisticsDetail.Detection detection : version.getDetectionList()) {
				int detectionMergeBegin = rowIndex + detectionCount;
				int detectionMergeCount = 0;
				for (DetectionStatisticsDetail.NonComplianceContent content : detection.getNonComplianceContentList()) {
					Row row = createRow(sheet, rowIndex + detectionCount, normalCell);
					setCellContent(0, row, vo.getAssetsName(), mergeCell);
					setCellContent(1, row, version.getVersion(), mergeCell);
					setCellContent(2, row, DateUtils.getDateFormat(detection.getDetectionTime(), FULL_DATA_FORMAT), mergeCell);
					setCellContent(3, row, detection.getNonComplianceCount(), mergeCell);
					setCellContent(4, row, (detectionMergeCount + 1) + "、" + content.getLawName(), normalCell);
					sheet.setColumnWidth(4, (int) (STATISTICS_COLUMN_WIDTH * 2.5));
					sameVersionMergeCount += 1;
					detectionMergeCount += 1;
					detectionCount += 1;
				}
				// 合并单元格
				if (detectionMergeCount > 1) {
					int detectionMergeEnd = detectionMergeBegin + detectionMergeCount - 1;
					CellRangeAddress dateRegion = new CellRangeAddress(detectionMergeBegin,
							detectionMergeEnd, 2, 2);
					sheet.addMergedRegion(dateRegion);
					CellRangeAddress countRegion = new CellRangeAddress(detectionMergeBegin,
							detectionMergeEnd, 3, 3);
					sheet.addMergedRegion(countRegion);
				}
			}
			// 合并同版本单元格
			if (sameVersionMergeCount > 1) {
				int sameVersionMerge = sameVersionMergeBegin + sameVersionMergeCount - 1;
				CellRangeAddress sameVersionRegion = new CellRangeAddress(sameVersionMergeBegin,
						sameVersionMerge, 1, 1);
				sheet.addMergedRegion(sameVersionRegion);
			}
		}
		return detectionCount;
	}

	public static void writeExcelSdlStatisticsDetail(TerminalTypeEnum terminalTypeEnum,
													 List<SdkUsageStatistics> sdkUsageStatisticsList,
													 FetchExcelData<SdkStatisticsDetail> sdkDetails, XSSFWorkbook workBook) {
		try {
			// sheet 对应一个工作页
			Sheet sheet = workBook.createSheet("检测任务-SDK合规问题详情");

			// 创建单元格样式
			CellStyle normalCell = normalCell(workBook);
			CellStyle titleCell = titleCell(workBook);
			CellStyle boldTitleCell = boldTitleCell(workBook);
			CellStyle mergeCell = mergeCell(workBook);

			// 自定义字体颜色, 同单元格样式
			Font normalFont = workBook.createFont();
			normalFont.setFontName(FONT_NAME);
			normalCell.setFont(normalFont);
			mergeCell.setFont(normalFont);
			titleCell.setFont(normalFont);


			Row mainTitleRow = createRow(sheet, 0, normalCell);
			setCellContent(0, mainTitleRow, "SDK使用情况统计(" + terminalTypeEnum.getName() + "）", boldTitleCell);
			setColumnsCellEmpty(1, 4, mainTitleRow, boldTitleCell);
			CellRangeAddress mainTitleRegion = new CellRangeAddress(0, 0, 0, 4);
			sheet.addMergedRegion(mainTitleRegion);

			Row sdkUsageNameRow = createRow(sheet, 1, normalCell);
			Row sdkUsageNumRow = createRow(sheet, 2, normalCell);
			int sdkUsageColumn = 0;
			for (SdkUsageStatistics usage : sdkUsageStatisticsList) {
				sheet.setColumnWidth(sdkUsageColumn, STATISTICS_COLUMN_WIDTH);
				setCellContent(sdkUsageColumn, sdkUsageNameRow, usage.getSdkName(), normalCell);
				setCellContent(sdkUsageColumn, sdkUsageNumRow, usage.getCount(), normalCell);
				sdkUsageColumn += 1;
			}
			Row mainTitleRow2 = createRow(sheet, 3, normalCell);
			setCellContent(0, mainTitleRow2, "SDK合规检测情况统计详情", boldTitleCell);
			CellRangeAddress mainTitleRegion2 = new CellRangeAddress(3, 3, 0, 4);
			setColumnsCellEmpty(1, 4, mainTitleRow, boldTitleCell);
			sheet.addMergedRegion(mainTitleRegion2);

			Row detailsTitleRow = createRow(sheet, 4, normalCell);
			setCellContent(0, detailsTitleRow, "SDK名称", titleCell);
			setCellContent(1, detailsTitleRow, "包名", titleCell);
			setCellContent(2, detailsTitleRow, "厂商", titleCell);
			setCellContent(3, detailsTitleRow, "合规风险数", titleCell);
			setCellContent(4, detailsTitleRow, "涉及应用问题", titleCell);
			int rowIndex = 5;
			ExcelData<SdkStatisticsDetail> data = sdkDetails.load(1, EXCEL_SDK_DATA_PAGE_SIZE);
			while (!CollectionUtils.isEmpty(data.dataList)) {
				for (SdkStatisticsDetail vo : data.dataList) {
					int sdkRowMergeBegin = rowIndex;
					int sdkRowMergeCount = 0;
					for (String lawName : vo.getLawItemList()) {
						Row row = createRow(sheet, rowIndex, normalCell);
						setCellContent(0, row, vo.getSdkName(), mergeCell);
						setCellContent(1, row, vo.getSdkPackageName(), mergeCell);
						setCellContent(2, row, vo.getManufacturer(), mergeCell);
						setCellContent(3, row, vo.getNonComplianceTotalCount(), mergeCell);
						sheet.setColumnWidth(4, (int) (STATISTICS_COLUMN_WIDTH * 2.5));
						setCellContent(4, row, (sdkRowMergeCount + 1) + "、" + lawName, normalCell);
						rowIndex += 1;
						sdkRowMergeCount += 1;
					}
					// 合并单元格
					if (sdkRowMergeCount > 1) {
						for (int m = 0; m < 4; m++) {
							int sdkRowMergeEnd = sdkRowMergeBegin + sdkRowMergeCount - 1;
							CellRangeAddress region = new CellRangeAddress(sdkRowMergeBegin,
									sdkRowMergeEnd, m, m);
							sheet.addMergedRegion(region);
						}
					}
				}
				data = sdkDetails.load(data.pageNum + 1, EXCEL_SDK_DATA_PAGE_SIZE);
			}
		} catch (Exception e) {
			e.getMessage();
		}
		LOG.info("数据导出成功.");
	}

	private static CellStyle alertCell(Workbook workbook) {
		CellStyle alertCell = workbook.createCellStyle();
		alertCell.setBorderBottom(BorderStyle.THIN); //下边框
		alertCell.setBorderLeft(BorderStyle.THIN);//左边框
		alertCell.setBorderTop(BorderStyle.THIN);//上边框
		alertCell.setBorderRight(BorderStyle.THIN);//右边框
		// 自定义字体颜色, 同单元格样式
		Font font = workbook.createFont();
		font.setFontName(FONT_NAME);
		font.setColor(IndexedColors.RED.getIndex());
		alertCell.setFont(font);
		return alertCell;
	}

	private static CellStyle normalCell(Workbook workbook) {
		CellStyle normalCell = workbook.createCellStyle();
		normalCell.setBorderBottom(BorderStyle.THIN); //下边框
		normalCell.setBorderLeft(BorderStyle.THIN);//左边框
		normalCell.setBorderTop(BorderStyle.THIN);//上边框
		normalCell.setBorderRight(BorderStyle.THIN);//右边框
		normalCell.setAlignment(HorizontalAlignment.LEFT); // 水平居中对齐
		normalCell.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中对齐
		return normalCell;
	}

	private static CellStyle titleCell(Workbook workbook) {
		CellStyle titleCell = workbook.createCellStyle();
		titleCell.setBorderBottom(BorderStyle.THIN); //下边框
		titleCell.setBorderLeft(BorderStyle.THIN);//左边框
		titleCell.setBorderTop(BorderStyle.THIN);//上边框
		titleCell.setBorderRight(BorderStyle.THIN);//右边框
		titleCell.setAlignment(HorizontalAlignment.CENTER); // 水平居中对齐
		titleCell.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中对齐
		titleCell.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
		titleCell.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		return titleCell;
	}

	private static CellStyle leftTitleCell(Workbook workbook) {
		CellStyle titleCell = workbook.createCellStyle();
		titleCell.setBorderBottom(BorderStyle.THIN); //下边框
		titleCell.setBorderLeft(BorderStyle.THIN);//左边框
		titleCell.setBorderTop(BorderStyle.THIN);//上边框
		titleCell.setBorderRight(BorderStyle.THIN);//右边框
		titleCell.setAlignment(HorizontalAlignment.LEFT); // 水平居中对齐
		titleCell.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中对齐
		titleCell.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
		titleCell.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		return titleCell;
	}

	private static CellStyle mergeCell(Workbook workbook) {
		CellStyle mergeCell = workbook.createCellStyle();
		mergeCell.setBorderBottom(BorderStyle.THIN); //下边框
		mergeCell.setBorderLeft(BorderStyle.THIN);//左边框
		mergeCell.setBorderTop(BorderStyle.THIN);//上边框
		mergeCell.setBorderRight(BorderStyle.THIN);//右边框
		mergeCell.setAlignment(HorizontalAlignment.CENTER); // 水平居中对齐
		mergeCell.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中对齐
		return mergeCell;
	}

	private static CellStyle boldTitleCell(Workbook workbook) {
		Font boldFont = workbook.createFont();
		boldFont.setBold(true); // 设置字体为加粗
		boldFont.setFontName(FONT_NAME);
		CellStyle boldTitleCell = workbook.createCellStyle();
		boldTitleCell.setBorderBottom(BorderStyle.THIN); //下边框
		boldTitleCell.setBorderLeft(BorderStyle.THIN);//左边框
		boldTitleCell.setBorderTop(BorderStyle.THIN);//上边框
		boldTitleCell.setBorderRight(BorderStyle.THIN);//右边框
		boldTitleCell.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
		boldTitleCell.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		boldTitleCell.setAlignment(HorizontalAlignment.CENTER); // 水平居中对齐
		boldTitleCell.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中对齐
		boldTitleCell.setFont(boldFont);
		return boldTitleCell;
	}

	private static Row createRow(Sheet sheet, int index, CellStyle normalCell) {
		return createRow(sheet, index, STATISTICS_DEFAULT_SIZE, normalCell, STATISTICS_COLUMN_WIDTH);
	}


	private static Row createRow(Sheet sheet, int index, CellStyle normalCell, int width) {
		return createRow(sheet, index, STATISTICS_DEFAULT_SIZE, normalCell, width);
	}

	private static Row createRow(Sheet sheet, int index, int size, CellStyle normalCell, int width) {
		Row row = sheet.createRow(index);
		for (int i = 0; i < size; i++) {
			Cell cell = row.createCell(i);
			cell.setCellStyle(normalCell);
			if (width > 0) {
				sheet.setColumnWidth(i, width);
			}
		}
		return row;
	}

	private static boolean isLaw164(Integer lawId) {
		return PrivacyLawId.law164().stream().anyMatch(law -> law.id.equals(lawId));
	}

	private static boolean isLaw191(Integer lawId) {
		return PrivacyLawId.law191().stream().anyMatch(law -> law.id.equals(lawId));
	}

	private static boolean isLaw35273(Integer lawId) {
		return PrivacyLawId.law35273().stream().anyMatch(law -> law.id.equals(lawId));
	}

	private static boolean isLaw41391(Integer lawId) {
		return PrivacyLawId.law41391().stream().anyMatch(law -> law.id.equals(lawId));
	}

	private static String lawsSimpleName(Integer lawId) {
		if (isLaw164(lawId)) {
			return "164号文";
		} else if (isLaw191(lawId)) {
			return "191号文";
		} else if (isLaw35273(lawId)) {
			return "35273号文";
		} else if (isLaw41391(lawId)) {
			return "41391号文";
		} else {
			return "";
		}
	}

	private static String lawsFullName(Integer lawId) {
		if (isLaw164(lawId)) {
			return "关于开展纵深推进APP侵害用户权益专项整治行动的通知（164号文）";
		} else if (isLaw191(lawId)) {
			return "关于开展纵深推进APP侵害用户权益专项整治行动的通知（191号文）";
		} else if (isLaw35273(lawId)) {
			return "个人信息安全规范（35273）";
		} else if (isLaw41391(lawId)) {
			return "信息安全技术移动互联网应用程序（App)收集个人信息基本要求（GB/T 41391-2022）";
		} else {
			return "";
		}
	}

	private static void setCellContent(int index, Row row, String content, CellStyle normalCell, int width) {
		row.getSheet().setColumnWidth(index, width);
		setCellContent(index, row, content, normalCell);
	}

	private static void setCellContent(int index, Row row, String content, CellStyle normalCell) {
		Cell cell = row.createCell(index);
		cell.setCellValue(Objects.isNull(content) ? EXCEL_EMPTY : content);
		cell.setCellStyle(normalCell);
	}

	private static void setCellContent(int index, Row row, String content) {
		Cell cell = row.createCell(index);
		CellStyle cellStyle = cell.getCellStyle();
		cell.setCellValue(Objects.isNull(content) ? EXCEL_EMPTY : content);
		cell.setCellStyle(cellStyle);
	}

	private static void setColumnsCellEmpty(int startIndex, int endIndex, Row row, CellStyle normalCell) {
		for (int i = startIndex; i < endIndex; i++) {
			setCellContent(i, row, StringUtils.EMPTY, normalCell);
		}
	}

	private static void setRowsCellEmpty(int startIndex, int endIndex, Sheet sheet, int column, CellStyle normalCell) {
		for (int i = startIndex; i < endIndex; i++) {
			Row row = createRow(sheet, i, normalCell);
			setCellContent(column, row, StringUtils.EMPTY, normalCell);
		}
	}

	private static void setCellContent(int index, Row row, Integer number, CellStyle normalCell) {
		Cell cell = row.createCell(index);
		cell.setCellValue(Objects.isNull(number) ? 0 : number);
		cell.setCellStyle(normalCell);
	}

	private static void pictureToSheet(Sheet finalSheet, Row row, int pictureIdx, int col1, int row1) {
		Drawing patriarch = finalSheet.createDrawingPatriarch();

		CreationHelper helper = finalSheet.getWorkbook().getCreationHelper();
		ClientAnchor anchor = helper.createClientAnchor();

		// 图片插入坐标
		anchor.setCol1(col1);
		anchor.setRow1(row1);

		// 指定我想要的长宽
		double standardWidth = 108;
		double standardHeight = 192;


		// 调整单元格高度与图片一致
		row.setHeightInPoints((float) standardHeight);

		// 插入图片
		Picture picture = patriarch.createPicture(anchor, pictureIdx);
		picture.resize(1, 1);
	}

	private static void fillEmptyAction(Row row, CellStyle normalStyle, int start, int end) {
		for (int i = start; i < end + 1; i++) {
			Cell cell = row.createCell(i);
			cell.setCellValue(EXCEL_EMPTY);
			cell.setCellStyle(normalStyle);
		}
	}

	private static void fillExcelAction(Row row, CellStyle alertStyle, CellStyle normalStyle, LawActionDetailVO detailVO, TTask task) {
		Cell c8 = row.createCell(8);
		c8.setCellValue(detailVO.getBehaviorStage() == null ? EXCEL_EMPTY : getReportBehaviorStageName(detailVO.getBehaviorStage().getValue(), task.getDetectionType()));
		c8.setCellStyle(normalStyle);

		Cell c9 = row.createCell(9);
		c9.setCellValue(detailVO.getTriggerTime() == null ? EXCEL_EMPTY : DateUtils.getDateFormat(detailVO.getTriggerTime(), FULL_DATA_FORMATSS));
		c9.setCellStyle(normalStyle);

		Cell c10 = row.createCell(10);
		c10.setCellValue(detailVO.getActionName() == null ? EXCEL_EMPTY : detailVO.getActionName());
		c10.setCellStyle(normalStyle);


		Cell c11 = row.createCell(11);
		c11.setCellValue(detailVO.getPersonal() == null || !detailVO.getPersonal() ? "否" : "是");
		c11.setCellStyle(normalStyle);

		Cell c12 = row.createCell(12);
		LawActionDetailUtils.setTimeUnit(detailVO);
		c12.setCellValue(detailVO.getTriggerNum() + detailVO.getTimeUnit());
		c12.setCellStyle(normalStyle);
		if (detailVO.getTriggerNum() != null && detailVO.getTriggerNum() >= PinfoConstant.MINI_COUNT) {
			c12.setCellStyle(alertStyle);//红色
		}

		Cell c13 = row.createCell(13);
		c13.setCellValue(detailVO.getPermissionAlias() == null ? EXCEL_EMPTY : detailVO.getPermissionAlias());
		c13.setCellStyle(normalStyle);

		Cell c14 = row.createCell(14);
		c14.setCellValue(getExecutorName(detailVO.getExecutorType(), task.getTerminalType()));
		c14.setCellStyle(normalStyle);

		Cell c15 = row.createCell(15);
		c15.setCellValue(detailVO.getExecutor() == null ? EXCEL_EMPTY : detailVO.getExecutor());
		c15.setCellStyle(normalStyle);

		Cell c16 = row.createCell(16);
		c16.setCellValue(detailVO.getPackageName() == null ? EXCEL_EMPTY : detailVO.getPackageName());
		c16.setCellStyle(normalStyle);
		if (detailVO.getDetailsData() != null && detailVO.getDetailsData().length() > 31000) {
			detailVO.setDetailsData(detailVO.getDetailsData().substring(0, 31000));
		}
		if (detailVO.getStackInfo() != null && detailVO.getStackInfo().length() > 31000) {
			detailVO.setStackInfo(detailVO.getStackInfo().substring(0, 31000));
		}
		Cell c17 = row.createCell(17);
		c17.setCellValue(detailVO.getDetailsData() == null ? new XSSFRichTextString(EXCEL_EMPTY) : new XSSFRichTextString(detailVO.getDetailsData()));
		c17.setCellStyle(normalStyle);
		Cell c18 = row.createCell(18);
		c18.setCellValue(detailVO.getStackInfo() == null ? new XSSFRichTextString(EXCEL_EMPTY) : new XSSFRichTextString(detailVO.getStackInfo()));
		c18.setCellStyle(normalStyle);
		if (!task.getTerminalType().isApplet()) {
			Cell c19 = row.createCell(19);
			c19.setCellValue(detailVO.getStackInfo() == null ? new XSSFRichTextString(EXCEL_EMPTY) : new XSSFRichTextString(detailVO.getJniStackInfo()));
			c19.setCellStyle(normalStyle);
			Cell c20 = row.createCell(20);
			c20.setCellValue(detailVO.getApiName() == null ? new XSSFRichTextString(EXCEL_EMPTY) : new XSSFRichTextString(detailVO.getApiName()));
			c20.setCellStyle(normalStyle);
		} else {
			Cell c19 = row.createCell(19);
			c19.setCellValue(detailVO.getApiName() == null ? new XSSFRichTextString(EXCEL_EMPTY) : new XSSFRichTextString(detailVO.getApiName()));
			c19.setCellStyle(normalStyle);
		}
	}

	//应用行为数据
	public static void writeExcelMisjudgmentActionData(FetchExcelData<TPrivacyActionNougat> fetchExcelData, TTask task, Workbook workBook) {
		String sheetName = "应用行为";
		int sheetIndex = workBook.getSheetIndex(sheetName);
		if (sheetIndex < 0) {
			LOG.error("sheetName={} 不存在", sheetName);
			return;
		}
		ExcelData<TPrivacyActionNougat> data = fetchExcelData.load(1, EXCEL_ACTION_DATA_PAGE_SIZE);
		if (CollectionUtils.isEmpty(data.dataList)) {
			return;
		}
		try {
			// sheet 对应一个工作页
			Sheet sheet = workBook.getSheetAt(sheetIndex);  //第一个表格

			// 创建单元格样式
			CellStyle cell_style = workBook.createCellStyle();
			cell_style.setBorderBottom(BorderStyle.THIN); //下边框
			cell_style.setBorderLeft(BorderStyle.THIN);//左边框
			cell_style.setBorderTop(BorderStyle.THIN);//上边框
			cell_style.setBorderRight(BorderStyle.THIN);//右边框
			// 自定义字体颜色, 同单元格样式
			Font font = workBook.createFont();
			font.setFontName("微软雅黑");
			font.setColor(IndexedColors.RED.getIndex());
			cell_style.setFont(font);


			CellStyle cell_style1 = workBook.createCellStyle();
			cell_style1.setBorderBottom(BorderStyle.THIN); //下边框
			cell_style1.setBorderLeft(BorderStyle.THIN);//左边框
			cell_style1.setBorderTop(BorderStyle.THIN);//上边框
			cell_style1.setBorderRight(BorderStyle.THIN);//右边框
			// 自定义字体颜色, 同单元格样式
			Font font1 = workBook.createFont();
			font1.setFontName("微软雅黑");
			cell_style1.setFont(font1);
			int total = 0;
			while (!CollectionUtils.isEmpty(data.dataList)) {
				/**
				 * 往Excel中写新数据
				 */
				for (int j = 0; j < data.dataList.size(); j++) {
					// 创建一行：从第二行开始，跳过属性列
					Row row = sheet.createRow(j + total + 1);
					// 得到要插入的每一条记录
					TPrivacyActionNougat vo = data.dataList.get(j);
					//行为阶段	调用时间	行为名称	触发频率（次/秒）	行为权限	主体类型	主体名称	包名	详情	函数调用栈

					// 在一行内循环
					Cell first = row.createCell(0);
					first.setCellValue(vo.getBehaviorStage() == null ? EXCEL_EMPTY : getReportBehaviorStageName(vo.getBehaviorStage().getValue(), task.getDetectionType()));
					first.setCellStyle(cell_style1);

					Cell second = row.createCell(1);
					second.setCellValue(vo.getActionTime() == null ? EXCEL_EMPTY : DateUtils.getDateFormat(vo.getActionTime(), FULL_DATA_FORMATSS));
					second.setCellStyle(cell_style1);

					Cell third = row.createCell(2);
					third.setCellValue(vo.getActionName() == null ? EXCEL_EMPTY : vo.getActionName());
					third.setCellStyle(cell_style1);


					Cell c3_1 = row.createCell(3);
					c3_1.setCellValue(vo.getIsPersonal() == null || "0".equals(vo.getIsPersonal()) ? "否" : "是");
					c3_1.setCellStyle(cell_style1);

					Cell c3 = row.createCell(4);
					ActionNougatUtils.setTriggerCycle(vo);
					c3.setCellValue(vo.getTriggerCycle());
					c3.setCellStyle(cell_style1);
					if (vo.getNumberAction() != null && vo.getNumberAction() >= PinfoConstant.MINI_COUNT) {
						c3.setCellStyle(cell_style);//红色
					}

					Cell c4 = row.createCell(5);
					c4.setCellValue(vo.getActionPermission() == null ? EXCEL_EMPTY : vo.getActionPermission());
					c4.setCellStyle(cell_style1);

					Cell c5_1 = row.createCell(6);
					c5_1.setCellValue(getExecutorName(vo.getExecutorType(), task.getTerminalType()));
					c5_1.setCellStyle(cell_style1);

					Cell c5 = row.createCell(7);
					c5.setCellValue(vo.getExecutor() == null ? EXCEL_EMPTY : vo.getExecutor());
					c5.setCellStyle(cell_style1);

					Cell c6 = row.createCell(8);
					c6.setCellValue(vo.getPackageName() == null ? EXCEL_EMPTY : vo.getPackageName());
					c6.setCellStyle(cell_style1);
					if (vo.getDetailsData() != null && vo.getDetailsData().length() > 31000) {
						vo.setDetailsData(vo.getDetailsData().substring(0, 31000));
					}

					String stackInfo = vo.getStackInfo();
					if (vo.getStackInfo() != null && stackInfo.contains("<---")) {
						vo.setStackInfo(stackInfo.replaceAll("<---", "\r\n"));
					}

					if (vo.getStackInfo() != null && vo.getStackInfo().length() > 31000) {
						vo.setStackInfo(vo.getStackInfo().substring(0, 31000));
					}
					Cell c7 = row.createCell(9);
					c7.setCellValue(vo.getDetailsData() == null ? new XSSFRichTextString(EXCEL_EMPTY) : new XSSFRichTextString(vo.getDetailsData()));
					c7.setCellStyle(cell_style1);

					Cell c8 = row.createCell(10);
					c8.setCellValue(vo.getStackInfo() == null ? new XSSFRichTextString(EXCEL_EMPTY) : new XSSFRichTextString(vo.getStackInfo()));
					c8.setCellStyle(cell_style1);


					Cell c9 = row.createCell(11);
					c9.setCellValue(vo.getJniStackInfo() == null ? new XSSFRichTextString(EXCEL_EMPTY) : new XSSFRichTextString(vo.getJniStackInfo()));
					c9.setCellStyle(cell_style1);

					Cell c10 = row.createCell(12);
					c10.setCellValue(vo.getApiName() == null ? new XSSFRichTextString(EXCEL_EMPTY) : new XSSFRichTextString(vo.getApiName()));
					c10.setCellStyle(cell_style1);
				}
				total += data.dataList.size();
				data = fetchExcelData.load(data.pageNum + 1, EXCEL_ACTION_DATA_PAGE_SIZE);
			}
		} catch (Exception e) {
			e.getMessage();
		}
		LOG.info("应用行为数据写入表格成功.");
	}


	//SDK数据
	public static void writeExcelMisjudgmentSdkData(List<SdkVO> dataList, Workbook workBook) {
		String sheetName = "SDK数据";
		int sheetIndex = workBook.getSheetIndex(sheetName);
		if (sheetIndex < 0) {
			LOG.error("sheetName={} 不存在", sheetName);
			return;
		}
		if (dataList == null || dataList.size() == 0) {
			return;
		}
		try {
			// sheet 对应一个工作页
			Sheet sheet = workBook.getSheetAt(sheetIndex);  //第一个表格

			// 创建单元格样式
			CellStyle cell_style = workBook.createCellStyle();
			cell_style.setBorderBottom(BorderStyle.THIN); //下边框
			cell_style.setBorderLeft(BorderStyle.THIN);//左边框
			cell_style.setBorderTop(BorderStyle.THIN);//上边框
			cell_style.setBorderRight(BorderStyle.THIN);//右边框
			cell_style.setAlignment(HorizontalAlignment.CENTER); // 居中
			cell_style.setAlignment(HorizontalAlignment.CENTER);  // 设置单元格水平方向对其方式
			cell_style.setVerticalAlignment(VerticalAlignment.CENTER); // 设置单元格垂直方向对其方式

			// 自定义字体颜色, 同单元格样式
			Font font = workBook.createFont();
			font.setFontName("微软雅黑");
			font.setColor(IndexedColors.RED.getIndex());
			cell_style.setFont(font);
			
			
			CellStyle cell_style1 = workBook.createCellStyle();
			cell_style1.setBorderBottom(BorderStyle.THIN); //下边框
			cell_style1.setBorderLeft(BorderStyle.THIN);//左边框
			cell_style1.setBorderTop(BorderStyle.THIN);//上边框
			cell_style1.setBorderRight(BorderStyle.THIN);//右边框
			
			cell_style1.setAlignment(HorizontalAlignment.CENTER);  // 设置单元格水平方向对其方式
			cell_style1.setVerticalAlignment(VerticalAlignment.CENTER); // 设置单元格垂直方向对其方式
			
			// 自定义字体颜色, 同单元格样式
			Font font1 = workBook.createFont();
			font1.setFontName("微软雅黑");
			cell_style1.setFont(font1);

			/**
			 * 往Excel中写新数据
			 */
			int index_row = 0;
			int index = 0;
			int last_index = 0;
			for (int j = 0; j < dataList.size(); j++) {
				
				SdkVO vo = dataList.get(j);
				List<PermissionVO> pList = vo.getPermissions();
				int size = (pList==null || pList.size()==0 ? 0 : pList.size());
				index_row = index + size;
				if(j==0) {
					index = 2;
					index_row = index + size;
				}
				else {
//					if(index==index_row && last_index==0){
//						index ++;
//						index_row = index;
//					}
				}
				last_index = (pList==null || pList.size()==0 ? 0 : pList.size());
				
				// 创建一行：从第二行开始，跳过属性列
				Row row = sheet.createRow(index);
				// 得到要插入的每一条记录
//				SDK名称	类型	包名	厂商	描述	下载链接	SdkVersion		
//				minSdkVersion	targetSdkVersion	complieSdkVersion
				// 在一行内循环
				Cell c = row.createCell(0);
				c.setCellValue(vo.getName() == null ? "" : vo.getName());
				c.setCellStyle(cell_style1);

				Cell c1 = row.createCell(1);
				c1.setCellValue(vo.getTypeName() == null ? "" : vo.getTypeName());
				c1.setCellStyle(cell_style1);

				Cell c2 = row.createCell(2);
				c2.setCellValue(vo.getPackageName() == null ? "" : vo.getPackageName());
				c2.setCellStyle(cell_style1);

				Cell c3 = row.createCell(3);
				c3.setCellValue(vo.getManufacturer() == null ? "" : vo.getManufacturer());
				c3.setCellStyle(cell_style1);
				
				Cell c4 = row.createCell(4);
				c4.setCellValue(vo.getDescribe() == null ? "" : vo.getDescribe());
				c4.setCellStyle(cell_style1);
				
				Cell c5 = row.createCell(5);
				c5.setCellValue(vo.getUrl() == null ? "" : vo.getUrl());
				c5.setCellStyle(cell_style1);
				
				Cell c6 = row.createCell(6);
				c6.setCellValue(vo.getMinSdkVersion() == null ? "" : String.valueOf(vo.getMinSdkVersion()));
				c6.setCellStyle(cell_style1);
				
				Cell c7 = row.createCell(7);
				c7.setCellValue(vo.getTargetSdkVersion() == null ? "" : String.valueOf(vo.getTargetSdkVersion()));
				c7.setCellStyle(cell_style1);
				
				Cell c8 = row.createCell(8);
				c8.setCellValue(vo.getCompileSdkVersion() == null ? "" : String.valueOf(vo.getCompileSdkVersion()));
				c8.setCellStyle(cell_style1);
				
				
				if(pList != null && pList.size()>0) {
					for (int i = 0; i < pList.size(); i++) {
						PermissionVO p = pList.get(i);
						if(p==null) {
							continue;
						}
						Row row1 = row;
						if(i>0) {
							row1 = sheet.createRow(index+i);
						}
						Cell c9 = row1.createCell(9);
						c9.setCellValue(p.getAliasName() == null ? "" : p.getAliasName());
						c9.setCellStyle(cell_style1);
						
						Cell c10 = row1.createCell(10);
						c10.setCellValue(p.getHarm() == null ? "" : p.getHarm());
						c10.setCellStyle(cell_style1);
						
						Cell c11 = row1.createCell(11);
						c11.setCellValue(p.getProtectionLevel() == null ? "" : p.getProtectionLevel());
						c11.setCellStyle(cell_style1);
						
						Cell c12 = row1.createCell(12);
						c12.setCellValue(p.getIsPrivacy() == null|| p.getIsPrivacy()==0 ? "否" : "是");
						c12.setCellStyle(cell_style1);
						
						Cell c13 = row1.createCell(13);
						c13.setCellValue(p.getType() == null || p.getType()==0 ? "正常" : "敏感");
						c13.setCellStyle(cell_style1);
						
						Cell c14 = row1.createCell(14);
						c14.setCellValue(p.getUsed()== null || !p.getUsed() ? "否" : "是");
						c14.setCellStyle(cell_style1);
						if(p.getUsed()!= null && p.getUsed()){
							c14.setCellStyle(cell_style);//红色
						}
					}
				}else {
					Cell c9 = row.createCell(9);
					c9.setCellValue("");
					c9.setCellStyle(cell_style1);
					
					Cell c10 = row.createCell(10);
					c10.setCellValue("");
					c10.setCellStyle(cell_style1);
					
					Cell c11 = row.createCell(11);
					c11.setCellValue("");
					c11.setCellStyle(cell_style1);
					
					Cell c12 = row.createCell(12);
					c12.setCellValue("");
					c12.setCellStyle(cell_style1);
					
					Cell c13 = row.createCell(13);
					c13.setCellValue("");
					c13.setCellStyle(cell_style1);
					
					Cell c14 = row.createCell(14);
					c14.setCellValue("");
					c14.setCellStyle(cell_style1);
				}
				
				//合并单元格
				if(index != index_row && index != (index_row-1)) {
					
					CellRangeAddress region = new CellRangeAddress(index, index_row-1, 0, 0);
					CellRangeAddress region1 = new CellRangeAddress(index, index_row-1, 1, 1);
					CellRangeAddress region2 = new CellRangeAddress(index, index_row-1, 2, 2);
					CellRangeAddress region3 = new CellRangeAddress(index, index_row-1, 3, 3);
					CellRangeAddress region4 = new CellRangeAddress(index, index_row-1, 4, 4);
					CellRangeAddress region5 = new CellRangeAddress(index, index_row-1, 5, 5);
					CellRangeAddress region6 = new CellRangeAddress(index, index_row-1, 6, 6);
					CellRangeAddress region7 = new CellRangeAddress(index, index_row-1, 7, 7);
					CellRangeAddress region8 = new CellRangeAddress(index, index_row-1, 8, 8);
					sheet.addMergedRegion(region);
					sheet.addMergedRegion(region1);
					sheet.addMergedRegion(region2);
					sheet.addMergedRegion(region3);
					sheet.addMergedRegion(region4);
					sheet.addMergedRegion(region5);
					sheet.addMergedRegion(region6);
					sheet.addMergedRegion(region7);
					sheet.addMergedRegion(region8);
					
					//加边框
					setBorderStyle(BorderStyle.THIN, region, sheet);
					setBorderStyle(BorderStyle.THIN, region1, sheet);
					setBorderStyle(BorderStyle.THIN, region2, sheet);
					setBorderStyle(BorderStyle.THIN, region3, sheet);
					setBorderStyle(BorderStyle.THIN, region4, sheet);
					setBorderStyle(BorderStyle.THIN, region5, sheet);
					setBorderStyle(BorderStyle.THIN, region6, sheet);
					setBorderStyle(BorderStyle.THIN, region7, sheet);
					setBorderStyle(BorderStyle.THIN, region8, sheet);
					
				}
				index = index_row;
				if(last_index==0) {
					index++;
				}
			}
		} catch (Exception e) {
			e.getMessage();
		}
		LOG.info("SDK数据写入表格成功");
		
	}
	
	/**
	 * 合并单元格设置边框
	 * @param
	 * @param cellRangeTitle
	 * @param sheet
	 */
	private static void setBorderStyle(BorderStyle borderStyle, CellRangeAddress cellRangeTitle, Sheet sheet){
	    RegionUtil.setBorderBottom(borderStyle, cellRangeTitle, sheet);//下边框
	    RegionUtil.setBorderLeft(borderStyle, cellRangeTitle, sheet);//左边框
	    RegionUtil.setBorderRight(borderStyle, cellRangeTitle, sheet);//右边框
	    RegionUtil.setBorderTop(borderStyle, cellRangeTitle, sheet);//上边框
	}
	
	
	/**
     * 创建一个单元格并为其设定指定的对齐方式
     * @param wb 工作簿
     * @param row 行
	 * @param column  列
	 * @param halign  水平方向对其方式
	 * @param valign  垂直方向对其方式
	 */
	private static void createCell(Workbook wb, Row row, short column, HorizontalAlignment halign, VerticalAlignment valign) {
		Cell cell = row.createCell(column);  // 创建单元格
		CellStyle cellStyle = wb.createCellStyle(); // 创建单元格样式
		cellStyle.setAlignment(halign);  // 设置单元格水平方向对其方式
		cellStyle.setVerticalAlignment(valign); // 设置单元格垂直方向对其方式
		cell.setCellStyle(cellStyle); // 设置单元格样式　　　　
	}

	/**
	 * 权限数据
	 *
	 * @param dataList
	 */
	public static void writeExcelMisjudgmentAppletPermissionData(Integer detectionType, List<PermissionVO> dataList, File workBookFile) {
		Workbook workBook = null;
		try {
			workBook = WriteExcel.getWorkbok(workBookFile);
		} catch (IOException e) {
			e.getMessage();
		}
		if (Objects.isNull(workBook)) {
			return;
		}
		String sheetName = "权限使用";
		int sheetIndex = workBook.getSheetIndex(sheetName);
		if (sheetIndex < 0) {
			LOG.error("sheetName={} 不存在", sheetName);
			return;
		}
		if (dataList == null || dataList.size() == 0) {
			return;
		}
		FileOutputStream fos = null;
		try {
			// sheet 对应一个工作页
			Sheet sheet = workBook.getSheetAt(sheetIndex);  //第一个表格
			// 创建单元格样式
			CellStyle cell_style = workBook.createCellStyle();
			cell_style.setBorderBottom(BorderStyle.THIN); //下边框
			cell_style.setBorderLeft(BorderStyle.THIN);//左边框
			cell_style.setBorderTop(BorderStyle.THIN);//上边框
			cell_style.setBorderRight(BorderStyle.THIN);//右边框
			// 自定义字体颜色, 同单元格样式
			Font font = workBook.createFont();
			font.setFontName("微软雅黑");
			font.setColor(IndexedColors.RED.getIndex());
			cell_style.setFont(font);


			CellStyle cell_style1 = workBook.createCellStyle();
			cell_style1.setBorderBottom(BorderStyle.THIN); //下边框
			cell_style1.setBorderLeft(BorderStyle.THIN);//左边框
			cell_style1.setBorderTop(BorderStyle.THIN);//上边框
			cell_style1.setBorderRight(BorderStyle.THIN);//右边框
			// 自定义字体颜色, 同单元格样式
			Font font1 = workBook.createFont();
			font1.setFontName("微软雅黑");
			cell_style1.setFont(font1);

			/**
			 * 往Excel中写新数据
			 */
			for (int j = 0; j < dataList.size(); j++) {
				// 创建一行：从第二行开始，跳过属性列
				Row row = sheet.createRow(j + 2);
				// 得到要插入的每一条记录
				PermissionVO vo = dataList.get(j);
				//行为阶段	调用时间	行为名称	触发频率（次/秒）	行为权限	主体类型	主体名称	包名	详情	函数调用栈

				// 在一行内循环
				Cell first = row.createCell(0);
				first.setCellValue(vo.getAliasName() == null ? "" : vo.getAliasName());
				first.setCellStyle(cell_style1);

				Cell second = row.createCell(1);
				second.setCellValue(vo.getRemark() == null ? "" : vo.getRemark());
				second.setCellStyle(cell_style1);

				Cell c4 = row.createCell(2);
				c4.setCellValue(vo.getType() == null || vo.getType() == 0 ? "正常" : "敏感");
				c4.setCellStyle(cell_style1);

				Cell c5 = row.createCell(3);
				c5.setCellValue(vo.getStatementPermission() == null || !vo.getStatementPermission() ? "否" : "是");
				c5.setCellStyle(cell_style1);

				Cell c6 = row.createCell(4);
				c6.setCellValue(vo.getExcessPermission() == null || !vo.getExcessPermission() ? "否" : "是");
				c6.setCellStyle(cell_style1);

				Cell c7 = row.createCell(5);
				c7.setCellValue(vo.getAppB1Count() == null ? 0 : vo.getAppB1Count());
				c7.setCellStyle(cell_style1);

				Cell c9 = row.createCell(6);
				c9.setCellValue(vo.getAppB2Count() == null ? 0 : vo.getAppB2Count());
				c9.setCellStyle(cell_style1);

				Cell c13 = row.createCell(7);
				c13.setCellValue(vo.getAppB3Count() == null ? 0 : vo.getAppB3Count());
				c13.setCellStyle(cell_style1);

				Cell c15 = row.createCell(8);
				c15.setCellValue(vo.getAppB4Count() == null ? 0 : vo.getAppB4Count());
				c15.setCellStyle(cell_style1);
			}
			if (detectionType == TaskDetectionTypeEnum.DEPTH.getValue()) {
				// 深度检测只有一个应用行为，需要修改名字和删掉其它多余的列
				Row row = sheet.getRow(0);
				Cell cell = row.getCell(7);
				cell.setCellValue("应用行为");
				deleteColumn(sheet, 9);
				deleteColumn(sheet, 8);
				deleteColumn(sheet, 6);
			}
			// 创建文件输出流，准备输出电子表格：这个必须有，否则你在sheet上做的任何操作都不会有效
			fos = new FileOutputStream(workBookFile.getAbsolutePath());
			workBook.write(fos);
			workBook.close();
		} catch (Exception e) {
			e.getMessage();
		} finally {
			if (fos != null) {
				try {
					fos.close();
				} catch (IOException e) {
					e.getMessage();
				}
			}
		}
		LOG.info("权限数据写入表格成功.");
	}

	/**
	 * 权限数据
	 *
	 * @param dataList
	 */
	public static void writeExcelMisjudgmentpermissionData(Integer detectionType, List<PermissionVO> dataList, File workBookFile) {
		Workbook workBook = null;
		try {
			workBook = WriteExcel.getWorkbok(workBookFile);
		} catch (IOException e) {
			e.getMessage();
		}
		if (Objects.isNull(workBook)) {
			return;
		}
		String sheetName = "权限使用";
		int sheetIndex = workBook.getSheetIndex(sheetName);
		if (sheetIndex < 0) {
			LOG.error("sheetName={} 不存在", sheetName);
			return;
		}
		if (dataList == null || dataList.size() == 0) {
			return;
		}
		FileOutputStream fos = null;
		try {
			// sheet 对应一个工作页
			Sheet sheet = workBook.getSheetAt(sheetIndex);  //第一个表格
			// 创建单元格样式
			CellStyle cell_style = workBook.createCellStyle();
			cell_style.setBorderBottom(BorderStyle.THIN); //下边框
			cell_style.setBorderLeft(BorderStyle.THIN);//左边框
			cell_style.setBorderTop(BorderStyle.THIN);//上边框
			cell_style.setBorderRight(BorderStyle.THIN);//右边框
			// 自定义字体颜色, 同单元格样式
			Font font = workBook.createFont();
			font.setFontName("微软雅黑");
			font.setColor(IndexedColors.RED.getIndex());
			cell_style.setFont(font);
			
			
			CellStyle cell_style1 = workBook.createCellStyle();
			cell_style1.setBorderBottom(BorderStyle.THIN); //下边框
			cell_style1.setBorderLeft(BorderStyle.THIN);//左边框
			cell_style1.setBorderTop(BorderStyle.THIN);//上边框
			cell_style1.setBorderRight(BorderStyle.THIN);//右边框
			// 自定义字体颜色, 同单元格样式
			Font font1 = workBook.createFont();
			font1.setFontName("微软雅黑");
			cell_style1.setFont(font1);

			/**
			 * 往Excel中写新数据
			 */
			for (int j = 0; j < dataList.size(); j++) {
				// 创建一行：从第二行开始，跳过属性列
				Row row = sheet.createRow(j + 2);
				// 得到要插入的每一条记录
				PermissionVO vo = dataList.get(j);
				//行为阶段	调用时间	行为名称	触发频率（次/秒）	行为权限	主体类型	主体名称	包名	详情	函数调用栈

				// 在一行内循环
				Cell first = row.createCell(0);
				first.setCellValue(vo.getAliasName() == null ? "" : vo.getAliasName());
				first.setCellStyle(cell_style1);

				Cell second = row.createCell(1);
				second.setCellValue(vo.getRemark() == null ? "" : vo.getRemark());
				second.setCellStyle(cell_style1);

				Cell third = row.createCell(2);
				third.setCellValue(vo.getProtectionLevel() == null ? "" : vo.getProtectionLevel());
				third.setCellStyle(cell_style1);

				Cell c4 = row.createCell(3);
				c4.setCellValue(vo.getType() == null || vo.getType()==0 ? "正常" : "敏感");
				c4.setCellStyle(cell_style1);
				
				Cell c5 = row.createCell(4);
				c5.setCellValue(vo.getStatementPermission() == null || !vo.getStatementPermission() ? "否" : "是");
				c5.setCellStyle(cell_style1);
				
				Cell c6 = row.createCell(5);
				c6.setCellValue(vo.getExcessPermission() == null || !vo.getExcessPermission() ? "否" : "是");
				c6.setCellStyle(cell_style1);
				
				Cell c7 = row.createCell(6);
				c7.setCellValue(vo.getAppB1Count() == null ? 0 : vo.getAppB1Count());
				c7.setCellStyle(cell_style1);
				
				Cell c8 = row.createCell(7);
				c8.setCellValue(vo.getSdkB1Count() == null ? 0 : vo.getSdkB1Count());
				c8.setCellStyle(cell_style1);
				
				Cell c9 = row.createCell(8);
				c9.setCellValue(vo.getAppB2Count() == null ? 0 : vo.getAppB2Count());
				c9.setCellStyle(cell_style1);
				
				Cell c10 = row.createCell(9);
				c10.setCellValue(vo.getSdkB2Count() == null ? 0 : vo.getSdkB2Count());
				c10.setCellStyle(cell_style1);
				
				Cell c13 = row.createCell(10);
				c13.setCellValue(vo.getAppB3Count() == null ? 0 : vo.getAppB3Count());
				c13.setCellStyle(cell_style1);
				
				Cell c14 = row.createCell(11);
				c14.setCellValue(vo.getSdkB3Count() == null ? 0 : vo.getSdkB3Count());
				c14.setCellStyle(cell_style1);

				Cell c15 = row.createCell(12);
				c15.setCellValue(vo.getAppB4Count() == null ? 0 : vo.getAppB4Count());
				c15.setCellStyle(cell_style1);

				Cell c16 = row.createCell(13);
				c16.setCellValue(vo.getSdkB4Count() == null ? 0 : vo.getSdkB4Count());
				c16.setCellStyle(cell_style1);
			}
			if (detectionType == TaskDetectionTypeEnum.DEPTH.getValue()) {
				// 深度检测只有一个应用行为，需要修改名字和删掉其它多余的列
				Row row = sheet.getRow(0);
				Cell cell = row.getCell(9);
				cell.setCellValue("应用行为");
				deleteColumn(sheet, 14);
				deleteColumn(sheet, 13);
				deleteColumn(sheet, 12);
				deleteColumn(sheet, 11);
				deleteColumn(sheet, 8);
				deleteColumn(sheet, 7);
			}
			// 创建文件输出流，准备输出电子表格：这个必须有，否则你在sheet上做的任何操作都不会有效
			fos = new FileOutputStream(workBookFile.getAbsolutePath());
			workBook.write(fos);
			workBook.close();
		} catch (Exception e) {
			e.getMessage();
		} finally {
			if (fos != null) {
				try {
					fos.close();
				} catch (IOException e) {
					e.getMessage();
				}
			}
		}
		LOG.info("权限数据写入表格成功.");
	}

	private static void replaceCellAppName(Cell cell) {
		String newValue = cell.getStringCellValue().replace("App", "小程序");
		cell.setCellValue(newValue);
	}

	/**
	 * 通讯传输数据
	 *
	 * @param fetchExcelData
	 * @param workBook
	 */
	public static void writeExcelMisjudgmentOutsideData(FetchExcelData<TPrivacyOutsideAddress> fetchExcelData, TTask task,
														Workbook workBook) {
		String sheetName = "通信行为";
		int sheetIndex = workBook.getSheetIndex(sheetName);
		if (sheetIndex < 0) {
			LOG.error("sheetName={} 不存在", sheetName);
			return;
		}
		ExcelData<TPrivacyOutsideAddress> data = fetchExcelData.load(1, EXCEL_ACTION_DATA_PAGE_SIZE);
		if (CollectionUtils.isEmpty(data.dataList)) {
			return;
		}
		try {
			// sheet 对应一个工作页
			Sheet sheet = workBook.getSheetAt(sheetIndex);  //第一个表格

			// 创建单元格样式
			CellStyle redCellStyle = workBook.createCellStyle();
			redCellStyle.setBorderBottom(BorderStyle.THIN); //下边框
			redCellStyle.setBorderLeft(BorderStyle.THIN);//左边框
			redCellStyle.setBorderTop(BorderStyle.THIN);//上边框
			redCellStyle.setBorderRight(BorderStyle.THIN);//右边框
			// 自定义字体颜色, 同单元格样式
			Font font = workBook.createFont();
			font.setFontName("微软雅黑");
			font.setColor(IndexedColors.RED.getIndex());
			redCellStyle.setFont(font);


			CellStyle normalStyle = workBook.createCellStyle();
			normalStyle.setBorderBottom(BorderStyle.THIN); //下边框
			normalStyle.setBorderLeft(BorderStyle.THIN);//左边框
			normalStyle.setBorderTop(BorderStyle.THIN);//上边框
			normalStyle.setBorderRight(BorderStyle.THIN);//右边框
			// 自定义字体颜色, 同单元格样式
			Font font1 = workBook.createFont();
			font1.setFontName("微软雅黑");
			normalStyle.setFont(font1);

			CellStyle detailsStyle = workBook.createCellStyle();
			detailsStyle.setBorderBottom(BorderStyle.THIN); //下边框
			detailsStyle.setBorderLeft(BorderStyle.THIN);//左边框
			detailsStyle.setBorderTop(BorderStyle.THIN);//上边框
			detailsStyle.setBorderRight(BorderStyle.THIN);//右边框
			detailsStyle.setWrapText(true);
			detailsStyle.setFont(font1);

			int total = 0;
			while (!CollectionUtils.isEmpty(data.dataList)) {
				/**
				 * 往Excel中写新数据
				 */
				for (int j = 0; j < data.dataList.size(); j++) {
					// 创建一行：从第二行开始，跳过属性列
					Row row = sheet.createRow(j + total + 1);
					// 得到要插入的每一条记录
					TPrivacyOutsideAddress vo = data.dataList.get(j);
					//行为阶段	调用时间	行为名称	触发频率（次/秒）	行为权限	主体类型	主体名称	包名	详情	函数调用栈

					// 在一行内循环
					Cell first = row.createCell(0);
					first.setCellValue(vo.getBehaviorStage() == null ? "" : getReportBehaviorStageName(vo.getBehaviorStage().getValue(), task.getDetectionType()));
					first.setCellStyle(normalStyle);

					Cell second = row.createCell(1);
					second.setCellValue(vo.getActionTime() == null ? "" : DateUtils.getDateFormat(vo.getActionTime(), FULL_DATA_FORMATSS));
					second.setCellStyle(normalStyle);

					Cell third = row.createCell(2);
					third.setCellValue(vo.getOutside() == null || vo.getOutside() == PrivacyStatusEnum.NO.getValue() ? "境内" : "境外");
					third.setCellStyle(normalStyle);

					Cell c3 = row.createCell(3);
					c3.setCellValue(getExecutorName(vo.getExecutorType(), task.getTerminalType()));
					c3.setCellStyle(normalStyle);

					Cell c4 = row.createCell(4);
					c4.setCellValue(vo.getExecutor() == null ? "" : vo.getExecutor());
					c4.setCellStyle(normalStyle);

					Cell c5 = row.createCell(5);
					c5.setCellValue(vo.getPackageName() == null ? "" : vo.getPackageName());
					c5.setCellStyle(normalStyle);

					Cell c6 = row.createCell(6);
					c6.setCellValue(vo.getIp() == null ? "" : vo.getIp());
					c6.setCellStyle(normalStyle);

					Cell c7 = row.createCell(7);
					c7.setCellValue(vo.getPort() == null ? "" : vo.getPort());
					c7.setCellStyle(normalStyle);

					Cell c8 = row.createCell(8);
					c8.setCellValue(vo.getHost() == null ? "" : vo.getHost());
					c8.setCellStyle(normalStyle);

					Cell c9 = row.createCell(9);
					c9.setCellValue(vo.getAddress() == null ? "" : vo.getAddress());
					c9.setCellStyle(normalStyle);

					Cell c10 = row.createCell(10);
					c10.setCellValue(vo.getProtocol() == null ? "" : vo.getProtocol());
					c10.setCellStyle(normalStyle);

					Cell c11 = row.createCell(11);
					c11.setCellValue(vo.getRequestMethod() == null ? "" : vo.getRequestMethod());
					c11.setCellStyle(normalStyle);

					if (vo.getDetailsData() != null && vo.getDetailsData().length() > 31000) {
						vo.setDetailsData(vo.getDetailsData().substring(0, 31000));
					}
					if (vo.getStackInfo() != null && vo.getStackInfo().length() > 31000) {
						vo.setStackInfo(vo.getStackInfo().substring(0, 31000));
					}

					if (vo.getResponseData() != null && vo.getResponseData().length() > 31000) {
						vo.setResponseData(vo.getResponseData().substring(0, 31000));
					}

					int columnWidth = 100;
					// 设置行高
					String detailsData = vo.getDetailsData();
					float detailsHeight = calculateRowHeight(detailsData, columnWidth, font1);
					String responseData = vo.getResponseData();
					float responseHeight = calculateRowHeight(responseData, columnWidth, font1);
					row.setHeightInPoints(Math.max(detailsHeight, responseHeight));

					Cell c12 = row.createCell(12);
					sheet.setColumnWidth(12, columnWidth * 256);
					c12.setCellValue(detailsData);
					c12.setCellStyle(detailsStyle);


					Cell c13 = row.createCell(13);
					sheet.setColumnWidth(13, columnWidth * 256);
					c13.setCellValue(responseData);
					c13.setCellStyle(detailsStyle);

					Cell c14 = row.createCell(14);
					c14.setCellValue(vo.getStackInfo() == null ? "" : vo.getStackInfo());
					c14.setCellStyle(normalStyle);

					Cell c15 = row.createCell(15);
					c15.setCellValue(vo.getCookie() == null ? "" : vo.getCookie());
					c15.setCellStyle(normalStyle);
				}
				total += data.dataList.size();
				data = fetchExcelData.load(data.pageNum + 1, EXCEL_ACTION_DATA_PAGE_SIZE);
			}
		} catch (Exception e) {
			e.getMessage();
		}
		LOG.info("通讯行为数据写入表格成功.");
	}

	/**
	 * 根据内容、列宽和字体计算行高
	 *
	 * @param content    单元格内容
	 * @param columnWidth 列宽（以字符为单位）
	 * @param font       字体
	 * @return 行高（以点为单位）
	 */
	private static float calculateRowHeight(String content, int columnWidth, Font font) {
		// 每行的最大字符数
		int maxCharsPerLine = columnWidth;

		// 分割内容为多行
		String[] lines = content.split("\n");

		// 计算总行数
		int totalLines = 0;
		for (String line : lines) {
			totalLines += Math.ceil((double) line.length() / maxCharsPerLine);
		}

		// 字体高度（以点为单位）
		float fontHeightInPoints = font.getFontHeightInPoints();

		// 总行高 = 行数 * 字体高度 + 一些额外空间（经验值）
		return totalLines * fontHeightInPoints + 2; // 加2是为了留一些上下边距
	}

	/**
	 * 存储个人信息数据
	 *
	 * @param fetchExcelData
	 * @param workBook
	 */
	public static void writeExcelMisjudgmentSharedPrefsData(FetchExcelData<TPrivacySharedPrefs> fetchExcelData, TTask task,
															Workbook workBook) {
		String sheetName = "存储个人信息";
		int sheetIndex = workBook.getSheetIndex(sheetName);
		if (sheetIndex < 0) {
			LOG.error("sheetName={} 不存在", sheetName);
			return;
		}
		ExcelData<TPrivacySharedPrefs> data = fetchExcelData.load(1, EXCEL_ACTION_DATA_PAGE_SIZE);
		if (CollectionUtils.isEmpty(data.dataList)) {
			return;
		}
		try {
			// sheet 对应一个工作页
			Sheet sheet = workBook.getSheetAt(sheetIndex);  //第一个表格

			// 创建单元格样式
			CellStyle cell_style = workBook.createCellStyle();
			cell_style.setBorderBottom(BorderStyle.THIN); //下边框
			cell_style.setBorderLeft(BorderStyle.THIN);//左边框
			cell_style.setBorderTop(BorderStyle.THIN);//上边框
			cell_style.setBorderRight(BorderStyle.THIN);//右边框
			// 自定义字体颜色, 同单元格样式
			Font font = workBook.createFont();
			font.setFontName("微软雅黑");
			font.setColor(IndexedColors.RED.getIndex());
			cell_style.setFont(font);


			CellStyle cell_style1 = workBook.createCellStyle();
			cell_style1.setBorderBottom(BorderStyle.THIN); //下边框
			cell_style1.setBorderLeft(BorderStyle.THIN);//左边框
			cell_style1.setBorderTop(BorderStyle.THIN);//上边框
			cell_style1.setBorderRight(BorderStyle.THIN);//右边框
			// 自定义字体颜色, 同单元格样式
			Font font1 = workBook.createFont();
			font1.setFontName("微软雅黑");
			cell_style1.setFont(font1);
			int total = 0;
			while (!CollectionUtils.isEmpty(data.dataList)) {
				/**
				 * 往Excel中写新数据
				 */
				for (int j = 0; j < data.dataList.size(); j++) {
					// 创建一行：从第二行开始，跳过属性列
					Row row = sheet.createRow(j + total + 1);
					// 得到要插入的每一条记录
					TPrivacySharedPrefs vo = data.dataList.get(j);
					//行为阶段	调用时间	主体类型	主体名称	包名	信息分类	个人信息	关键词	存储位置	关键词	代码片段	函数调用栈

					// 在一行内循环
					Cell first = row.createCell(0);
					first.setCellValue(vo.getBehaviorStage() == null ? "" : getReportBehaviorStageName(vo.getBehaviorStage().getValue(), task.getDetectionType()));
					first.setCellStyle(cell_style1);

					Cell second = row.createCell(1);
					second.setCellValue(vo.getActionTime() == null ? "" : DateUtils.getDateFormat(vo.getActionTime(), FULL_DATA_FORMATSS));
					second.setCellStyle(cell_style1);

					Cell third = row.createCell(2);
					third.setCellValue(getExecutorName(vo.getExecutorType(), task.getTerminalType()));
					third.setCellStyle(cell_style1);

					Cell c3 = row.createCell(3);
					c3.setCellValue(vo.getExecutor() == null ? "" : vo.getExecutor());
					c3.setCellStyle(cell_style1);

					Cell c4 = row.createCell(4);
					c4.setCellValue(vo.getPackageName() == null ? "" : vo.getPackageName());
					c4.setCellStyle(cell_style1);

					Cell c5 = row.createCell(5);
					c5.setCellValue(vo.getTypeName() == null ? "" : vo.getTypeName());
					c5.setCellStyle(cell_style1);

					Cell c6 = row.createCell(6);
					c6.setCellValue(vo.getName() == null ? "" : vo.getName());
					c6.setCellStyle(cell_style1);

					Cell c7 = row.createCell(7);
					c7.setCellValue(vo.getSensitiveWord() == null ? "" : vo.getSensitiveWord());
					c7.setCellStyle(cell_style1);

					Cell c8 = row.createCell(8);
					c8.setCellValue(vo.getPath() == null ? "" : vo.getPath());
					c8.setCellStyle(cell_style1);

					if (vo.getContent() != null && vo.getContent().length() > 31000) {
						vo.setContent(vo.getContent().substring(0, 31000));
					}
					if (vo.getStackInfo() != null && vo.getStackInfo().length() > 31000) {
						vo.setStackInfo(vo.getStackInfo().substring(0, 31000));
					}

					Cell c9 = row.createCell(9);
					c9.setCellValue(vo.getContent() == null ? "" : vo.getContent());
					c9.setCellStyle(cell_style1);

					Cell c10 = row.createCell(10);
					c10.setCellValue(vo.getStackInfo() == null ? "" : vo.getStackInfo());
					c10.setCellStyle(cell_style1);
				}
				total += data.dataList.size();
				data = fetchExcelData.load(data.pageNum + 1, EXCEL_ACTION_DATA_PAGE_SIZE);
			}
		} catch (Exception e) {
			e.getMessage();
		}
		LOG.info("存储个人信息数据写入表格成功.");
	}


	/**
	 * 传输个人信息数据
	 *
	 * @param fetchExcelData
	 * @param workBook
	 */
	public static void writeExcelMisjudgmentSensitiveData(FetchExcelData<TPrivacySensitiveWord> fetchExcelData, TTask task, Workbook workBook) {
		String sheetName = "传输个人信息";
		int sheetIndex = workBook.getSheetIndex(sheetName);
		if (sheetIndex < 0) {
			LOG.error("sheetName={} 不存在", sheetName);
			return;
		}
		ExcelData<TPrivacySensitiveWord> data = fetchExcelData.load(1, EXCEL_ACTION_DATA_PAGE_SIZE);
		if (CollectionUtils.isEmpty(data.dataList)) {
			return;
		}
		try {
			// sheet 对应一个工作页
			Sheet sheet = workBook.getSheetAt(sheetIndex);  //第一个表格

			// 创建单元格样式
			CellStyle cell_style = workBook.createCellStyle();
			cell_style.setBorderBottom(BorderStyle.THIN); //下边框
			cell_style.setBorderLeft(BorderStyle.THIN);//左边框
			cell_style.setBorderTop(BorderStyle.THIN);//上边框
			cell_style.setBorderRight(BorderStyle.THIN);//右边框
			// 自定义字体颜色, 同单元格样式
			Font font = workBook.createFont();
			font.setFontName("微软雅黑");
			font.setColor(IndexedColors.RED.getIndex());
			cell_style.setFont(font);


			CellStyle cell_style1 = workBook.createCellStyle();
			cell_style1.setBorderBottom(BorderStyle.THIN); //下边框
			cell_style1.setBorderLeft(BorderStyle.THIN);//左边框
			cell_style1.setBorderTop(BorderStyle.THIN);//上边框
			cell_style1.setBorderRight(BorderStyle.THIN);//右边框
			// 自定义字体颜色, 同单元格样式
			Font font1 = workBook.createFont();
			font1.setFontName("微软雅黑");
			cell_style1.setFont(font1);

			CellStyle detailsStyle = workBook.createCellStyle();
			detailsStyle.setBorderBottom(BorderStyle.THIN); //下边框
			detailsStyle.setBorderLeft(BorderStyle.THIN);//左边框
			detailsStyle.setBorderTop(BorderStyle.THIN);//上边框
			detailsStyle.setBorderRight(BorderStyle.THIN);//右边框
			detailsStyle.setWrapText(true);
			detailsStyle.setFont(font1);

			int total = 0;
			while (!CollectionUtils.isEmpty(data.dataList)) {
				/**
				 * 往Excel中写新数据
				 */
				for (int j = 0; j < data.dataList.size(); j++) {
					// 创建一行：从第二行开始，跳过属性列
					Row row = sheet.createRow(j + total + 1);
					// 得到要插入的每一条记录
					TPrivacySensitiveWord vo = data.dataList.get(j);
					//行为阶段	调用时间	主体类型	主体名称	包名	信息分类	个人信息	IP

					// 在一行内循环
					Cell first = row.createCell(0);
					first.setCellValue(vo.getBehaviorStage() == null ? "" : getReportBehaviorStageName(vo.getBehaviorStage().getValue(), task.getDetectionType()));
					first.setCellStyle(cell_style1);

					Cell second = row.createCell(1);
					second.setCellValue(vo.getActionTime() == null ? "" : DateUtils.getDateFormat(vo.getActionTime(), FULL_DATA_FORMATSS));
					second.setCellStyle(cell_style1);

					Cell third = row.createCell(2);
					third.setCellValue(getExecutorName(vo.getExecutorType(), task.getTerminalType()));
					third.setCellStyle(cell_style1);

					Cell c3 = row.createCell(3);
					c3.setCellValue(vo.getExecutor() == null ? "" : vo.getExecutor());
					c3.setCellStyle(cell_style1);

					Cell c4 = row.createCell(4);
					c4.setCellValue(vo.getPackageName() == null ? "" : vo.getPackageName());
					c4.setCellStyle(cell_style1);

					Cell c5 = row.createCell(5);
					c5.setCellValue(vo.getTypeName() == null ? "" : vo.getTypeName());
					c5.setCellStyle(cell_style1);

					Cell c6 = row.createCell(6);
					c6.setCellValue(vo.getName() == null ? "" : vo.getName());
					c6.setCellStyle(cell_style1);

					Cell c7 = row.createCell(7);
					c7.setCellValue(vo.getIp() == null ? "" : vo.getIp());
					c7.setCellStyle(cell_style1);

					//端口号	域名	地理位置	协议类型	网络数据类型	URL地址	关键词	代码片段	抓包数据	函数调用栈
					Cell c8 = row.createCell(8);
					c8.setCellValue(vo.getPort() == null ? "" : vo.getPort());
					c8.setCellStyle(cell_style1);

					Cell c9 = row.createCell(9);
					c9.setCellValue(vo.getHost() == null ? "" : vo.getHost());
					c9.setCellStyle(cell_style1);

					Cell c10 = row.createCell(10);
					c10.setCellValue(vo.getAttributively() == null ? "" : vo.getAttributively());
					c10.setCellStyle(cell_style1);

					Cell c11 = row.createCell(11);
					c11.setCellValue(vo.getProtocol() == null ? "" : vo.getProtocol());
					c11.setCellStyle(cell_style1);

					Cell c12 = row.createCell(12);
					c12.setCellValue(vo.getMethod() == null ? "" : vo.getMethod());
					c12.setCellStyle(cell_style1);

					Cell c13 = row.createCell(13);
					c13.setCellValue(vo.getUrl() == null ? "" : vo.getUrl());
					c13.setCellStyle(cell_style1);

					Cell c14 = row.createCell(14);
					c14.setCellValue(vo.getSensitiveWord() == null ? "" : vo.getSensitiveWord());
					c14.setCellStyle(cell_style1);

					Cell c15 = row.createCell(15);
					c15.setCellValue(vo.getCode() == null ? "" : vo.getCode());
					c15.setCellStyle(cell_style1);

					if (vo.getDetailsData() != null && vo.getDetailsData().length() > 31000) {
						vo.setDetailsData(vo.getDetailsData().substring(0, 31000));
					}
					if (vo.getStackInfo() != null && vo.getStackInfo().length() > 31000) {
						vo.setStackInfo(vo.getStackInfo().substring(0, 31000));
					}

					int columnWidth = 100;
					// 设置行高
					String detailsData = vo.getDetailsData();
					float detailsHeight = calculateRowHeight(detailsData, columnWidth, font1);
					String responseData = vo.getResponseData();
					float responseHeight = calculateRowHeight(responseData, columnWidth, font1);
					row.setHeightInPoints(Math.max(detailsHeight, responseHeight));

					Cell c16 = row.createCell(16);
					sheet.setColumnWidth(16, columnWidth * 256);
					c16.setCellValue(detailsData);
					c16.setCellStyle(detailsStyle);


					Cell c17 = row.createCell(17);
					sheet.setColumnWidth(17, columnWidth * 256);
					c17.setCellValue(responseData);
					c17.setCellStyle(detailsStyle);

					Cell c18 = row.createCell(18);
					c18.setCellValue(vo.getStackInfo() == null ? "" : vo.getStackInfo());
					c18.setCellStyle(cell_style1);

					Cell c19 = row.createCell(19);
					c19.setCellValue(vo.getCookie() == null ? "" : vo.getCookie());
					c19.setCellStyle(cell_style1);
				}
				total += data.dataList.size();
				data = fetchExcelData.load(data.pageNum + 1, EXCEL_ACTION_DATA_PAGE_SIZE);
			}
		} catch (Exception e) {
			e.getMessage();
		}
		LOG.info("传输个人信息数据写入表格成功.");
	}


	//日志数据
	public static void writeExcelSysOperateLog(List<TsysOperateLog> logList, String finalXlsxPath) {
		if(logList==null || logList.size()==0) {
			return;
		}
		OutputStream out = null;
		try {
			// 读取Excel文档
			File finalXlsxFile = new File(finalXlsxPath);
			Workbook workBook = getWorkbok(finalXlsxFile);
			// sheet 对应一个工作页
			Sheet sheet = workBook.getSheetAt(0);  //第一个表格
			// 创建单元格样式
			CellStyle cell_style = workBook.createCellStyle();
			cell_style.setBorderBottom(BorderStyle.THIN); //下边框
			cell_style.setBorderLeft(BorderStyle.THIN);//左边框
			cell_style.setBorderTop(BorderStyle.THIN);//上边框
			cell_style.setBorderRight(BorderStyle.THIN);//右边框


			// 自定义字体颜色, 同单元格样式
			Font font = workBook.createFont();
			font.setFontName("微软雅黑");
			cell_style.setFont(font);

			/**
			 * 往Excel中写新数据
			 */
			for (int j = 0; j < logList.size(); j++) {
				// 创建一行：从第二行开始，跳过属性列
				Row row = sheet.createRow(j + 1);
				// 得到要插入的每一条记录
				TsysOperateLog vo = logList.get(j);
				//日志	账号名称 姓名 IP地址 操作内容 状态 操作时间

				// 在一行内循环
				Cell c0 = row.createCell(0);
				c0.setCellValue(vo.getUserName() == null ? "" : vo.getUserName());
				c0.setCellStyle(cell_style);


				Cell c1 = row.createCell(1);
				c1.setCellValue(vo.getRealName() == null ? "" : vo.getRealName());
				c1.setCellStyle(cell_style);


				Cell c2 = row.createCell(2);
				c2.setCellValue(vo.getIp() == null ? "":vo.getIp());
				c2.setCellStyle(cell_style);

				Cell c3 = row.createCell(3);
				c3.setCellValue(vo.getContent() == null ? "" : vo.getContent());
				c3.setCellStyle(cell_style);


				Cell c4 = row.createCell(4);
				c4.setCellValue(vo.getStatus() == null ? "" : (vo.getStatus().getValue()==200?"成功":"失败"));
				c4.setCellStyle(cell_style);

				Cell c5 = row.createCell(5);
				c5.setCellValue(vo.getCreatedTime() == null ? "" : DateUtils.getDateFormat(vo.getCreatedTime(), FULL_DATA_FORMATSS));
				c5.setCellStyle(cell_style);

			}
			// 创建文件输出流，准备输出电子表格：这个必须有，否则你在sheet上做的任何操作都不会有效
			out = new FileOutputStream(finalXlsxPath);
			workBook.write(out);
			workBook.close();
		} catch (Exception e) {
			e.getMessage();
		} finally {
			try {
				if (out != null) {
					out.flush();
					out.close();
				}
			} catch (IOException e) {
				e.getMessage();
			}
		}
		LOG.info("日志数据写入表格成功.finalXlsxPath={}",finalXlsxPath);
	}

	public static void deleteColumn(Sheet sheet, int columnToDelete) {
		for (int rId = 0; rId <= sheet.getLastRowNum(); rId++) {
			Row row = sheet.getRow(rId);
			for (int cID = columnToDelete; cID <= row.getLastCellNum(); cID++) {
				Cell cOld = row.getCell(cID);
				if (cOld != null) {
					row.removeCell(cOld);
				}
				Cell cNext = row.getCell(cID + 1);
				if (cNext != null) {
					Cell cNew = row.createCell(cID, cNext.getCellType());
					cloneCell(cNew, cNext);
					if (rId == 0) {
						sheet.setColumnWidth(cID, sheet.getColumnWidth(cID + 1));
					}
				}
			}
		}
	}

	/**
	 * 右边列左移
	 *
	 * @param cNew
	 * @param cOld
	 */
	private static void cloneCell(Cell cNew, Cell cOld) {
		cNew.setCellComment(cOld.getCellComment());
		cNew.setCellStyle(cOld.getCellStyle());

		if (CellType.BOOLEAN == cNew.getCellType()) {
			cNew.setCellValue(cOld.getBooleanCellValue());
		} else if (CellType.NUMERIC == cNew.getCellType()) {
			cNew.setCellValue(cOld.getNumericCellValue());
		} else if (CellType.STRING == cNew.getCellType()) {
			cNew.setCellValue(cOld.getStringCellValue());
		} else if (CellType.ERROR == cNew.getCellType()) {
			cNew.setCellValue(cOld.getErrorCellValue());
		} else if (CellType.FORMULA == cNew.getCellType()) {
			cNew.setCellValue(cOld.getCellFormula());
		}
	}

	public static SXSSFWorkbook getSXSSFWorkbook(String filePath) throws IOException {
		FileInputStream in = new FileInputStream(filePath);
		return new SXSSFWorkbook(new XSSFWorkbook(in));
	}

	private static String getExecutorName(Integer executorType, TerminalTypeEnum terminalTypeEnum) {
		if (ExecutorTypeEnum.APP.getValue().equals(executorType)) {
			if (terminalTypeEnum == TerminalTypeEnum.WECHAT_APPLET || terminalTypeEnum == TerminalTypeEnum.ALIPAY_APPLET) {
				return "小程序";
			} else {
				return "APP";
			}
		}
		return "SDK";
	}

	@Data
	public static class ExcelData<T> {
		List<T> dataList;
		int pageNum = 0;

		public ExcelData(List<T> dataList, int pageNum) {
			this.dataList = dataList;
			this.pageNum = pageNum;
		}

		public static <T> ExcelData<T> empty() {
			return new ExcelData<>(Collections.emptyList(), 0);
		}
	}

	@Slf4j
	public static class ReadImage {

		private final IjiamiCommonProperties commonProperties;
		private final IBaseFileService fileService;

		public ReadImage(IjiamiCommonProperties commonProperties, IBaseFileService fileService) {
			this.commonProperties = commonProperties;
			this.fileService = fileService;
		}

		byte[] imageDataByFileKey(String fileKey) {
			if (StringUtils.isNotBlank(fileKey)) {
				try {
					if (fileKey.contains("group")) {
						String url = "";
						File file = new File(commonProperties.getProperty("detection.tools.dynamic_path"), UuidUtil.uuid());
						if (fileKey.contains("http")) {
							url = fileKey;
						} else {
							url = commonProperties.getProperty("detection.result.url.prefix") + fileKey;
						}
						FileUtils.copyURLToFile(new URL(url), file);
						return FileUtils.readFileToByteArray(file);
					} else {
						cn.ijiami.base.common.file.entity.File img = fileService.findFileByFileKey(fileKey);
						return FileUtils.readFileToByteArray(new File(commonProperties.getFilePath() + img.getFilePath()));
					}
				} catch (Exception e) {
					log.warn("下载图片失败", e);
				}
			}
			return new byte[]{};
		}

	}

	public static final int EXCEL_ACTION_DATA_PAGE_SIZE = 10000;
	public static final int EXCEL_SDK_DATA_PAGE_SIZE = 500;

	public interface FetchExcelData<T> {
		ExcelData<T> load(int pageNum, int pageSize);
	}

	public interface WriteExcelData<T> {
		void write(List<T> dataList, int offset);
	}

	public static <T> void excelPageWrite(FetchExcelData<T> fetchExcelData, WriteExcelData<T> writeExcelData) {
		ExcelData<T> data = fetchExcelData.load(1, EXCEL_ACTION_DATA_PAGE_SIZE);
		int offset = 0;
		while (!CollectionUtils.isEmpty(data.dataList)) {
			writeExcelData.write(data.dataList, offset);
			offset += data.dataList.size();
			data = fetchExcelData.load(data.pageNum + 1, EXCEL_ACTION_DATA_PAGE_SIZE);
		}
	}

	public static void writeExcelDeepDetectionAction(List<? extends RealTimeLog> dataList, Date taskStartTime, String appName, String finalXlsxPath) {
		OutputStream out = null;
		try {
			// 读取Excel文档
			Workbook workBook = new XSSFWorkbook();
			// sheet 对应一个工作页
			Sheet sheet = workBook.createSheet("行为数据");

			// 创建单元格样式
			CellStyle titleStyle = titleCell(workBook);
			CellStyle normalStyle = normalCell(workBook);
			normalStyle.setAlignment(HorizontalAlignment.CENTER);
			CellStyle stackInfoStyle = normalCell(workBook);
			stackInfoStyle.setWrapText(true);
			// 自定义字体颜色, 同单元格样式
			Font font = workBook.createFont();
			font.setFontName("微软雅黑");
			normalStyle.setFont(font);

			Row titleRow = sheet.createRow(0);
			int middleCell = 256 * 20;
			int largeCell = 256 * 200;
			setCellContent(0, titleRow, "应用名称", titleStyle, middleCell);
			setCellContent(1, titleRow, "行为名称", titleStyle, middleCell);
			setCellContent(2, titleRow, "行为主体", titleStyle, middleCell);
			setCellContent(3, titleRow, "触发时间点", titleStyle, middleCell);
			setCellContent(4, titleRow, "运行状态", titleStyle, middleCell);
			setCellContent(5, titleRow, "堆栈信息", titleStyle, largeCell);
			if (!CollectionUtils.isEmpty(dataList)) {
				// 往Excel中写新数据
				SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm:ss");
				SimpleDateFormat appFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				for (int j = 0; j < dataList.size(); j++) {
					// 创建一行：从第二行开始，跳过属性列
					Row row = sheet.createRow(j + 1);
					// 得到要插入的每一条记录
					RealTimeLog realTimeLog = dataList.get(j);
					row.setHeight((short) (256 * 60));
					setCellContent(0, row, appName, normalStyle, middleCell);
					setCellContent(1, row, realTimeLog.getActionName(), normalStyle, middleCell);
					setCellContent(2, row, realTimeLog.getExecutor(), normalStyle, middleCell);
					setCellContent(3, row, dateFormat.format(realTimeLog.getActionTime()), normalStyle, middleCell);
					setCellContent(4, row, behaviorStageStatus(BehaviorStageEnum.getItem(realTimeLog.getBehaviorStage())), normalStyle, middleCell);
					String stackInfo = String.format("应用启动时间:%s\r\n行为触发时间:%s\r\n函数调用栈:\r\n%s \r\n详情:%s",
							appFormat.format(taskStartTime), appFormat.format(realTimeLog.getActionTime()),
							setValue(realTimeLog.getStackInfo()), setValue(realTimeLog.getDetailsData()));
					setCellContent(5, row, stackInfo, stackInfoStyle, largeCell);
				}
			}
			// 创建文件输出流，准备输出电子表格：这个必须有，否则你在sheet上做的任何操作都不会有效
			out = Files.newOutputStream(Paths.get(finalXlsxPath));
			workBook.write(out);
			workBook.close();
		} catch (Exception e) {
			LOG.error("导出文件错误 ", e);
		} finally {
			try {
				if (out != null) {
					out.flush();
					out.close();
				}
			} catch (IOException e) {
				LOG.error("导出文件错误 ", e);
			}
		}
		LOG.info("数据导出成功.finalXlsxPath={}",finalXlsxPath);
	}

	public static void writeExcelDeepSdkAction(List<RealtimeSdkItem> dataList, String finalXlsxPath) {
		OutputStream out = null;
		try {
			// 读取Excel文档
			Workbook workBook = new XSSFWorkbook();
			// sheet 对应一个工作页
			Sheet sheet = workBook.createSheet("行为数据");

			// 创建单元格样式
			CellStyle titleStyle = titleCell(workBook);
			CellStyle normalStyle = workBook.createCellStyle();
			normalStyle.setBorderBottom(BorderStyle.THIN); //下边框
			normalStyle.setBorderLeft(BorderStyle.THIN);//左边框
			normalStyle.setBorderTop(BorderStyle.THIN);//上边框
			normalStyle.setBorderRight(BorderStyle.THIN);//右边框
			normalStyle.setAlignment(HorizontalAlignment.CENTER); // 水平居中对齐
			normalStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中对齐
			// 自定义字体颜色, 同单元格样式
			Font font = workBook.createFont();
			font.setFontName("微软雅黑");
			normalStyle.setFont(font);

			Row titleRow = sheet.createRow(0);
			int middleCell = 256 * 20;
			setCellContent(0, titleRow, "应用名称", titleStyle, middleCell);
			setCellContent(1, titleRow, "执行行为主体名称", titleStyle, middleCell);
			setCellContent(2, titleRow, "行为名称", titleStyle, middleCell);
			setCellContent(3, titleRow, "运行次数", titleStyle, middleCell);
			if (!CollectionUtils.isEmpty(dataList)) {
				// 往Excel中写新数据
				for (int j = 0; j < dataList.size(); j++) {
					// 创建一行：从第二行开始，跳过属性列
					Row row = sheet.createRow(j + 1);
					// 得到要插入的每一条记录
					RealtimeSdkItem sdkItem = dataList.get(j);
					setCellContent(0, row, sdkItem.getAppName(), normalStyle, middleCell);
					setCellContent(1, row, sdkItem.getExecutor(), normalStyle, middleCell);
					setCellContent(2, row, sdkItem.getActionName(), normalStyle, middleCell);
					setCellContent(3, row, String.valueOf(sdkItem.getCount()), normalStyle, middleCell);
				}
			}
			// 创建文件输出流，准备输出电子表格：这个必须有，否则你在sheet上做的任何操作都不会有效
			out = Files.newOutputStream(Paths.get(finalXlsxPath));
			workBook.write(out);
			workBook.close();
		} catch (Exception e) {
			LOG.error("导出文件错误 ", e);
		} finally {
			try {
				if (out != null) {
					out.flush();
					out.close();
				}
			} catch (IOException e) {
				LOG.error("导出文件错误 ", e);
			}
		}
		LOG.info("数据导出成功.finalXlsxPath={}",finalXlsxPath);
	}
	private static String behaviorStageStatus(BehaviorStageEnum behaviorStage) {
		if (behaviorStage == BehaviorStageEnum.BEHAVIOR_GRANT) {
			return "授权前状态";
		} else if (behaviorStage == BehaviorStageEnum.BEHAVIOR_FRONT) {
			return "前台状态";
		} else if (behaviorStage == BehaviorStageEnum.BEHAVIOR_GROUND) {
			return "后台状态";
		} else if (behaviorStage == BehaviorStageEnum.BEHAVIOR_EXIT) {
			return "应用退出状态";
		} else if (behaviorStage == BehaviorStageEnum.BEHAVIOR_LOCK_SCREEN) {
			return "锁屏静默状态";
		} else {
			return "未知状态";
		}
	}

	private static String setValue(String str) {
		return StringUtils.isBlank(str) || StringUtils.equals(str, PinfoConstant.DETAILS_EMPTY) ? "【无】" : str;
	}

	//导出行为生成excel
	public static void generateExcel(List<CompliancePrivacyActionNougatVO> lists, TerminalTypeEnum terminalType, String finalXlsxPath, OutputStream out){
		try {
			Workbook workBook = new XSSFWorkbook();
			Sheet sheet = workBook.createSheet("行为数据");
			// 标题列样式
			CellStyle titleStyle = titleCell(workBook);
			//数据列样式
			CellStyle cellStyle = workBook.createCellStyle();
			cellStyle.setBorderBottom(BorderStyle.THIN); //下边框
			cellStyle.setBorderLeft(BorderStyle.THIN);//左边框
			cellStyle.setBorderTop(BorderStyle.THIN);//上边框
			cellStyle.setBorderRight(BorderStyle.THIN);//右边框
			cellStyle.setAlignment(HorizontalAlignment.CENTER); // 水平居中对齐
			cellStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中对齐

			// 自定义字体颜色, 同单元格样式
			Font font = workBook.createFont();
			font.setFontName("微软雅黑");
			cellStyle.setFont(font);

			//创建第一行标题行
			Row titleRow = sheet.createRow(0);
			//设置列宽列高
			int middleCell = 256 * 20;
			setCellContent(0, titleRow, "行为阶段", titleStyle, middleCell);
			setCellContent(1, titleRow, "调用时间", titleStyle, middleCell);
			setCellContent(2, titleRow, "行为名称", titleStyle, middleCell);
			setCellContent(3, titleRow, "行为权限", titleStyle, middleCell);
			setCellContent(4, titleRow, "主体类型", titleStyle, middleCell);
			setCellContent(5, titleRow, "主体名称", titleStyle, middleCell);
			if(terminalType == TerminalTypeEnum.WECHAT_APPLET || terminalType == TerminalTypeEnum.ALIPAY_APPLET){
				setCellContent(6,titleRow,"Appid",titleStyle,middleCell);
			}else{
				setCellContent(6, titleRow, "包名", titleStyle, middleCell);
			}
			setCellContent(7, titleRow, "详情", titleStyle, middleCell);
			setCellContent(8, titleRow, "函数调用栈", titleStyle, middleCell);
			setCellContent(9,titleRow,"触发频率（次/秒）",titleStyle,middleCell);


			if(!CollectionUtils.isEmpty(lists)){
				for(int k = 0; k < lists.size(); k++){
					//填充数据，从第二行开始,跳过属性列
					Row row = sheet.createRow(k + 1);
					CompliancePrivacyActionNougatVO vo = lists.get(k);
					setCellContent(0, row,vo.getBehaviorStage().getName(), cellStyle, middleCell);
					setCellContent(1, row,DateUtil.format(vo.getActionDate(), "yyyy-MM-dd HH:mm:ss"), cellStyle, middleCell);
					setCellContent(2, row,vo.getActionName(), cellStyle, middleCell);
					setCellContent(3, row,vo.getActionPermission(), cellStyle, middleCell);
					String content = "SDK";
					if(1 == vo.getExecutorType()){
						if(TerminalTypeEnum.WECHAT_APPLET == terminalType){
							content = "微信小程序";
						}else if(TerminalTypeEnum.ALIPAY_APPLET == terminalType) {
							content = "支付宝小程序";
						}else{
							content = "APP";
						}
					}
					setCellContent(4, row,content, cellStyle, middleCell);
					setCellContent(5, row,vo.getExecutor(), cellStyle, middleCell);
					setCellContent(6, row,vo.getPackageName(), cellStyle, middleCell);
					setCellContent(7, row,vo.getDetailsData(), cellStyle, middleCell);
					setCellContent(8, row,vo.getStackInfo(), cellStyle, middleCell);
					setCellContent(9,row,vo.getCounter()==null?"":String.valueOf(vo.getCounter()),cellStyle,middleCell);
				}
			}
			if(null == out){
				// 创建文件输出流，准备输出电子表格：这个必须有，否则你在sheet上做的任何操作都不会有效
				out = Files.newOutputStream(Paths.get(finalXlsxPath));
			}
			workBook.write(out);
			workBook.close();
		} catch (IOException e) {
			LOG.error("生成excel失败",e);
			throw new IjiamiRuntimeException("生成excel失败");
		}finally {
			try {
				if (out != null) {
					out.flush();
					out.close();
				}
			} catch (IOException e) {
				LOG.error("生成excel失败 ", e);
			}
		}
	}
}
