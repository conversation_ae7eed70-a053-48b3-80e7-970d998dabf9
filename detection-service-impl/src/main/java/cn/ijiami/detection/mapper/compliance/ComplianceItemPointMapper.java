package cn.ijiami.detection.mapper.compliance;

import cn.ijiami.detection.entity.compliance.TcomplianceItemPoint;
import cn.ijiami.framework.mybatis.IjiamiMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ComplianceItemPointMapper extends IjiamiMapper<TcomplianceItemPoint> {

    List<TcomplianceItemPoint> findAllByTerminalType(@Param("terminalType") Integer terminalType);

    List<TcomplianceItemPoint> findItemPointStageByName(@Param("itemPointName") String itemPointName,@Param("terminalType") Integer terminalType);
}
