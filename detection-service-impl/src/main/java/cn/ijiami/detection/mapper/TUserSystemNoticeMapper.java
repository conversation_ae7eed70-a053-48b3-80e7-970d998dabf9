package cn.ijiami.detection.mapper;

import cn.ijiami.detection.VO.ManagerSystemNoticeVO;
import cn.ijiami.detection.VO.UserSystemNoticeVO;
import cn.ijiami.detection.entity.TUserSystemNotice;
import cn.ijiami.detection.enums.SystemNoticeReadStatusEnum;
import cn.ijiami.detection.enums.SystemNoticeSendStatusEnum;
import cn.ijiami.detection.enums.SystemNoticeTypeEnum;
import cn.ijiami.detection.enums.SystemNoticeValidityPeriodEnum;
import cn.ijiami.framework.mybatis.IjiamiMapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface TUserSystemNoticeMapper extends IjiamiMapper<TUserSystemNotice> {

    List<UserSystemNoticeVO> findByPage(@Param("userId") Long userId,
                                        @Param("sendStatus") SystemNoticeSendStatusEnum sendStatus,
                                        @Param("readStatus") SystemNoticeReadStatusEnum readStatus);


    List<UserSystemNoticeVO> findGteCreateTime(@Param("userId") Long userId,
                                               @Param("sendStatus") SystemNoticeSendStatusEnum sendStatus,
                                               @Param("createTime") Date  createTime);

}
